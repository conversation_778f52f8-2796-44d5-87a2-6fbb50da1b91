import { Context } from 'hono';

// 统一响应格式，匹配API文档要求
export function success<T>(c: Context, message: string,  data?: T): Response {
  const response = {
    success: true,
    msg: message,
    data: data || null,
  };
  return c.json(response);
}

export function error(c: Context, message: string, code?: string, status: number = 400): Response {
  const response = {
    success: false,
    msg: message,
    data: null,
    code,
  };
  return c.json(response, status as any);
}

