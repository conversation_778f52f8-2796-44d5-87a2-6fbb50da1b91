{"version": 3, "sources": ["wrangler-modules-watch:wrangler:modules-watch", "../../../node_modules/.pnpm/wrangler@4.26.1_@cloudflare+workers-types@4.20250731.0/node_modules/wrangler/templates/modules-watch-stub.js", "../bundle-REFUON/middleware-loader.entry.ts", "../bundle-REFUON/middleware-insertion-facade.js", "../../../src/index.ts", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/index.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/hono.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/hono-base.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/compose.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/context.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/request.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/request/constants.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/body.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/url.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/html.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/constants.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/reg-exp-router/index.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/reg-exp-router/router.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/reg-exp-router/node.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/reg-exp-router/trie.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/smart-router/index.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/smart-router/router.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/trie-router/index.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/trie-router/router.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/trie-router/node.js", "../../../src/middleware/common.ts", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/middleware/cors/index.js", "../../../src/utils/response.ts", "../../../src/routes/auth.ts", "../../../src/utils/auth.ts", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/middleware/jwt/index.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/middleware/jwt/jwt.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/helper/cookie/index.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/cookie.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/http-exception.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/index.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/jwt.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/encode.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/jwa.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/jws.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/helper/adapter/index.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/types.js", "../../../node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/utf8.js", "../../../node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/index.js", "../../../src/routes/admins.ts", "../../../src/middleware/auth.ts", "../../../src/routes/licenses.ts", "../../../src/routes/client.ts", "../../../src/routes/orders.ts", "../../../src/routes/products.ts", "../../../src/routes/statistics.ts", "../../../src/routes/init.ts", "../../../src/utils/init-db.ts", "../../../node_modules/.pnpm/wrangler@4.26.1_@cloudflare+workers-types@4.20250731.0/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../node_modules/.pnpm/wrangler@4.26.1_@cloudflare+workers-types@4.20250731.0/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../../../node_modules/.pnpm/wrangler@4.26.1_@cloudflare+workers-types@4.20250731.0/node_modules/wrangler/templates/middleware/common.ts"], "sourceRoot": "C:\\Users\\<USER>\\code\\verify\\backend\\.wrangler\\tmp\\dev-3h8236", "sourcesContent": ["", "// `esbuild` doesn't support returning `watch*` options from `onStart()`\n// plugin callbacks. Instead, we define an empty virtual module that is\n// imported by this injected file. Importing the module registers watchers.\nimport \"wrangler:modules-watch\";\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"C:\\\\Users\\\\<USER>\\\\code\\\\verify\\\\backend\\\\.wrangler\\\\tmp\\\\bundle-REFUON\\\\middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"C:\\\\Users\\\\<USER>\\\\code\\\\verify\\\\backend\\\\node_modules\\\\.pnpm\\\\wrangler@4.26.1_@cloudflare+workers-types@4.20250731.0\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\common.ts\";\nimport type { WorkerEntrypointConstructor } from \"C:\\\\Users\\\\<USER>\\\\code\\\\verify\\\\backend\\\\.wrangler\\\\tmp\\\\bundle-REFUON\\\\middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"C:\\\\Users\\\\<USER>\\\\code\\\\verify\\\\backend\\\\.wrangler\\\\tmp\\\\bundle-REFUON\\\\middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n", "\t\t\t\timport worker, * as OTHER_EXPORTS from \"C:\\\\Users\\\\<USER>\\\\code\\\\verify\\\\backend\\\\src\\\\index.ts\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"C:\\\\Users\\\\<USER>\\\\code\\\\verify\\\\backend\\\\node_modules\\\\.pnpm\\\\wrangler@4.26.1_@cloudflare+workers-types@4.20250731.0\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"C:\\\\Users\\\\<USER>\\\\code\\\\verify\\\\backend\\\\node_modules\\\\.pnpm\\\\wrangler@4.26.1_@cloudflare+workers-types@4.20250731.0\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"C:\\\\Users\\\\<USER>\\\\code\\\\verify\\\\backend\\\\src\\\\index.ts\";\n\t\t\t\tconst MIDDLEWARE_TEST_INJECT = \"__INJECT_FOR_TESTING_WRANGLER_MIDDLEWARE__\";\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "import { Hono } from 'hono';\nimport { CloudflareBindings } from './types/interfaces';\nimport { corsMiddleware, bindingsMiddleware } from './middleware/common';\nimport { setupAuthRoutes } from './routes/auth';\nimport { setupAdminRoutes } from './routes/admins';\nimport { setupLicenseRoutes } from './routes/licenses';\nimport { setupClientRoutes } from './routes/client';\nimport { setupOrderRoutes } from './routes/orders';\nimport { setupProductRoutes } from './routes/products';\nimport { setupStatisticsRoutes } from './routes/statistics';\nimport { setupInitRoutes } from './routes/init';\n\nconst app = new Hono<{ Bindings: CloudflareBindings }>();\n\n// 全局中间件\napp.use('*', corsMiddleware());\napp.use('*', bindingsMiddleware());\n\n// 健康检查\napp.get('/', (c) => {\n  return c.json({\n    success: true,\n    message: 'License Verification Service API',\n    version: c.env.API_VERSION || 'v1',\n    timestamp: new Date().toISOString(),\n  });\n});\n\n// 设置路由\nsetupAuthRoutes(app);\nsetupAdminRoutes(app);\nsetupLicenseRoutes(app);\nsetupClientRoutes(app);\nsetupOrderRoutes(app);\nsetupProductRoutes(app);\nsetupStatisticsRoutes(app);\nsetupInitRoutes(app);\n\nexport default app;", "// src/index.ts\nimport { Hono } from \"./hono.js\";\nexport {\n  Hono\n};\n", "// src/hono.ts\nimport { HonoBase } from \"./hono-base.js\";\nimport { RegExpRouter } from \"./router/reg-exp-router/index.js\";\nimport { SmartRouter } from \"./router/smart-router/index.js\";\nimport { TrieRouter } from \"./router/trie-router/index.js\";\nvar Hono = class extends HonoBase {\n  constructor(options = {}) {\n    super(options);\n    this.router = options.router ?? new SmartRouter({\n      routers: [new RegExpRouter(), new TrieRouter()]\n    });\n  }\n};\nexport {\n  Hono\n};\n", "// src/hono-base.ts\nimport { compose } from \"./compose.js\";\nimport { Context } from \"./context.js\";\nimport { METHODS, METHOD_NAME_ALL, METHOD_NAME_ALL_LOWERCASE } from \"./router.js\";\nimport { COMPOSED_HANDLER } from \"./utils/constants.js\";\nimport { getPath, getPathNoStrict, mergePath } from \"./utils/url.js\";\nvar notFoundHandler = (c) => {\n  return c.text(\"404 Not Found\", 404);\n};\nvar errorHandler = (err, c) => {\n  if (\"getResponse\" in err) {\n    const res = err.getResponse();\n    return c.newResponse(res.body, res);\n  }\n  console.error(err);\n  return c.text(\"Internal Server Error\", 500);\n};\nvar Hono = class {\n  get;\n  post;\n  put;\n  delete;\n  options;\n  patch;\n  all;\n  on;\n  use;\n  router;\n  getPath;\n  _basePath = \"/\";\n  #path = \"/\";\n  routes = [];\n  constructor(options = {}) {\n    const allMethods = [...METHODS, METHOD_NAME_ALL_LOWERCASE];\n    allMethods.forEach((method) => {\n      this[method] = (args1, ...args) => {\n        if (typeof args1 === \"string\") {\n          this.#path = args1;\n        } else {\n          this.#addRoute(method, this.#path, args1);\n        }\n        args.forEach((handler) => {\n          this.#addRoute(method, this.#path, handler);\n        });\n        return this;\n      };\n    });\n    this.on = (method, path, ...handlers) => {\n      for (const p of [path].flat()) {\n        this.#path = p;\n        for (const m of [method].flat()) {\n          handlers.map((handler) => {\n            this.#addRoute(m.toUpperCase(), this.#path, handler);\n          });\n        }\n      }\n      return this;\n    };\n    this.use = (arg1, ...handlers) => {\n      if (typeof arg1 === \"string\") {\n        this.#path = arg1;\n      } else {\n        this.#path = \"*\";\n        handlers.unshift(arg1);\n      }\n      handlers.forEach((handler) => {\n        this.#addRoute(METHOD_NAME_ALL, this.#path, handler);\n      });\n      return this;\n    };\n    const { strict, ...optionsWithoutStrict } = options;\n    Object.assign(this, optionsWithoutStrict);\n    this.getPath = strict ?? true ? options.getPath ?? getPath : getPathNoStrict;\n  }\n  #clone() {\n    const clone = new Hono({\n      router: this.router,\n      getPath: this.getPath\n    });\n    clone.errorHandler = this.errorHandler;\n    clone.#notFoundHandler = this.#notFoundHandler;\n    clone.routes = this.routes;\n    return clone;\n  }\n  #notFoundHandler = notFoundHandler;\n  errorHandler = errorHandler;\n  route(path, app) {\n    const subApp = this.basePath(path);\n    app.routes.map((r) => {\n      let handler;\n      if (app.errorHandler === errorHandler) {\n        handler = r.handler;\n      } else {\n        handler = async (c, next) => (await compose([], app.errorHandler)(c, () => r.handler(c, next))).res;\n        handler[COMPOSED_HANDLER] = r.handler;\n      }\n      subApp.#addRoute(r.method, r.path, handler);\n    });\n    return this;\n  }\n  basePath(path) {\n    const subApp = this.#clone();\n    subApp._basePath = mergePath(this._basePath, path);\n    return subApp;\n  }\n  onError = (handler) => {\n    this.errorHandler = handler;\n    return this;\n  };\n  notFound = (handler) => {\n    this.#notFoundHandler = handler;\n    return this;\n  };\n  mount(path, applicationHandler, options) {\n    let replaceRequest;\n    let optionHandler;\n    if (options) {\n      if (typeof options === \"function\") {\n        optionHandler = options;\n      } else {\n        optionHandler = options.optionHandler;\n        if (options.replaceRequest === false) {\n          replaceRequest = (request) => request;\n        } else {\n          replaceRequest = options.replaceRequest;\n        }\n      }\n    }\n    const getOptions = optionHandler ? (c) => {\n      const options2 = optionHandler(c);\n      return Array.isArray(options2) ? options2 : [options2];\n    } : (c) => {\n      let executionContext = void 0;\n      try {\n        executionContext = c.executionCtx;\n      } catch {\n      }\n      return [c.env, executionContext];\n    };\n    replaceRequest ||= (() => {\n      const mergedPath = mergePath(this._basePath, path);\n      const pathPrefixLength = mergedPath === \"/\" ? 0 : mergedPath.length;\n      return (request) => {\n        const url = new URL(request.url);\n        url.pathname = url.pathname.slice(pathPrefixLength) || \"/\";\n        return new Request(url, request);\n      };\n    })();\n    const handler = async (c, next) => {\n      const res = await applicationHandler(replaceRequest(c.req.raw), ...getOptions(c));\n      if (res) {\n        return res;\n      }\n      await next();\n    };\n    this.#addRoute(METHOD_NAME_ALL, mergePath(path, \"*\"), handler);\n    return this;\n  }\n  #addRoute(method, path, handler) {\n    method = method.toUpperCase();\n    path = mergePath(this._basePath, path);\n    const r = { basePath: this._basePath, path, method, handler };\n    this.router.add(method, path, [handler, r]);\n    this.routes.push(r);\n  }\n  #handleError(err, c) {\n    if (err instanceof Error) {\n      return this.errorHandler(err, c);\n    }\n    throw err;\n  }\n  #dispatch(request, executionCtx, env, method) {\n    if (method === \"HEAD\") {\n      return (async () => new Response(null, await this.#dispatch(request, executionCtx, env, \"GET\")))();\n    }\n    const path = this.getPath(request, { env });\n    const matchResult = this.router.match(method, path);\n    const c = new Context(request, {\n      path,\n      matchResult,\n      env,\n      executionCtx,\n      notFoundHandler: this.#notFoundHandler\n    });\n    if (matchResult[0].length === 1) {\n      let res;\n      try {\n        res = matchResult[0][0][0][0](c, async () => {\n          c.res = await this.#notFoundHandler(c);\n        });\n      } catch (err) {\n        return this.#handleError(err, c);\n      }\n      return res instanceof Promise ? res.then(\n        (resolved) => resolved || (c.finalized ? c.res : this.#notFoundHandler(c))\n      ).catch((err) => this.#handleError(err, c)) : res ?? this.#notFoundHandler(c);\n    }\n    const composed = compose(matchResult[0], this.errorHandler, this.#notFoundHandler);\n    return (async () => {\n      try {\n        const context = await composed(c);\n        if (!context.finalized) {\n          throw new Error(\n            \"Context is not finalized. Did you forget to return a Response object or `await next()`?\"\n          );\n        }\n        return context.res;\n      } catch (err) {\n        return this.#handleError(err, c);\n      }\n    })();\n  }\n  fetch = (request, ...rest) => {\n    return this.#dispatch(request, rest[1], rest[0], request.method);\n  };\n  request = (input, requestInit, Env, executionCtx) => {\n    if (input instanceof Request) {\n      return this.fetch(requestInit ? new Request(input, requestInit) : input, Env, executionCtx);\n    }\n    input = input.toString();\n    return this.fetch(\n      new Request(\n        /^https?:\\/\\//.test(input) ? input : `http://localhost${mergePath(\"/\", input)}`,\n        requestInit\n      ),\n      Env,\n      executionCtx\n    );\n  };\n  fire = () => {\n    addEventListener(\"fetch\", (event) => {\n      event.respondWith(this.#dispatch(event.request, event, void 0, event.request.method));\n    });\n  };\n};\nexport {\n  Hono as HonoBase\n};\n", "// src/compose.ts\nvar compose = (middleware, onError, onNotFound) => {\n  return (context, next) => {\n    let index = -1;\n    return dispatch(0);\n    async function dispatch(i) {\n      if (i <= index) {\n        throw new Error(\"next() called multiple times\");\n      }\n      index = i;\n      let res;\n      let isError = false;\n      let handler;\n      if (middleware[i]) {\n        handler = middleware[i][0][0];\n        context.req.routeIndex = i;\n      } else {\n        handler = i === middleware.length && next || void 0;\n      }\n      if (handler) {\n        try {\n          res = await handler(context, () => dispatch(i + 1));\n        } catch (err) {\n          if (err instanceof Error && onError) {\n            context.error = err;\n            res = await onError(err, context);\n            isError = true;\n          } else {\n            throw err;\n          }\n        }\n      } else {\n        if (context.finalized === false && onNotFound) {\n          res = await onNotFound(context);\n        }\n      }\n      if (res && (context.finalized === false || isError)) {\n        context.res = res;\n      }\n      return context;\n    }\n  };\n};\nexport {\n  compose\n};\n", "// src/context.ts\nimport { HonoRequest } from \"./request.js\";\nimport { HtmlEscapedCallbackPhase, resolveCallback } from \"./utils/html.js\";\nvar TEXT_PLAIN = \"text/plain; charset=UTF-8\";\nvar setDefaultContentType = (contentType, headers) => {\n  return {\n    \"Content-Type\": contentType,\n    ...headers\n  };\n};\nvar Context = class {\n  #rawRequest;\n  #req;\n  env = {};\n  #var;\n  finalized = false;\n  error;\n  #status;\n  #executionCtx;\n  #res;\n  #layout;\n  #renderer;\n  #notFoundHandler;\n  #preparedHeaders;\n  #matchResult;\n  #path;\n  constructor(req, options) {\n    this.#rawRequest = req;\n    if (options) {\n      this.#executionCtx = options.executionCtx;\n      this.env = options.env;\n      this.#notFoundHandler = options.notFoundHandler;\n      this.#path = options.path;\n      this.#matchResult = options.matchResult;\n    }\n  }\n  get req() {\n    this.#req ??= new HonoRequest(this.#rawRequest, this.#path, this.#matchResult);\n    return this.#req;\n  }\n  get event() {\n    if (this.#executionCtx && \"respondWith\" in this.#executionCtx) {\n      return this.#executionCtx;\n    } else {\n      throw Error(\"This context has no FetchEvent\");\n    }\n  }\n  get executionCtx() {\n    if (this.#executionCtx) {\n      return this.#executionCtx;\n    } else {\n      throw Error(\"This context has no ExecutionContext\");\n    }\n  }\n  get res() {\n    return this.#res ||= new Response(null, {\n      headers: this.#preparedHeaders ??= new Headers()\n    });\n  }\n  set res(_res) {\n    if (this.#res && _res) {\n      _res = new Response(_res.body, _res);\n      for (const [k, v] of this.#res.headers.entries()) {\n        if (k === \"content-type\") {\n          continue;\n        }\n        if (k === \"set-cookie\") {\n          const cookies = this.#res.headers.getSetCookie();\n          _res.headers.delete(\"set-cookie\");\n          for (const cookie of cookies) {\n            _res.headers.append(\"set-cookie\", cookie);\n          }\n        } else {\n          _res.headers.set(k, v);\n        }\n      }\n    }\n    this.#res = _res;\n    this.finalized = true;\n  }\n  render = (...args) => {\n    this.#renderer ??= (content) => this.html(content);\n    return this.#renderer(...args);\n  };\n  setLayout = (layout) => this.#layout = layout;\n  getLayout = () => this.#layout;\n  setRenderer = (renderer) => {\n    this.#renderer = renderer;\n  };\n  header = (name, value, options) => {\n    if (this.finalized) {\n      this.#res = new Response(this.#res.body, this.#res);\n    }\n    const headers = this.#res ? this.#res.headers : this.#preparedHeaders ??= new Headers();\n    if (value === void 0) {\n      headers.delete(name);\n    } else if (options?.append) {\n      headers.append(name, value);\n    } else {\n      headers.set(name, value);\n    }\n  };\n  status = (status) => {\n    this.#status = status;\n  };\n  set = (key, value) => {\n    this.#var ??= /* @__PURE__ */ new Map();\n    this.#var.set(key, value);\n  };\n  get = (key) => {\n    return this.#var ? this.#var.get(key) : void 0;\n  };\n  get var() {\n    if (!this.#var) {\n      return {};\n    }\n    return Object.fromEntries(this.#var);\n  }\n  #newResponse(data, arg, headers) {\n    const responseHeaders = this.#res ? new Headers(this.#res.headers) : this.#preparedHeaders ?? new Headers();\n    if (typeof arg === \"object\" && \"headers\" in arg) {\n      const argHeaders = arg.headers instanceof Headers ? arg.headers : new Headers(arg.headers);\n      for (const [key, value] of argHeaders) {\n        if (key.toLowerCase() === \"set-cookie\") {\n          responseHeaders.append(key, value);\n        } else {\n          responseHeaders.set(key, value);\n        }\n      }\n    }\n    if (headers) {\n      for (const [k, v] of Object.entries(headers)) {\n        if (typeof v === \"string\") {\n          responseHeaders.set(k, v);\n        } else {\n          responseHeaders.delete(k);\n          for (const v2 of v) {\n            responseHeaders.append(k, v2);\n          }\n        }\n      }\n    }\n    const status = typeof arg === \"number\" ? arg : arg?.status ?? this.#status;\n    return new Response(data, { status, headers: responseHeaders });\n  }\n  newResponse = (...args) => this.#newResponse(...args);\n  body = (data, arg, headers) => this.#newResponse(data, arg, headers);\n  text = (text, arg, headers) => {\n    return !this.#preparedHeaders && !this.#status && !arg && !headers && !this.finalized ? new Response(text) : this.#newResponse(\n      text,\n      arg,\n      setDefaultContentType(TEXT_PLAIN, headers)\n    );\n  };\n  json = (object, arg, headers) => {\n    return this.#newResponse(\n      JSON.stringify(object),\n      arg,\n      setDefaultContentType(\"application/json\", headers)\n    );\n  };\n  html = (html, arg, headers) => {\n    const res = (html2) => this.#newResponse(html2, arg, setDefaultContentType(\"text/html; charset=UTF-8\", headers));\n    return typeof html === \"object\" ? resolveCallback(html, HtmlEscapedCallbackPhase.Stringify, false, {}).then(res) : res(html);\n  };\n  redirect = (location, status) => {\n    const locationString = String(location);\n    this.header(\n      \"Location\",\n      !/[^\\x00-\\xFF]/.test(locationString) ? locationString : encodeURI(locationString)\n    );\n    return this.newResponse(null, status ?? 302);\n  };\n  notFound = () => {\n    this.#notFoundHandler ??= () => new Response();\n    return this.#notFoundHandler(this);\n  };\n};\nexport {\n  Context,\n  TEXT_PLAIN\n};\n", "// src/request.ts\nimport { GET_MATCH_RESULT } from \"./request/constants.js\";\nimport { parseBody } from \"./utils/body.js\";\nimport { decodeURIComponent_, getQueryParam, getQueryParams, tryDecode } from \"./utils/url.js\";\nvar tryDecodeURIComponent = (str) => tryDecode(str, decodeURIComponent_);\nvar HonoRequest = class {\n  raw;\n  #validatedData;\n  #matchResult;\n  routeIndex = 0;\n  path;\n  bodyCache = {};\n  constructor(request, path = \"/\", matchResult = [[]]) {\n    this.raw = request;\n    this.path = path;\n    this.#matchResult = matchResult;\n    this.#validatedData = {};\n  }\n  param(key) {\n    return key ? this.#getDecodedParam(key) : this.#getAllDecodedParams();\n  }\n  #getDecodedParam(key) {\n    const paramKey = this.#matchResult[0][this.routeIndex][1][key];\n    const param = this.#getParamValue(paramKey);\n    return param ? /\\%/.test(param) ? tryDecodeURIComponent(param) : param : void 0;\n  }\n  #getAllDecodedParams() {\n    const decoded = {};\n    const keys = Object.keys(this.#matchResult[0][this.routeIndex][1]);\n    for (const key of keys) {\n      const value = this.#getParamValue(this.#matchResult[0][this.routeIndex][1][key]);\n      if (value && typeof value === \"string\") {\n        decoded[key] = /\\%/.test(value) ? tryDecodeURIComponent(value) : value;\n      }\n    }\n    return decoded;\n  }\n  #getParamValue(paramKey) {\n    return this.#matchResult[1] ? this.#matchResult[1][paramKey] : paramKey;\n  }\n  query(key) {\n    return getQueryParam(this.url, key);\n  }\n  queries(key) {\n    return getQueryParams(this.url, key);\n  }\n  header(name) {\n    if (name) {\n      return this.raw.headers.get(name) ?? void 0;\n    }\n    const headerData = {};\n    this.raw.headers.forEach((value, key) => {\n      headerData[key] = value;\n    });\n    return headerData;\n  }\n  async parseBody(options) {\n    return this.bodyCache.parsedBody ??= await parseBody(this, options);\n  }\n  #cachedBody = (key) => {\n    const { bodyCache, raw } = this;\n    const cachedBody = bodyCache[key];\n    if (cachedBody) {\n      return cachedBody;\n    }\n    const anyCachedKey = Object.keys(bodyCache)[0];\n    if (anyCachedKey) {\n      return bodyCache[anyCachedKey].then((body) => {\n        if (anyCachedKey === \"json\") {\n          body = JSON.stringify(body);\n        }\n        return new Response(body)[key]();\n      });\n    }\n    return bodyCache[key] = raw[key]();\n  };\n  json() {\n    return this.#cachedBody(\"text\").then((text) => JSON.parse(text));\n  }\n  text() {\n    return this.#cachedBody(\"text\");\n  }\n  arrayBuffer() {\n    return this.#cachedBody(\"arrayBuffer\");\n  }\n  blob() {\n    return this.#cachedBody(\"blob\");\n  }\n  formData() {\n    return this.#cachedBody(\"formData\");\n  }\n  addValidatedData(target, data) {\n    this.#validatedData[target] = data;\n  }\n  valid(target) {\n    return this.#validatedData[target];\n  }\n  get url() {\n    return this.raw.url;\n  }\n  get method() {\n    return this.raw.method;\n  }\n  get [GET_MATCH_RESULT]() {\n    return this.#matchResult;\n  }\n  get matchedRoutes() {\n    return this.#matchResult[0].map(([[, route]]) => route);\n  }\n  get routePath() {\n    return this.#matchResult[0].map(([[, route]]) => route)[this.routeIndex].path;\n  }\n};\nexport {\n  HonoRequest\n};\n", "// src/request/constants.ts\nvar GET_MATCH_RESULT = Symbol();\nexport {\n  GET_MATCH_RESULT\n};\n", "// src/utils/body.ts\nimport { HonoRequest } from \"../request.js\";\nvar parseBody = async (request, options = /* @__PURE__ */ Object.create(null)) => {\n  const { all = false, dot = false } = options;\n  const headers = request instanceof HonoRequest ? request.raw.headers : request.headers;\n  const contentType = headers.get(\"Content-Type\");\n  if (contentType?.startsWith(\"multipart/form-data\") || contentType?.startsWith(\"application/x-www-form-urlencoded\")) {\n    return parseFormData(request, { all, dot });\n  }\n  return {};\n};\nasync function parseFormData(request, options) {\n  const formData = await request.formData();\n  if (formData) {\n    return convertFormDataToBodyData(formData, options);\n  }\n  return {};\n}\nfunction convertFormDataToBodyData(formData, options) {\n  const form = /* @__PURE__ */ Object.create(null);\n  formData.forEach((value, key) => {\n    const shouldParseAllValues = options.all || key.endsWith(\"[]\");\n    if (!shouldParseAllValues) {\n      form[key] = value;\n    } else {\n      handleParsingAllValues(form, key, value);\n    }\n  });\n  if (options.dot) {\n    Object.entries(form).forEach(([key, value]) => {\n      const shouldParseDotValues = key.includes(\".\");\n      if (shouldParseDotValues) {\n        handleParsingNestedValues(form, key, value);\n        delete form[key];\n      }\n    });\n  }\n  return form;\n}\nvar handleParsingAllValues = (form, key, value) => {\n  if (form[key] !== void 0) {\n    if (Array.isArray(form[key])) {\n      ;\n      form[key].push(value);\n    } else {\n      form[key] = [form[key], value];\n    }\n  } else {\n    if (!key.endsWith(\"[]\")) {\n      form[key] = value;\n    } else {\n      form[key] = [value];\n    }\n  }\n};\nvar handleParsingNestedValues = (form, key, value) => {\n  let nestedForm = form;\n  const keys = key.split(\".\");\n  keys.forEach((key2, index) => {\n    if (index === keys.length - 1) {\n      nestedForm[key2] = value;\n    } else {\n      if (!nestedForm[key2] || typeof nestedForm[key2] !== \"object\" || Array.isArray(nestedForm[key2]) || nestedForm[key2] instanceof File) {\n        nestedForm[key2] = /* @__PURE__ */ Object.create(null);\n      }\n      nestedForm = nestedForm[key2];\n    }\n  });\n};\nexport {\n  parseBody\n};\n", "// src/utils/url.ts\nvar splitPath = (path) => {\n  const paths = path.split(\"/\");\n  if (paths[0] === \"\") {\n    paths.shift();\n  }\n  return paths;\n};\nvar splitRoutingPath = (routePath) => {\n  const { groups, path } = extractGroupsFromPath(routePath);\n  const paths = splitPath(path);\n  return replaceGroupMarks(paths, groups);\n};\nvar extractGroupsFromPath = (path) => {\n  const groups = [];\n  path = path.replace(/\\{[^}]+\\}/g, (match, index) => {\n    const mark = `@${index}`;\n    groups.push([mark, match]);\n    return mark;\n  });\n  return { groups, path };\n};\nvar replaceGroupMarks = (paths, groups) => {\n  for (let i = groups.length - 1; i >= 0; i--) {\n    const [mark] = groups[i];\n    for (let j = paths.length - 1; j >= 0; j--) {\n      if (paths[j].includes(mark)) {\n        paths[j] = paths[j].replace(mark, groups[i][1]);\n        break;\n      }\n    }\n  }\n  return paths;\n};\nvar patternCache = {};\nvar getPattern = (label, next) => {\n  if (label === \"*\") {\n    return \"*\";\n  }\n  const match = label.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n  if (match) {\n    const cacheKey = `${label}#${next}`;\n    if (!patternCache[cacheKey]) {\n      if (match[2]) {\n        patternCache[cacheKey] = next && next[0] !== \":\" && next[0] !== \"*\" ? [cacheKey, match[1], new RegExp(`^${match[2]}(?=/${next})`)] : [label, match[1], new RegExp(`^${match[2]}$`)];\n      } else {\n        patternCache[cacheKey] = [label, match[1], true];\n      }\n    }\n    return patternCache[cacheKey];\n  }\n  return null;\n};\nvar tryDecode = (str, decoder) => {\n  try {\n    return decoder(str);\n  } catch {\n    return str.replace(/(?:%[0-9A-Fa-f]{2})+/g, (match) => {\n      try {\n        return decoder(match);\n      } catch {\n        return match;\n      }\n    });\n  }\n};\nvar tryDecodeURI = (str) => tryDecode(str, decodeURI);\nvar getPath = (request) => {\n  const url = request.url;\n  const start = url.indexOf(\n    \"/\",\n    url.charCodeAt(9) === 58 ? 13 : 8\n  );\n  let i = start;\n  for (; i < url.length; i++) {\n    const charCode = url.charCodeAt(i);\n    if (charCode === 37) {\n      const queryIndex = url.indexOf(\"?\", i);\n      const path = url.slice(start, queryIndex === -1 ? void 0 : queryIndex);\n      return tryDecodeURI(path.includes(\"%25\") ? path.replace(/%25/g, \"%2525\") : path);\n    } else if (charCode === 63) {\n      break;\n    }\n  }\n  return url.slice(start, i);\n};\nvar getQueryStrings = (url) => {\n  const queryIndex = url.indexOf(\"?\", 8);\n  return queryIndex === -1 ? \"\" : \"?\" + url.slice(queryIndex + 1);\n};\nvar getPathNoStrict = (request) => {\n  const result = getPath(request);\n  return result.length > 1 && result.at(-1) === \"/\" ? result.slice(0, -1) : result;\n};\nvar mergePath = (base, sub, ...rest) => {\n  if (rest.length) {\n    sub = mergePath(sub, ...rest);\n  }\n  return `${base?.[0] === \"/\" ? \"\" : \"/\"}${base}${sub === \"/\" ? \"\" : `${base?.at(-1) === \"/\" ? \"\" : \"/\"}${sub?.[0] === \"/\" ? sub.slice(1) : sub}`}`;\n};\nvar checkOptionalParameter = (path) => {\n  if (path.charCodeAt(path.length - 1) !== 63 || !path.includes(\":\")) {\n    return null;\n  }\n  const segments = path.split(\"/\");\n  const results = [];\n  let basePath = \"\";\n  segments.forEach((segment) => {\n    if (segment !== \"\" && !/\\:/.test(segment)) {\n      basePath += \"/\" + segment;\n    } else if (/\\:/.test(segment)) {\n      if (/\\?/.test(segment)) {\n        if (results.length === 0 && basePath === \"\") {\n          results.push(\"/\");\n        } else {\n          results.push(basePath);\n        }\n        const optionalSegment = segment.replace(\"?\", \"\");\n        basePath += \"/\" + optionalSegment;\n        results.push(basePath);\n      } else {\n        basePath += \"/\" + segment;\n      }\n    }\n  });\n  return results.filter((v, i, a) => a.indexOf(v) === i);\n};\nvar _decodeURI = (value) => {\n  if (!/[%+]/.test(value)) {\n    return value;\n  }\n  if (value.indexOf(\"+\") !== -1) {\n    value = value.replace(/\\+/g, \" \");\n  }\n  return value.indexOf(\"%\") !== -1 ? tryDecode(value, decodeURIComponent_) : value;\n};\nvar _getQueryParam = (url, key, multiple) => {\n  let encoded;\n  if (!multiple && key && !/[%+]/.test(key)) {\n    let keyIndex2 = url.indexOf(`?${key}`, 8);\n    if (keyIndex2 === -1) {\n      keyIndex2 = url.indexOf(`&${key}`, 8);\n    }\n    while (keyIndex2 !== -1) {\n      const trailingKeyCode = url.charCodeAt(keyIndex2 + key.length + 1);\n      if (trailingKeyCode === 61) {\n        const valueIndex = keyIndex2 + key.length + 2;\n        const endIndex = url.indexOf(\"&\", valueIndex);\n        return _decodeURI(url.slice(valueIndex, endIndex === -1 ? void 0 : endIndex));\n      } else if (trailingKeyCode == 38 || isNaN(trailingKeyCode)) {\n        return \"\";\n      }\n      keyIndex2 = url.indexOf(`&${key}`, keyIndex2 + 1);\n    }\n    encoded = /[%+]/.test(url);\n    if (!encoded) {\n      return void 0;\n    }\n  }\n  const results = {};\n  encoded ??= /[%+]/.test(url);\n  let keyIndex = url.indexOf(\"?\", 8);\n  while (keyIndex !== -1) {\n    const nextKeyIndex = url.indexOf(\"&\", keyIndex + 1);\n    let valueIndex = url.indexOf(\"=\", keyIndex);\n    if (valueIndex > nextKeyIndex && nextKeyIndex !== -1) {\n      valueIndex = -1;\n    }\n    let name = url.slice(\n      keyIndex + 1,\n      valueIndex === -1 ? nextKeyIndex === -1 ? void 0 : nextKeyIndex : valueIndex\n    );\n    if (encoded) {\n      name = _decodeURI(name);\n    }\n    keyIndex = nextKeyIndex;\n    if (name === \"\") {\n      continue;\n    }\n    let value;\n    if (valueIndex === -1) {\n      value = \"\";\n    } else {\n      value = url.slice(valueIndex + 1, nextKeyIndex === -1 ? void 0 : nextKeyIndex);\n      if (encoded) {\n        value = _decodeURI(value);\n      }\n    }\n    if (multiple) {\n      if (!(results[name] && Array.isArray(results[name]))) {\n        results[name] = [];\n      }\n      ;\n      results[name].push(value);\n    } else {\n      results[name] ??= value;\n    }\n  }\n  return key ? results[key] : results;\n};\nvar getQueryParam = _getQueryParam;\nvar getQueryParams = (url, key) => {\n  return _getQueryParam(url, key, true);\n};\nvar decodeURIComponent_ = decodeURIComponent;\nexport {\n  checkOptionalParameter,\n  decodeURIComponent_,\n  getPath,\n  getPathNoStrict,\n  getPattern,\n  getQueryParam,\n  getQueryParams,\n  getQueryStrings,\n  mergePath,\n  splitPath,\n  splitRoutingPath,\n  tryDecode\n};\n", "// src/utils/html.ts\nvar HtmlEscapedCallbackPhase = {\n  Stringify: 1,\n  BeforeStream: 2,\n  Stream: 3\n};\nvar raw = (value, callbacks) => {\n  const escapedString = new String(value);\n  escapedString.isEscaped = true;\n  escapedString.callbacks = callbacks;\n  return escapedString;\n};\nvar escapeRe = /[&<>'\"]/;\nvar stringBufferToString = async (buffer, callbacks) => {\n  let str = \"\";\n  callbacks ||= [];\n  const resolvedBuffer = await Promise.all(buffer);\n  for (let i = resolvedBuffer.length - 1; ; i--) {\n    str += resolvedBuffer[i];\n    i--;\n    if (i < 0) {\n      break;\n    }\n    let r = resolvedBuffer[i];\n    if (typeof r === \"object\") {\n      callbacks.push(...r.callbacks || []);\n    }\n    const isEscaped = r.isEscaped;\n    r = await (typeof r === \"object\" ? r.toString() : r);\n    if (typeof r === \"object\") {\n      callbacks.push(...r.callbacks || []);\n    }\n    if (r.isEscaped ?? isEscaped) {\n      str += r;\n    } else {\n      const buf = [str];\n      escapeToBuffer(r, buf);\n      str = buf[0];\n    }\n  }\n  return raw(str, callbacks);\n};\nvar escapeToBuffer = (str, buffer) => {\n  const match = str.search(escapeRe);\n  if (match === -1) {\n    buffer[0] += str;\n    return;\n  }\n  let escape;\n  let index;\n  let lastIndex = 0;\n  for (index = match; index < str.length; index++) {\n    switch (str.charCodeAt(index)) {\n      case 34:\n        escape = \"&quot;\";\n        break;\n      case 39:\n        escape = \"&#39;\";\n        break;\n      case 38:\n        escape = \"&amp;\";\n        break;\n      case 60:\n        escape = \"&lt;\";\n        break;\n      case 62:\n        escape = \"&gt;\";\n        break;\n      default:\n        continue;\n    }\n    buffer[0] += str.substring(lastIndex, index) + escape;\n    lastIndex = index + 1;\n  }\n  buffer[0] += str.substring(lastIndex, index);\n};\nvar resolveCallbackSync = (str) => {\n  const callbacks = str.callbacks;\n  if (!callbacks?.length) {\n    return str;\n  }\n  const buffer = [str];\n  const context = {};\n  callbacks.forEach((c) => c({ phase: HtmlEscapedCallbackPhase.Stringify, buffer, context }));\n  return buffer[0];\n};\nvar resolveCallback = async (str, phase, preserveCallbacks, context, buffer) => {\n  if (typeof str === \"object\" && !(str instanceof String)) {\n    if (!(str instanceof Promise)) {\n      str = str.toString();\n    }\n    if (str instanceof Promise) {\n      str = await str;\n    }\n  }\n  const callbacks = str.callbacks;\n  if (!callbacks?.length) {\n    return Promise.resolve(str);\n  }\n  if (buffer) {\n    buffer[0] += str;\n  } else {\n    buffer = [str];\n  }\n  const resStr = Promise.all(callbacks.map((c) => c({ phase, buffer, context }))).then(\n    (res) => Promise.all(\n      res.filter(Boolean).map((str2) => resolveCallback(str2, phase, false, context, buffer))\n    ).then(() => buffer[0])\n  );\n  if (preserveCallbacks) {\n    return raw(await resStr, callbacks);\n  } else {\n    return resStr;\n  }\n};\nexport {\n  HtmlEscapedCallbackPhase,\n  escapeToBuffer,\n  raw,\n  resolveCallback,\n  resolveCallbackSync,\n  stringBufferToString\n};\n", "// src/router.ts\nvar METHOD_NAME_ALL = \"ALL\";\nvar METHOD_NAME_ALL_LOWERCASE = \"all\";\nvar METHODS = [\"get\", \"post\", \"put\", \"delete\", \"options\", \"patch\"];\nvar MESSAGE_MATCHER_IS_ALREADY_BUILT = \"Can not add a route since the matcher is already built.\";\nvar UnsupportedPathError = class extends Error {\n};\nexport {\n  MESSAGE_MATCHER_IS_ALREADY_BUILT,\n  METHODS,\n  METHOD_NAME_ALL,\n  METHOD_NAME_ALL_LOWERCASE,\n  UnsupportedPathError\n};\n", "// src/utils/constants.ts\nvar COMPOSED_HANDLER = \"__COMPOSED_HANDLER\";\nexport {\n  COMPOSED_HANDLER\n};\n", "// src/router/reg-exp-router/index.ts\nimport { RegExpRouter } from \"./router.js\";\nexport {\n  RegExpRouter\n};\n", "// src/router/reg-exp-router/router.ts\nimport {\n  MESSAGE_MATCHER_IS_ALREADY_BUILT,\n  METHOD_NAME_ALL,\n  UnsupportedPathError\n} from \"../../router.js\";\nimport { checkOptionalParameter } from \"../../utils/url.js\";\nimport { PATH_ERROR } from \"./node.js\";\nimport { Trie } from \"./trie.js\";\nvar emptyParam = [];\nvar nullMatcher = [/^$/, [], /* @__PURE__ */ Object.create(null)];\nvar wildcardRegExpCache = /* @__PURE__ */ Object.create(null);\nfunction buildWildcardRegExp(path) {\n  return wildcardRegExpCache[path] ??= new RegExp(\n    path === \"*\" ? \"\" : `^${path.replace(\n      /\\/\\*$|([.\\\\+*[^\\]$()])/g,\n      (_, metaChar) => metaChar ? `\\\\${metaChar}` : \"(?:|/.*)\"\n    )}$`\n  );\n}\nfunction clearWildcardRegExpCache() {\n  wildcardRegExpCache = /* @__PURE__ */ Object.create(null);\n}\nfunction buildMatcherFromPreprocessedRoutes(routes) {\n  const trie = new Trie();\n  const handlerData = [];\n  if (routes.length === 0) {\n    return nullMatcher;\n  }\n  const routesWithStaticPathFlag = routes.map(\n    (route) => [!/\\*|\\/:/.test(route[0]), ...route]\n  ).sort(\n    ([isStaticA, pathA], [isStaticB, pathB]) => isStaticA ? 1 : isStaticB ? -1 : pathA.length - pathB.length\n  );\n  const staticMap = /* @__PURE__ */ Object.create(null);\n  for (let i = 0, j = -1, len = routesWithStaticPathFlag.length; i < len; i++) {\n    const [pathErrorCheckOnly, path, handlers] = routesWithStaticPathFlag[i];\n    if (pathErrorCheckOnly) {\n      staticMap[path] = [handlers.map(([h]) => [h, /* @__PURE__ */ Object.create(null)]), emptyParam];\n    } else {\n      j++;\n    }\n    let paramAssoc;\n    try {\n      paramAssoc = trie.insert(path, j, pathErrorCheckOnly);\n    } catch (e) {\n      throw e === PATH_ERROR ? new UnsupportedPathError(path) : e;\n    }\n    if (pathErrorCheckOnly) {\n      continue;\n    }\n    handlerData[j] = handlers.map(([h, paramCount]) => {\n      const paramIndexMap = /* @__PURE__ */ Object.create(null);\n      paramCount -= 1;\n      for (; paramCount >= 0; paramCount--) {\n        const [key, value] = paramAssoc[paramCount];\n        paramIndexMap[key] = value;\n      }\n      return [h, paramIndexMap];\n    });\n  }\n  const [regexp, indexReplacementMap, paramReplacementMap] = trie.buildRegExp();\n  for (let i = 0, len = handlerData.length; i < len; i++) {\n    for (let j = 0, len2 = handlerData[i].length; j < len2; j++) {\n      const map = handlerData[i][j]?.[1];\n      if (!map) {\n        continue;\n      }\n      const keys = Object.keys(map);\n      for (let k = 0, len3 = keys.length; k < len3; k++) {\n        map[keys[k]] = paramReplacementMap[map[keys[k]]];\n      }\n    }\n  }\n  const handlerMap = [];\n  for (const i in indexReplacementMap) {\n    handlerMap[i] = handlerData[indexReplacementMap[i]];\n  }\n  return [regexp, handlerMap, staticMap];\n}\nfunction findMiddleware(middleware, path) {\n  if (!middleware) {\n    return void 0;\n  }\n  for (const k of Object.keys(middleware).sort((a, b) => b.length - a.length)) {\n    if (buildWildcardRegExp(k).test(path)) {\n      return [...middleware[k]];\n    }\n  }\n  return void 0;\n}\nvar RegExpRouter = class {\n  name = \"RegExpRouter\";\n  #middleware;\n  #routes;\n  constructor() {\n    this.#middleware = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };\n    this.#routes = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };\n  }\n  add(method, path, handler) {\n    const middleware = this.#middleware;\n    const routes = this.#routes;\n    if (!middleware || !routes) {\n      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);\n    }\n    if (!middleware[method]) {\n      ;\n      [middleware, routes].forEach((handlerMap) => {\n        handlerMap[method] = /* @__PURE__ */ Object.create(null);\n        Object.keys(handlerMap[METHOD_NAME_ALL]).forEach((p) => {\n          handlerMap[method][p] = [...handlerMap[METHOD_NAME_ALL][p]];\n        });\n      });\n    }\n    if (path === \"/*\") {\n      path = \"*\";\n    }\n    const paramCount = (path.match(/\\/:/g) || []).length;\n    if (/\\*$/.test(path)) {\n      const re = buildWildcardRegExp(path);\n      if (method === METHOD_NAME_ALL) {\n        Object.keys(middleware).forEach((m) => {\n          middleware[m][path] ||= findMiddleware(middleware[m], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];\n        });\n      } else {\n        middleware[method][path] ||= findMiddleware(middleware[method], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];\n      }\n      Object.keys(middleware).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          Object.keys(middleware[m]).forEach((p) => {\n            re.test(p) && middleware[m][p].push([handler, paramCount]);\n          });\n        }\n      });\n      Object.keys(routes).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          Object.keys(routes[m]).forEach(\n            (p) => re.test(p) && routes[m][p].push([handler, paramCount])\n          );\n        }\n      });\n      return;\n    }\n    const paths = checkOptionalParameter(path) || [path];\n    for (let i = 0, len = paths.length; i < len; i++) {\n      const path2 = paths[i];\n      Object.keys(routes).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          routes[m][path2] ||= [\n            ...findMiddleware(middleware[m], path2) || findMiddleware(middleware[METHOD_NAME_ALL], path2) || []\n          ];\n          routes[m][path2].push([handler, paramCount - len + i + 1]);\n        }\n      });\n    }\n  }\n  match(method, path) {\n    clearWildcardRegExpCache();\n    const matchers = this.#buildAllMatchers();\n    this.match = (method2, path2) => {\n      const matcher = matchers[method2] || matchers[METHOD_NAME_ALL];\n      const staticMatch = matcher[2][path2];\n      if (staticMatch) {\n        return staticMatch;\n      }\n      const match = path2.match(matcher[0]);\n      if (!match) {\n        return [[], emptyParam];\n      }\n      const index = match.indexOf(\"\", 1);\n      return [matcher[1][index], match];\n    };\n    return this.match(method, path);\n  }\n  #buildAllMatchers() {\n    const matchers = /* @__PURE__ */ Object.create(null);\n    Object.keys(this.#routes).concat(Object.keys(this.#middleware)).forEach((method) => {\n      matchers[method] ||= this.#buildMatcher(method);\n    });\n    this.#middleware = this.#routes = void 0;\n    return matchers;\n  }\n  #buildMatcher(method) {\n    const routes = [];\n    let hasOwnRoute = method === METHOD_NAME_ALL;\n    [this.#middleware, this.#routes].forEach((r) => {\n      const ownRoute = r[method] ? Object.keys(r[method]).map((path) => [path, r[method][path]]) : [];\n      if (ownRoute.length !== 0) {\n        hasOwnRoute ||= true;\n        routes.push(...ownRoute);\n      } else if (method !== METHOD_NAME_ALL) {\n        routes.push(\n          ...Object.keys(r[METHOD_NAME_ALL]).map((path) => [path, r[METHOD_NAME_ALL][path]])\n        );\n      }\n    });\n    if (!hasOwnRoute) {\n      return null;\n    } else {\n      return buildMatcherFromPreprocessedRoutes(routes);\n    }\n  }\n};\nexport {\n  RegExpRouter\n};\n", "// src/router/reg-exp-router/node.ts\nvar LABEL_REG_EXP_STR = \"[^/]+\";\nvar ONLY_WILDCARD_REG_EXP_STR = \".*\";\nvar TAIL_WILDCARD_REG_EXP_STR = \"(?:|/.*)\";\nvar PATH_ERROR = Symbol();\nvar regExpMetaChars = new Set(\".\\\\+*[^]$()\");\nfunction compareKey(a, b) {\n  if (a.length === 1) {\n    return b.length === 1 ? a < b ? -1 : 1 : -1;\n  }\n  if (b.length === 1) {\n    return 1;\n  }\n  if (a === ONLY_WILDCARD_REG_EXP_STR || a === TAIL_WILDCARD_REG_EXP_STR) {\n    return 1;\n  } else if (b === ONLY_WILDCARD_REG_EXP_STR || b === TAIL_WILDCARD_REG_EXP_STR) {\n    return -1;\n  }\n  if (a === LABEL_REG_EXP_STR) {\n    return 1;\n  } else if (b === LABEL_REG_EXP_STR) {\n    return -1;\n  }\n  return a.length === b.length ? a < b ? -1 : 1 : b.length - a.length;\n}\nvar Node = class {\n  #index;\n  #varIndex;\n  #children = /* @__PURE__ */ Object.create(null);\n  insert(tokens, index, paramMap, context, pathErrorCheckOnly) {\n    if (tokens.length === 0) {\n      if (this.#index !== void 0) {\n        throw PATH_ERROR;\n      }\n      if (pathErrorCheckOnly) {\n        return;\n      }\n      this.#index = index;\n      return;\n    }\n    const [token, ...restTokens] = tokens;\n    const pattern = token === \"*\" ? restTokens.length === 0 ? [\"\", \"\", ONLY_WILDCARD_REG_EXP_STR] : [\"\", \"\", LABEL_REG_EXP_STR] : token === \"/*\" ? [\"\", \"\", TAIL_WILDCARD_REG_EXP_STR] : token.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n    let node;\n    if (pattern) {\n      const name = pattern[1];\n      let regexpStr = pattern[2] || LABEL_REG_EXP_STR;\n      if (name && pattern[2]) {\n        regexpStr = regexpStr.replace(/^\\((?!\\?:)(?=[^)]+\\)$)/, \"(?:\");\n        if (/\\((?!\\?:)/.test(regexpStr)) {\n          throw PATH_ERROR;\n        }\n      }\n      node = this.#children[regexpStr];\n      if (!node) {\n        if (Object.keys(this.#children).some(\n          (k) => k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR\n        )) {\n          throw PATH_ERROR;\n        }\n        if (pathErrorCheckOnly) {\n          return;\n        }\n        node = this.#children[regexpStr] = new Node();\n        if (name !== \"\") {\n          node.#varIndex = context.varIndex++;\n        }\n      }\n      if (!pathErrorCheckOnly && name !== \"\") {\n        paramMap.push([name, node.#varIndex]);\n      }\n    } else {\n      node = this.#children[token];\n      if (!node) {\n        if (Object.keys(this.#children).some(\n          (k) => k.length > 1 && k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR\n        )) {\n          throw PATH_ERROR;\n        }\n        if (pathErrorCheckOnly) {\n          return;\n        }\n        node = this.#children[token] = new Node();\n      }\n    }\n    node.insert(restTokens, index, paramMap, context, pathErrorCheckOnly);\n  }\n  buildRegExpStr() {\n    const childKeys = Object.keys(this.#children).sort(compareKey);\n    const strList = childKeys.map((k) => {\n      const c = this.#children[k];\n      return (typeof c.#varIndex === \"number\" ? `(${k})@${c.#varIndex}` : regExpMetaChars.has(k) ? `\\\\${k}` : k) + c.buildRegExpStr();\n    });\n    if (typeof this.#index === \"number\") {\n      strList.unshift(`#${this.#index}`);\n    }\n    if (strList.length === 0) {\n      return \"\";\n    }\n    if (strList.length === 1) {\n      return strList[0];\n    }\n    return \"(?:\" + strList.join(\"|\") + \")\";\n  }\n};\nexport {\n  Node,\n  PATH_ERROR\n};\n", "// src/router/reg-exp-router/trie.ts\nimport { Node } from \"./node.js\";\nvar Trie = class {\n  #context = { varIndex: 0 };\n  #root = new Node();\n  insert(path, index, pathErrorCheckOnly) {\n    const paramAssoc = [];\n    const groups = [];\n    for (let i = 0; ; ) {\n      let replaced = false;\n      path = path.replace(/\\{[^}]+\\}/g, (m) => {\n        const mark = `@\\\\${i}`;\n        groups[i] = [mark, m];\n        i++;\n        replaced = true;\n        return mark;\n      });\n      if (!replaced) {\n        break;\n      }\n    }\n    const tokens = path.match(/(?::[^\\/]+)|(?:\\/\\*$)|./g) || [];\n    for (let i = groups.length - 1; i >= 0; i--) {\n      const [mark] = groups[i];\n      for (let j = tokens.length - 1; j >= 0; j--) {\n        if (tokens[j].indexOf(mark) !== -1) {\n          tokens[j] = tokens[j].replace(mark, groups[i][1]);\n          break;\n        }\n      }\n    }\n    this.#root.insert(tokens, index, paramAssoc, this.#context, pathErrorCheckOnly);\n    return paramAssoc;\n  }\n  buildRegExp() {\n    let regexp = this.#root.buildRegExpStr();\n    if (regexp === \"\") {\n      return [/^$/, [], []];\n    }\n    let captureIndex = 0;\n    const indexReplacementMap = [];\n    const paramReplacementMap = [];\n    regexp = regexp.replace(/#(\\d+)|@(\\d+)|\\.\\*\\$/g, (_, handlerIndex, paramIndex) => {\n      if (handlerIndex !== void 0) {\n        indexReplacementMap[++captureIndex] = Number(handlerIndex);\n        return \"$()\";\n      }\n      if (paramIndex !== void 0) {\n        paramReplacementMap[Number(paramIndex)] = ++captureIndex;\n        return \"\";\n      }\n      return \"\";\n    });\n    return [new RegExp(`^${regexp}`), indexReplacementMap, paramReplacementMap];\n  }\n};\nexport {\n  Trie\n};\n", "// src/router/smart-router/index.ts\nimport { SmartRouter } from \"./router.js\";\nexport {\n  SmartRouter\n};\n", "// src/router/smart-router/router.ts\nimport { MESSAGE_MATCHER_IS_ALREADY_BUILT, UnsupportedPathError } from \"../../router.js\";\nvar SmartRouter = class {\n  name = \"SmartRouter\";\n  #routers = [];\n  #routes = [];\n  constructor(init) {\n    this.#routers = init.routers;\n  }\n  add(method, path, handler) {\n    if (!this.#routes) {\n      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);\n    }\n    this.#routes.push([method, path, handler]);\n  }\n  match(method, path) {\n    if (!this.#routes) {\n      throw new Error(\"Fatal error\");\n    }\n    const routers = this.#routers;\n    const routes = this.#routes;\n    const len = routers.length;\n    let i = 0;\n    let res;\n    for (; i < len; i++) {\n      const router = routers[i];\n      try {\n        for (let i2 = 0, len2 = routes.length; i2 < len2; i2++) {\n          router.add(...routes[i2]);\n        }\n        res = router.match(method, path);\n      } catch (e) {\n        if (e instanceof UnsupportedPathError) {\n          continue;\n        }\n        throw e;\n      }\n      this.match = router.match.bind(router);\n      this.#routers = [router];\n      this.#routes = void 0;\n      break;\n    }\n    if (i === len) {\n      throw new Error(\"Fatal error\");\n    }\n    this.name = `SmartRouter + ${this.activeRouter.name}`;\n    return res;\n  }\n  get activeRouter() {\n    if (this.#routes || this.#routers.length !== 1) {\n      throw new Error(\"No active router has been determined yet.\");\n    }\n    return this.#routers[0];\n  }\n};\nexport {\n  SmartRouter\n};\n", "// src/router/trie-router/index.ts\nimport { <PERSON>eRouter } from \"./router.js\";\nexport {\n  TrieRouter\n};\n", "// src/router/trie-router/router.ts\nimport { checkOptionalParameter } from \"../../utils/url.js\";\nimport { Node } from \"./node.js\";\nvar TrieRouter = class {\n  name = \"TrieRouter\";\n  #node;\n  constructor() {\n    this.#node = new Node();\n  }\n  add(method, path, handler) {\n    const results = checkOptionalParameter(path);\n    if (results) {\n      for (let i = 0, len = results.length; i < len; i++) {\n        this.#node.insert(method, results[i], handler);\n      }\n      return;\n    }\n    this.#node.insert(method, path, handler);\n  }\n  match(method, path) {\n    return this.#node.search(method, path);\n  }\n};\nexport {\n  TrieRouter\n};\n", "// src/router/trie-router/node.ts\nimport { METHOD_NAME_ALL } from \"../../router.js\";\nimport { getPattern, splitPath, splitRoutingPath } from \"../../utils/url.js\";\nvar emptyParams = /* @__PURE__ */ Object.create(null);\nvar Node = class {\n  #methods;\n  #children;\n  #patterns;\n  #order = 0;\n  #params = emptyParams;\n  constructor(method, handler, children) {\n    this.#children = children || /* @__PURE__ */ Object.create(null);\n    this.#methods = [];\n    if (method && handler) {\n      const m = /* @__PURE__ */ Object.create(null);\n      m[method] = { handler, possibleKeys: [], score: 0 };\n      this.#methods = [m];\n    }\n    this.#patterns = [];\n  }\n  insert(method, path, handler) {\n    this.#order = ++this.#order;\n    let curNode = this;\n    const parts = splitRoutingPath(path);\n    const possibleKeys = [];\n    for (let i = 0, len = parts.length; i < len; i++) {\n      const p = parts[i];\n      const nextP = parts[i + 1];\n      const pattern = getPattern(p, nextP);\n      const key = Array.isArray(pattern) ? pattern[0] : p;\n      if (key in curNode.#children) {\n        curNode = curNode.#children[key];\n        if (pattern) {\n          possibleKeys.push(pattern[1]);\n        }\n        continue;\n      }\n      curNode.#children[key] = new Node();\n      if (pattern) {\n        curNode.#patterns.push(pattern);\n        possibleKeys.push(pattern[1]);\n      }\n      curNode = curNode.#children[key];\n    }\n    curNode.#methods.push({\n      [method]: {\n        handler,\n        possibleKeys: possibleKeys.filter((v, i, a) => a.indexOf(v) === i),\n        score: this.#order\n      }\n    });\n    return curNode;\n  }\n  #getHandlerSets(node, method, nodeParams, params) {\n    const handlerSets = [];\n    for (let i = 0, len = node.#methods.length; i < len; i++) {\n      const m = node.#methods[i];\n      const handlerSet = m[method] || m[METHOD_NAME_ALL];\n      const processedSet = {};\n      if (handlerSet !== void 0) {\n        handlerSet.params = /* @__PURE__ */ Object.create(null);\n        handlerSets.push(handlerSet);\n        if (nodeParams !== emptyParams || params && params !== emptyParams) {\n          for (let i2 = 0, len2 = handlerSet.possibleKeys.length; i2 < len2; i2++) {\n            const key = handlerSet.possibleKeys[i2];\n            const processed = processedSet[handlerSet.score];\n            handlerSet.params[key] = params?.[key] && !processed ? params[key] : nodeParams[key] ?? params?.[key];\n            processedSet[handlerSet.score] = true;\n          }\n        }\n      }\n    }\n    return handlerSets;\n  }\n  search(method, path) {\n    const handlerSets = [];\n    this.#params = emptyParams;\n    const curNode = this;\n    let curNodes = [curNode];\n    const parts = splitPath(path);\n    const curNodesQueue = [];\n    for (let i = 0, len = parts.length; i < len; i++) {\n      const part = parts[i];\n      const isLast = i === len - 1;\n      const tempNodes = [];\n      for (let j = 0, len2 = curNodes.length; j < len2; j++) {\n        const node = curNodes[j];\n        const nextNode = node.#children[part];\n        if (nextNode) {\n          nextNode.#params = node.#params;\n          if (isLast) {\n            if (nextNode.#children[\"*\"]) {\n              handlerSets.push(\n                ...this.#getHandlerSets(nextNode.#children[\"*\"], method, node.#params)\n              );\n            }\n            handlerSets.push(...this.#getHandlerSets(nextNode, method, node.#params));\n          } else {\n            tempNodes.push(nextNode);\n          }\n        }\n        for (let k = 0, len3 = node.#patterns.length; k < len3; k++) {\n          const pattern = node.#patterns[k];\n          const params = node.#params === emptyParams ? {} : { ...node.#params };\n          if (pattern === \"*\") {\n            const astNode = node.#children[\"*\"];\n            if (astNode) {\n              handlerSets.push(...this.#getHandlerSets(astNode, method, node.#params));\n              astNode.#params = params;\n              tempNodes.push(astNode);\n            }\n            continue;\n          }\n          if (!part) {\n            continue;\n          }\n          const [key, name, matcher] = pattern;\n          const child = node.#children[key];\n          const restPathString = parts.slice(i).join(\"/\");\n          if (matcher instanceof RegExp) {\n            const m = matcher.exec(restPathString);\n            if (m) {\n              params[name] = m[0];\n              handlerSets.push(...this.#getHandlerSets(child, method, node.#params, params));\n              if (Object.keys(child.#children).length) {\n                child.#params = params;\n                const componentCount = m[0].match(/\\//)?.length ?? 0;\n                const targetCurNodes = curNodesQueue[componentCount] ||= [];\n                targetCurNodes.push(child);\n              }\n              continue;\n            }\n          }\n          if (matcher === true || matcher.test(part)) {\n            params[name] = part;\n            if (isLast) {\n              handlerSets.push(...this.#getHandlerSets(child, method, params, node.#params));\n              if (child.#children[\"*\"]) {\n                handlerSets.push(\n                  ...this.#getHandlerSets(child.#children[\"*\"], method, params, node.#params)\n                );\n              }\n            } else {\n              child.#params = params;\n              tempNodes.push(child);\n            }\n          }\n        }\n      }\n      curNodes = tempNodes.concat(curNodesQueue.shift() ?? []);\n    }\n    if (handlerSets.length > 1) {\n      handlerSets.sort((a, b) => {\n        return a.score - b.score;\n      });\n    }\n    return [handlerSets.map(({ handler, params }) => [handler, params])];\n  }\n};\nexport {\n  Node\n};\n", "import { Context, Next } from 'hono';\nimport { cors } from 'hono/cors';\nimport { CloudflareBindings } from '../types/interfaces';\nimport * as response from '../utils/response';\n\nexport function corsMiddleware() {\n  return cors({\n    origin: '*',\n    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],\n    allowHeaders: ['Content-Type', 'Authorization'],\n    credentials: true,\n  });\n}\n\nexport function bindingsMiddleware() {\n  return async (c: Context<{ Bindings: CloudflareBindings }>, next: Next) => {\n    if (!c.env.DB) {\n      return response.error(c, '数据库不可用', 'DATABASE_UNAVAILABLE', 500);\n    }\n    if (!c.env.CACHE) {\n      return response.error(c, '缓存不可用', 'CACHE_UNAVAILABLE', 500);\n    }\n    await next();\n  };\n}", "// src/middleware/cors/index.ts\nvar cors = (options) => {\n  const defaults = {\n    origin: \"*\",\n    allowMethods: [\"GET\", \"HEAD\", \"PUT\", \"POST\", \"DELETE\", \"PATCH\"],\n    allowHeaders: [],\n    exposeHeaders: []\n  };\n  const opts = {\n    ...defaults,\n    ...options\n  };\n  const findAllowOrigin = ((optsOrigin) => {\n    if (typeof optsOrigin === \"string\") {\n      if (optsOrigin === \"*\") {\n        return () => optsOrigin;\n      } else {\n        return (origin) => optsOrigin === origin ? origin : null;\n      }\n    } else if (typeof optsOrigin === \"function\") {\n      return optsOrigin;\n    } else {\n      return (origin) => optsOrigin.includes(origin) ? origin : null;\n    }\n  })(opts.origin);\n  const findAllowMethods = ((optsAllowMethods) => {\n    if (typeof optsAllowMethods === \"function\") {\n      return optsAllowMethods;\n    } else if (Array.isArray(optsAllowMethods)) {\n      return () => optsAllowMethods;\n    } else {\n      return () => [];\n    }\n  })(opts.allowMethods);\n  return async function cors2(c, next) {\n    function set(key, value) {\n      c.res.headers.set(key, value);\n    }\n    const allowOrigin = findAllowOrigin(c.req.header(\"origin\") || \"\", c);\n    if (allowOrigin) {\n      set(\"Access-Control-Allow-Origin\", allowOrigin);\n    }\n    if (opts.origin !== \"*\") {\n      const existingVary = c.req.header(\"Vary\");\n      if (existingVary) {\n        set(\"Vary\", existingVary);\n      } else {\n        set(\"Vary\", \"Origin\");\n      }\n    }\n    if (opts.credentials) {\n      set(\"Access-Control-Allow-Credentials\", \"true\");\n    }\n    if (opts.exposeHeaders?.length) {\n      set(\"Access-Control-Expose-Headers\", opts.exposeHeaders.join(\",\"));\n    }\n    if (c.req.method === \"OPTIONS\") {\n      if (opts.maxAge != null) {\n        set(\"Access-Control-Max-Age\", opts.maxAge.toString());\n      }\n      const allowMethods = findAllowMethods(c.req.header(\"origin\") || \"\", c);\n      if (allowMethods.length) {\n        set(\"Access-Control-Allow-Methods\", allowMethods.join(\",\"));\n      }\n      let headers = opts.allowHeaders;\n      if (!headers?.length) {\n        const requestHeaders = c.req.header(\"Access-Control-Request-Headers\");\n        if (requestHeaders) {\n          headers = requestHeaders.split(/\\s*,\\s*/);\n        }\n      }\n      if (headers?.length) {\n        set(\"Access-Control-Allow-Headers\", headers.join(\",\"));\n        c.res.headers.append(\"Vary\", \"Access-Control-Request-Headers\");\n      }\n      c.res.headers.delete(\"Content-Length\");\n      c.res.headers.delete(\"Content-Type\");\n      return new Response(null, {\n        headers: c.res.headers,\n        status: 204,\n        statusText: \"No Content\"\n      });\n    }\n    await next();\n  };\n};\nexport {\n  cors\n};\n", "import { Context } from 'hono';\n\n// 统一响应格式，匹配API文档要求\nexport function success<T>(c: Context, message: string,  data?: T): Response {\n  const response = {\n    success: true,\n    msg: message,\n    data: data || null,\n  };\n  return c.json(response);\n}\n\nexport function error(c: Context, message: string, code?: string, status: number = 400): Response {\n  const response = {\n    success: false,\n    msg: message,\n    data: null,\n    code,\n  };\n  return c.json(response, status as any);\n}\n\n", "import { Hono } from 'hono';\nimport { CloudflareBindings } from '../types/interfaces';\nimport { JWTService } from '../utils/auth';\nimport * as response from '../utils/response';\nimport * as bcrypt from 'bcryptjs';\n\nexport function setupAuthRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {\n  // Admin login endpoint\n  app.post('/api/auth/login', async (c) => {\n    try {\n      const body = await c.req.json();\n      const { username, password } = body;\n\n      if (!username || !password) {\n        return response.error(c, '用户名和密码不能为空', 'MISSING_CREDENTIALS', 400);\n      }\n\n      // Get admin by username\n      const admin = await c.env.DB.prepare(`\n        SELECT id, username, password_hash, role, status, product_ids\n        FROM admins\n        WHERE username = ? AND status = 'active'\n      `).bind(username).first();\n\n      if (!admin) {\n        return response.error(c, '用户名或密码错误', 'INVALID_CREDENTIALS', 401);\n      }\n\n      // Verify password using bcrypt\n      const isPasswordValid = await bcrypt.compare(password, admin.password_hash as string);\n\n      if (!isPasswordValid) {\n        return response.error(c, '用户名或密码错误', 'INVALID_CREDENTIALS', 401);\n      }\n\n      const jwtService = new JWTService(c.env.JWT_SECRET);\n\n      // Generate tokens\n      const tokenPayload = {\n        admin_id: admin.id as number,\n        username: admin.username as string,\n        role: admin.role as string,\n      };\n\n      const token = await jwtService.generateToken(tokenPayload);\n      const refreshToken = await jwtService.generateRefreshToken(tokenPayload);\n\n      // Parse product_ids\n      let productIds: string[] = [];\n      if (admin.product_ids) {\n        try {\n          productIds = JSON.parse(admin.product_ids as string);\n        } catch {\n          productIds = (admin.product_ids as string).split(',').map((id: string) => id.trim());\n        }\n      }\n\n      // Cache admin info\n      const cacheKey = `admin:auth:${admin.id}`;\n      await c.env.CACHE.put(cacheKey, JSON.stringify({\n        id: admin.id,\n        username: admin.username,\n        role: admin.role,\n        product_ids: productIds,\n      }), { expirationTtl: 3600 }); // 1 hour\n\n      return response.success(c, '登录成功', {\n        token,\n        admin_id: admin.id as number,\n        username: admin.username as string,\n        role: admin.role as string,\n        authorized_products: productIds,\n        expires_in: 86400,\n      });\n\n    } catch (error) {\n      console.error('Admin login error:', error);\n      return response.error(c, '服务器内部错误', 'LOGIN_ERROR', 500);\n    }\n  });\n\n  // Token refresh endpoint\n  app.post('/api/auth/refresh', async (c) => {\n    try {\n      const authHeader = c.req.header('Authorization');\n      \n      if (!authHeader || !authHeader.startsWith('Bearer ')) {\n        return response.error(c, '缺少有效的刷新token', 'MISSING_TOKEN', 401);\n      }\n\n      const refresh_token = authHeader.substring(7);\n\n      const jwtService = new JWTService(c.env.JWT_SECRET);\n\n      // Verify refresh token\n      let payload;\n      try {\n        payload = await jwtService.verifyToken(refresh_token);\n      } catch {\n        return response.error(c, '无效或过期的刷新token', 'INVALID_TOKEN', 401);\n      }\n\n      // Get fresh admin info\n      const admin = await c.env.DB.prepare(`\n        SELECT id, username, role, status, product_ids\n        FROM admins\n        WHERE id = ? AND status = 'active'\n      `).bind(payload.admin_id).first();\n\n      if (!admin) {\n        return response.error(c, '管理员账号不存在或已禁用', 'ADMIN_NOT_FOUND', 401);\n      }\n\n      // Generate new tokens\n      const tokenPayload = {\n        admin_id: admin.id as number,\n        username: admin.username as string,\n        role: admin.role as string,\n      };\n\n      const token = await jwtService.generateToken(tokenPayload);\n      const newRefreshToken = await jwtService.generateRefreshToken(tokenPayload);\n\n      // Parse product_ids\n      let productIds: string[] = [];\n      if (admin.product_ids) {\n        try {\n          productIds = JSON.parse(admin.product_ids as string);\n        } catch {\n          productIds = (admin.product_ids as string).split(',').map((id: string) => id.trim());\n        }\n      }\n\n      // Update cache\n      const cacheKey = `admin:auth:${admin.id}`;\n      await c.env.CACHE.put(cacheKey, JSON.stringify({\n        id: admin.id,\n        username: admin.username,\n        role: admin.role,\n        product_ids: productIds,\n      }), { expirationTtl: 3600 });\n\n      return response.success(c, 'Token刷新成功', {\n        token,\n        expires_in: 86400,\n      });\n\n    } catch (error) {\n      console.error('Token refresh error:', error);\n      return response.error(c, '服务器内部错误', 'REFRESH_ERROR', 500);\n    }\n  });\n\n  // Logout endpoint\n  app.post('/api/auth/logout', async (c) => {\n    try {\n      const authHeader = c.req.header('Authorization');\n      if (!authHeader || !authHeader.startsWith('Bearer ')) {\n        return response.error(c, '需要授权头', 'MISSING_TOKEN', 401);\n      }\n\n      const token = authHeader.substring(7);\n      const jwtService = new JWTService(c.env.JWT_SECRET);\n\n      try {\n        const payload = await jwtService.verifyToken(token);\n        \n        // Clear cache\n        const cacheKey = `admin:auth:${payload.admin_id}`;\n        await c.env.CACHE.delete(cacheKey);\n        \n      } catch {\n        // Token is invalid, but we still return success for logout\n      }\n\n      return response.success(c, '登出成功');\n\n    } catch (error) {\n      console.error('Logout error:', error);\n      return response.error(c, '服务器内部错误', 'LOGOUT_ERROR', 500);\n    }\n  });\n}", "import { sign, verify } from 'hono/jwt';\nimport { JWTPayload } from '../types/interfaces';\n\nexport class JWTService {\n  private secret: string;\n\n  constructor(secret: string) {\n    this.secret = secret;\n  }\n\n  async generateToken(payload: Omit<JWTPayload, 'exp' | 'iat'>): Promise<string> {\n    const now = Math.floor(Date.now() / 1000);\n    const jwtPayload = {\n      ...payload,\n      iat: now,\n      exp: now + 24 * 60 * 60, // 24小时过期\n    };\n    return await sign(jwtPayload, this.secret);\n  }\n\n  async generateRefreshToken(payload: Omit<JWTPayload, 'exp' | 'iat'>): Promise<string> {\n    const now = Math.floor(Date.now() / 1000);\n    const jwtPayload = {\n      ...payload,\n      iat: now,\n      exp: now + 7 * 24 * 60 * 60, // 7天过期\n    };\n    return await sign(jwtPayload, this.secret);\n  }\n\n  async verifyToken(token: string): Promise<JWTPayload> {\n    return await verify(token, this.secret) as JWTPayload;\n  }\n}\n\nexport function generateRandomKey(length: number = 32): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}", "// src/middleware/jwt/index.ts\nimport { jwt, verify, decode, sign } from \"./jwt.js\";\nexport {\n  decode,\n  jwt,\n  sign,\n  verify\n};\n", "// src/middleware/jwt/jwt.ts\nimport { getCookie, getSignedCookie } from \"../../helper/cookie/index.js\";\nimport { HTTPException } from \"../../http-exception.js\";\nimport { Jwt } from \"../../utils/jwt/index.js\";\nimport \"../../context.js\";\nvar jwt = (options) => {\n  if (!options || !options.secret) {\n    throw new Error('JWT auth middleware requires options for \"secret\"');\n  }\n  if (!crypto.subtle || !crypto.subtle.importKey) {\n    throw new Error(\"`crypto.subtle.importKey` is undefined. JWT auth middleware requires it.\");\n  }\n  return async function jwt2(ctx, next) {\n    const headerName = options.headerName || \"Authorization\";\n    const credentials = ctx.req.raw.headers.get(headerName);\n    let token;\n    if (credentials) {\n      const parts = credentials.split(/\\s+/);\n      if (parts.length !== 2) {\n        const errDescription = \"invalid credentials structure\";\n        throw new HTTPException(401, {\n          message: errDescription,\n          res: unauthorizedResponse({\n            ctx,\n            error: \"invalid_request\",\n            errDescription\n          })\n        });\n      } else {\n        token = parts[1];\n      }\n    } else if (options.cookie) {\n      if (typeof options.cookie == \"string\") {\n        token = getCookie(ctx, options.cookie);\n      } else if (options.cookie.secret) {\n        if (options.cookie.prefixOptions) {\n          token = await getSignedCookie(\n            ctx,\n            options.cookie.secret,\n            options.cookie.key,\n            options.cookie.prefixOptions\n          );\n        } else {\n          token = await getSignedCookie(ctx, options.cookie.secret, options.cookie.key);\n        }\n      } else {\n        if (options.cookie.prefixOptions) {\n          token = getCookie(ctx, options.cookie.key, options.cookie.prefixOptions);\n        } else {\n          token = getCookie(ctx, options.cookie.key);\n        }\n      }\n    }\n    if (!token) {\n      const errDescription = \"no authorization included in request\";\n      throw new HTTPException(401, {\n        message: errDescription,\n        res: unauthorizedResponse({\n          ctx,\n          error: \"invalid_request\",\n          errDescription\n        })\n      });\n    }\n    let payload;\n    let cause;\n    try {\n      payload = await Jwt.verify(token, options.secret, options.alg);\n    } catch (e) {\n      cause = e;\n    }\n    if (!payload) {\n      throw new HTTPException(401, {\n        message: \"Unauthorized\",\n        res: unauthorizedResponse({\n          ctx,\n          error: \"invalid_token\",\n          statusText: \"Unauthorized\",\n          errDescription: \"token verification failure\"\n        }),\n        cause\n      });\n    }\n    ctx.set(\"jwtPayload\", payload);\n    await next();\n  };\n};\nfunction unauthorizedResponse(opts) {\n  return new Response(\"Unauthorized\", {\n    status: 401,\n    statusText: opts.statusText,\n    headers: {\n      \"WWW-Authenticate\": `Bearer realm=\"${opts.ctx.req.url}\",error=\"${opts.error}\",error_description=\"${opts.errDescription}\"`\n    }\n  });\n}\nvar verify = Jwt.verify;\nvar decode = Jwt.decode;\nvar sign = Jwt.sign;\nexport {\n  decode,\n  jwt,\n  sign,\n  verify\n};\n", "// src/helper/cookie/index.ts\nimport { parse, parseSigned, serialize, serializeSigned } from \"../../utils/cookie.js\";\nvar getCookie = (c, key, prefix) => {\n  const cookie = c.req.raw.headers.get(\"Cookie\");\n  if (typeof key === \"string\") {\n    if (!cookie) {\n      return void 0;\n    }\n    let finalKey = key;\n    if (prefix === \"secure\") {\n      finalKey = \"__Secure-\" + key;\n    } else if (prefix === \"host\") {\n      finalKey = \"__Host-\" + key;\n    }\n    const obj2 = parse(cookie, finalKey);\n    return obj2[finalKey];\n  }\n  if (!cookie) {\n    return {};\n  }\n  const obj = parse(cookie);\n  return obj;\n};\nvar getSignedCookie = async (c, secret, key, prefix) => {\n  const cookie = c.req.raw.headers.get(\"Cookie\");\n  if (typeof key === \"string\") {\n    if (!cookie) {\n      return void 0;\n    }\n    let finalKey = key;\n    if (prefix === \"secure\") {\n      finalKey = \"__Secure-\" + key;\n    } else if (prefix === \"host\") {\n      finalKey = \"__Host-\" + key;\n    }\n    const obj2 = await parseSigned(cookie, secret, finalKey);\n    return obj2[finalKey];\n  }\n  if (!cookie) {\n    return {};\n  }\n  const obj = await parseSigned(cookie, secret);\n  return obj;\n};\nvar setCookie = (c, name, value, opt) => {\n  let cookie;\n  if (opt?.prefix === \"secure\") {\n    cookie = serialize(\"__Secure-\" + name, value, { path: \"/\", ...opt, secure: true });\n  } else if (opt?.prefix === \"host\") {\n    cookie = serialize(\"__Host-\" + name, value, {\n      ...opt,\n      path: \"/\",\n      secure: true,\n      domain: void 0\n    });\n  } else {\n    cookie = serialize(name, value, { path: \"/\", ...opt });\n  }\n  c.header(\"Set-Cookie\", cookie, { append: true });\n};\nvar setSignedCookie = async (c, name, value, secret, opt) => {\n  let cookie;\n  if (opt?.prefix === \"secure\") {\n    cookie = await serializeSigned(\"__Secure-\" + name, value, secret, {\n      path: \"/\",\n      ...opt,\n      secure: true\n    });\n  } else if (opt?.prefix === \"host\") {\n    cookie = await serializeSigned(\"__Host-\" + name, value, secret, {\n      ...opt,\n      path: \"/\",\n      secure: true,\n      domain: void 0\n    });\n  } else {\n    cookie = await serializeSigned(name, value, secret, { path: \"/\", ...opt });\n  }\n  c.header(\"set-cookie\", cookie, { append: true });\n};\nvar deleteCookie = (c, name, opt) => {\n  const deletedCookie = getCookie(c, name, opt?.prefix);\n  setCookie(c, name, \"\", { ...opt, maxAge: 0 });\n  return deletedCookie;\n};\nexport {\n  deleteCookie,\n  getCookie,\n  getSignedCookie,\n  setCookie,\n  setSignedCookie\n};\n", "// src/utils/cookie.ts\nimport { decodeURIComponent_, tryDecode } from \"./url.js\";\nvar algorithm = { name: \"HMAC\", hash: \"SHA-256\" };\nvar getCryptoKey = async (secret) => {\n  const secretBuf = typeof secret === \"string\" ? new TextEncoder().encode(secret) : secret;\n  return await crypto.subtle.importKey(\"raw\", secretBuf, algorithm, false, [\"sign\", \"verify\"]);\n};\nvar makeSignature = async (value, secret) => {\n  const key = await getCryptoKey(secret);\n  const signature = await crypto.subtle.sign(algorithm.name, key, new TextEncoder().encode(value));\n  return btoa(String.fromCharCode(...new Uint8Array(signature)));\n};\nvar verifySignature = async (base64Signature, value, secret) => {\n  try {\n    const signatureBinStr = atob(base64Signature);\n    const signature = new Uint8Array(signatureBinStr.length);\n    for (let i = 0, len = signatureBinStr.length; i < len; i++) {\n      signature[i] = signatureBinStr.charCodeAt(i);\n    }\n    return await crypto.subtle.verify(algorithm, secret, signature, new TextEncoder().encode(value));\n  } catch {\n    return false;\n  }\n};\nvar validCookieNameRegEx = /^[\\w!#$%&'*.^`|~+-]+$/;\nvar validCookieValueRegEx = /^[ !#-:<-[\\]-~]*$/;\nvar parse = (cookie, name) => {\n  if (name && cookie.indexOf(name) === -1) {\n    return {};\n  }\n  const pairs = cookie.trim().split(\";\");\n  const parsedCookie = {};\n  for (let pairStr of pairs) {\n    pairStr = pairStr.trim();\n    const valueStartPos = pairStr.indexOf(\"=\");\n    if (valueStartPos === -1) {\n      continue;\n    }\n    const cookieName = pairStr.substring(0, valueStartPos).trim();\n    if (name && name !== cookieName || !validCookieNameRegEx.test(cookieName)) {\n      continue;\n    }\n    let cookieValue = pairStr.substring(valueStartPos + 1).trim();\n    if (cookieValue.startsWith('\"') && cookieValue.endsWith('\"')) {\n      cookieValue = cookieValue.slice(1, -1);\n    }\n    if (validCookieValueRegEx.test(cookieValue)) {\n      parsedCookie[cookieName] = cookieValue.indexOf(\"%\") !== -1 ? tryDecode(cookieValue, decodeURIComponent_) : cookieValue;\n      if (name) {\n        break;\n      }\n    }\n  }\n  return parsedCookie;\n};\nvar parseSigned = async (cookie, secret, name) => {\n  const parsedCookie = {};\n  const secretKey = await getCryptoKey(secret);\n  for (const [key, value] of Object.entries(parse(cookie, name))) {\n    const signatureStartPos = value.lastIndexOf(\".\");\n    if (signatureStartPos < 1) {\n      continue;\n    }\n    const signedValue = value.substring(0, signatureStartPos);\n    const signature = value.substring(signatureStartPos + 1);\n    if (signature.length !== 44 || !signature.endsWith(\"=\")) {\n      continue;\n    }\n    const isVerified = await verifySignature(signature, signedValue, secretKey);\n    parsedCookie[key] = isVerified ? signedValue : false;\n  }\n  return parsedCookie;\n};\nvar _serialize = (name, value, opt = {}) => {\n  let cookie = `${name}=${value}`;\n  if (name.startsWith(\"__Secure-\") && !opt.secure) {\n    throw new Error(\"__Secure- Cookie must have Secure attributes\");\n  }\n  if (name.startsWith(\"__Host-\")) {\n    if (!opt.secure) {\n      throw new Error(\"__Host- Cookie must have Secure attributes\");\n    }\n    if (opt.path !== \"/\") {\n      throw new Error('__Host- Cookie must have Path attributes with \"/\"');\n    }\n    if (opt.domain) {\n      throw new Error(\"__Host- Cookie must not have Domain attributes\");\n    }\n  }\n  if (opt && typeof opt.maxAge === \"number\" && opt.maxAge >= 0) {\n    if (opt.maxAge > 3456e4) {\n      throw new Error(\n        \"Cookies Max-Age SHOULD NOT be greater than 400 days (34560000 seconds) in duration.\"\n      );\n    }\n    cookie += `; Max-Age=${opt.maxAge | 0}`;\n  }\n  if (opt.domain && opt.prefix !== \"host\") {\n    cookie += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    cookie += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (opt.expires.getTime() - Date.now() > 3456e7) {\n      throw new Error(\n        \"Cookies Expires SHOULD NOT be greater than 400 days (34560000 seconds) in the future.\"\n      );\n    }\n    cookie += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) {\n    cookie += \"; HttpOnly\";\n  }\n  if (opt.secure) {\n    cookie += \"; Secure\";\n  }\n  if (opt.sameSite) {\n    cookie += `; SameSite=${opt.sameSite.charAt(0).toUpperCase() + opt.sameSite.slice(1)}`;\n  }\n  if (opt.priority) {\n    cookie += `; Priority=${opt.priority.charAt(0).toUpperCase() + opt.priority.slice(1)}`;\n  }\n  if (opt.partitioned) {\n    if (!opt.secure) {\n      throw new Error(\"Partitioned Cookie must have Secure attributes\");\n    }\n    cookie += \"; Partitioned\";\n  }\n  return cookie;\n};\nvar serialize = (name, value, opt) => {\n  value = encodeURIComponent(value);\n  return _serialize(name, value, opt);\n};\nvar serializeSigned = async (name, value, secret, opt = {}) => {\n  const signature = await makeSignature(value, secret);\n  value = `${value}.${signature}`;\n  value = encodeURIComponent(value);\n  return _serialize(name, value, opt);\n};\nexport {\n  parse,\n  parseSigned,\n  serialize,\n  serializeSigned\n};\n", "// src/http-exception.ts\nvar HTTPException = class extends Error {\n  res;\n  status;\n  constructor(status = 500, options) {\n    super(options?.message, { cause: options?.cause });\n    this.res = options?.res;\n    this.status = status;\n  }\n  getResponse() {\n    if (this.res) {\n      const newResponse = new Response(this.res.body, {\n        status: this.status,\n        headers: this.res.headers\n      });\n      return newResponse;\n    }\n    return new Response(this.message, {\n      status: this.status\n    });\n  }\n};\nexport {\n  HTTPException\n};\n", "// src/utils/jwt/index.ts\nimport { decode, sign, verify, verifyFromJwks } from \"./jwt.js\";\nvar Jwt = { sign, verify, decode, verifyFromJwks };\nexport {\n  Jwt\n};\n", "// src/utils/jwt/jwt.ts\nimport { decodeBase64Url, encodeBase64Url } from \"../../utils/encode.js\";\nimport { AlgorithmTypes } from \"./jwa.js\";\nimport { signing, verifying } from \"./jws.js\";\nimport {\n  JwtHeaderInvalid,\n  JwtHeaderRequiresKid,\n  JwtTokenExpired,\n  JwtTokenInvalid,\n  JwtTokenIssuedAt,\n  JwtTokenNotBefore,\n  JwtTokenSignatureMismatched\n} from \"./types.js\";\nimport { utf8Decoder, utf8Encoder } from \"./utf8.js\";\nvar encodeJwtPart = (part) => encodeBase64Url(utf8Encoder.encode(JSON.stringify(part)).buffer).replace(/=/g, \"\");\nvar encodeSignaturePart = (buf) => encodeBase64Url(buf).replace(/=/g, \"\");\nvar decodeJwtPart = (part) => JSON.parse(utf8Decoder.decode(decodeBase64Url(part)));\nfunction isTokenHeader(obj) {\n  if (typeof obj === \"object\" && obj !== null) {\n    const objWithAlg = obj;\n    return \"alg\" in objWithAlg && Object.values(AlgorithmTypes).includes(objWithAlg.alg) && (!(\"typ\" in objWithAlg) || objWithAlg.typ === \"JWT\");\n  }\n  return false;\n}\nvar sign = async (payload, privateKey, alg = \"HS256\") => {\n  const encodedPayload = encodeJwtPart(payload);\n  let encodedHeader;\n  if (typeof privateKey === \"object\" && \"alg\" in privateKey) {\n    alg = privateKey.alg;\n    encodedHeader = encodeJwtPart({ alg, typ: \"JWT\", kid: privateKey.kid });\n  } else {\n    encodedHeader = encodeJwtPart({ alg, typ: \"JWT\" });\n  }\n  const partialToken = `${encodedHeader}.${encodedPayload}`;\n  const signaturePart = await signing(privateKey, alg, utf8Encoder.encode(partialToken));\n  const signature = encodeSignaturePart(signaturePart);\n  return `${partialToken}.${signature}`;\n};\nvar verify = async (token, publicKey, alg = \"HS256\") => {\n  const tokenParts = token.split(\".\");\n  if (tokenParts.length !== 3) {\n    throw new JwtTokenInvalid(token);\n  }\n  const { header, payload } = decode(token);\n  if (!isTokenHeader(header)) {\n    throw new JwtHeaderInvalid(header);\n  }\n  const now = Date.now() / 1e3 | 0;\n  if (payload.nbf && payload.nbf > now) {\n    throw new JwtTokenNotBefore(token);\n  }\n  if (payload.exp && payload.exp <= now) {\n    throw new JwtTokenExpired(token);\n  }\n  if (payload.iat && now < payload.iat) {\n    throw new JwtTokenIssuedAt(now, payload.iat);\n  }\n  const headerPayload = token.substring(0, token.lastIndexOf(\".\"));\n  const verified = await verifying(\n    publicKey,\n    alg,\n    decodeBase64Url(tokenParts[2]),\n    utf8Encoder.encode(headerPayload)\n  );\n  if (!verified) {\n    throw new JwtTokenSignatureMismatched(token);\n  }\n  return payload;\n};\nvar verifyFromJwks = async (token, options, init) => {\n  const header = decodeHeader(token);\n  if (!isTokenHeader(header)) {\n    throw new JwtHeaderInvalid(header);\n  }\n  if (!header.kid) {\n    throw new JwtHeaderRequiresKid(header);\n  }\n  if (options.jwks_uri) {\n    const response = await fetch(options.jwks_uri, init);\n    if (!response.ok) {\n      throw new Error(`failed to fetch JWKS from ${options.jwks_uri}`);\n    }\n    const data = await response.json();\n    if (!data.keys) {\n      throw new Error('invalid JWKS response. \"keys\" field is missing');\n    }\n    if (!Array.isArray(data.keys)) {\n      throw new Error('invalid JWKS response. \"keys\" field is not an array');\n    }\n    if (options.keys) {\n      options.keys.push(...data.keys);\n    } else {\n      options.keys = data.keys;\n    }\n  } else if (!options.keys) {\n    throw new Error('verifyFromJwks requires options for either \"keys\" or \"jwks_uri\" or both');\n  }\n  const matchingKey = options.keys.find((key) => key.kid === header.kid);\n  if (!matchingKey) {\n    throw new JwtTokenInvalid(token);\n  }\n  return await verify(token, matchingKey, matchingKey.alg || header.alg);\n};\nvar decode = (token) => {\n  try {\n    const [h, p] = token.split(\".\");\n    const header = decodeJwtPart(h);\n    const payload = decodeJwtPart(p);\n    return {\n      header,\n      payload\n    };\n  } catch {\n    throw new JwtTokenInvalid(token);\n  }\n};\nvar decodeHeader = (token) => {\n  try {\n    const [h] = token.split(\".\");\n    return decodeJwtPart(h);\n  } catch {\n    throw new JwtTokenInvalid(token);\n  }\n};\nexport {\n  decode,\n  decodeHeader,\n  isTokenHeader,\n  sign,\n  verify,\n  verifyFromJwks\n};\n", "// src/utils/encode.ts\nvar decodeBase64Url = (str) => {\n  return decodeBase64(str.replace(/_|-/g, (m) => ({ _: \"/\", \"-\": \"+\" })[m] ?? m));\n};\nvar encodeBase64Url = (buf) => encodeBase64(buf).replace(/\\/|\\+/g, (m) => ({ \"/\": \"_\", \"+\": \"-\" })[m] ?? m);\nvar encodeBase64 = (buf) => {\n  let binary = \"\";\n  const bytes = new Uint8Array(buf);\n  for (let i = 0, len = bytes.length; i < len; i++) {\n    binary += String.fromCharCode(bytes[i]);\n  }\n  return btoa(binary);\n};\nvar decodeBase64 = (str) => {\n  const binary = atob(str);\n  const bytes = new Uint8Array(new ArrayBuffer(binary.length));\n  const half = binary.length / 2;\n  for (let i = 0, j = binary.length - 1; i <= half; i++, j--) {\n    bytes[i] = binary.charCodeAt(i);\n    bytes[j] = binary.charCodeAt(j);\n  }\n  return bytes;\n};\nexport {\n  decodeBase64,\n  decodeBase64Url,\n  encodeBase64,\n  encodeBase64Url\n};\n", "// src/utils/jwt/jwa.ts\nvar AlgorithmTypes = /* @__PURE__ */ ((AlgorithmTypes2) => {\n  AlgorithmTypes2[\"HS256\"] = \"HS256\";\n  AlgorithmTypes2[\"HS384\"] = \"HS384\";\n  AlgorithmTypes2[\"HS512\"] = \"HS512\";\n  AlgorithmTypes2[\"RS256\"] = \"RS256\";\n  AlgorithmTypes2[\"RS384\"] = \"RS384\";\n  AlgorithmTypes2[\"RS512\"] = \"RS512\";\n  AlgorithmTypes2[\"PS256\"] = \"PS256\";\n  AlgorithmTypes2[\"PS384\"] = \"PS384\";\n  AlgorithmTypes2[\"PS512\"] = \"PS512\";\n  AlgorithmTypes2[\"ES256\"] = \"ES256\";\n  AlgorithmTypes2[\"ES384\"] = \"ES384\";\n  AlgorithmTypes2[\"ES512\"] = \"ES512\";\n  AlgorithmTypes2[\"EdDSA\"] = \"EdDSA\";\n  return AlgorithmTypes2;\n})(AlgorithmTypes || {});\nexport {\n  AlgorithmTypes\n};\n", "// src/utils/jwt/jws.ts\nimport { getRuntime<PERSON><PERSON> } from \"../../helper/adapter/index.js\";\nimport { decodeBase64 } from \"../encode.js\";\nimport { CryptoKeyUsage, JwtAlgorithmNotImplemented } from \"./types.js\";\nimport { utf8Encoder } from \"./utf8.js\";\nasync function signing(privateKey, alg, data) {\n  const algorithm = getKeyAlgorithm(alg);\n  const cryptoKey = await importPrivateKey(privateKey, algorithm);\n  return await crypto.subtle.sign(algorithm, cryptoKey, data);\n}\nasync function verifying(publicKey, alg, signature, data) {\n  const algorithm = getKeyAlgorithm(alg);\n  const cryptoKey = await importPublicKey(publicKey, algorithm);\n  return await crypto.subtle.verify(algorithm, cryptoKey, signature, data);\n}\nfunction pemToBinary(pem) {\n  return decodeBase64(pem.replace(/-+(BEGIN|END).*/g, \"\").replace(/\\s/g, \"\"));\n}\nasync function importPrivateKey(key, alg) {\n  if (!crypto.subtle || !crypto.subtle.importKey) {\n    throw new Error(\"`crypto.subtle.importKey` is undefined. JWT auth middleware requires it.\");\n  }\n  if (isCryptoKey(key)) {\n    if (key.type !== \"private\" && key.type !== \"secret\") {\n      throw new Error(\n        `unexpected key type: CryptoKey.type is ${key.type}, expected private or secret`\n      );\n    }\n    return key;\n  }\n  const usages = [CryptoKeyUsage.Sign];\n  if (typeof key === \"object\") {\n    return await crypto.subtle.importKey(\"jwk\", key, alg, false, usages);\n  }\n  if (key.includes(\"PRIVATE\")) {\n    return await crypto.subtle.importKey(\"pkcs8\", pemToBinary(key), alg, false, usages);\n  }\n  return await crypto.subtle.importKey(\"raw\", utf8Encoder.encode(key), alg, false, usages);\n}\nasync function importPublicKey(key, alg) {\n  if (!crypto.subtle || !crypto.subtle.importKey) {\n    throw new Error(\"`crypto.subtle.importKey` is undefined. JWT auth middleware requires it.\");\n  }\n  if (isCryptoKey(key)) {\n    if (key.type === \"public\" || key.type === \"secret\") {\n      return key;\n    }\n    key = await exportPublicJwkFrom(key);\n  }\n  if (typeof key === \"string\" && key.includes(\"PRIVATE\")) {\n    const privateKey = await crypto.subtle.importKey(\"pkcs8\", pemToBinary(key), alg, true, [\n      CryptoKeyUsage.Sign\n    ]);\n    key = await exportPublicJwkFrom(privateKey);\n  }\n  const usages = [CryptoKeyUsage.Verify];\n  if (typeof key === \"object\") {\n    return await crypto.subtle.importKey(\"jwk\", key, alg, false, usages);\n  }\n  if (key.includes(\"PUBLIC\")) {\n    return await crypto.subtle.importKey(\"spki\", pemToBinary(key), alg, false, usages);\n  }\n  return await crypto.subtle.importKey(\"raw\", utf8Encoder.encode(key), alg, false, usages);\n}\nasync function exportPublicJwkFrom(privateKey) {\n  if (privateKey.type !== \"private\") {\n    throw new Error(`unexpected key type: ${privateKey.type}`);\n  }\n  if (!privateKey.extractable) {\n    throw new Error(\"unexpected private key is unextractable\");\n  }\n  const jwk = await crypto.subtle.exportKey(\"jwk\", privateKey);\n  const { kty } = jwk;\n  const { alg, e, n } = jwk;\n  const { crv, x, y } = jwk;\n  return { kty, alg, e, n, crv, x, y, key_ops: [CryptoKeyUsage.Verify] };\n}\nfunction getKeyAlgorithm(name) {\n  switch (name) {\n    case \"HS256\":\n      return {\n        name: \"HMAC\",\n        hash: {\n          name: \"SHA-256\"\n        }\n      };\n    case \"HS384\":\n      return {\n        name: \"HMAC\",\n        hash: {\n          name: \"SHA-384\"\n        }\n      };\n    case \"HS512\":\n      return {\n        name: \"HMAC\",\n        hash: {\n          name: \"SHA-512\"\n        }\n      };\n    case \"RS256\":\n      return {\n        name: \"RSASSA-PKCS1-v1_5\",\n        hash: {\n          name: \"SHA-256\"\n        }\n      };\n    case \"RS384\":\n      return {\n        name: \"RSASSA-PKCS1-v1_5\",\n        hash: {\n          name: \"SHA-384\"\n        }\n      };\n    case \"RS512\":\n      return {\n        name: \"RSASSA-PKCS1-v1_5\",\n        hash: {\n          name: \"SHA-512\"\n        }\n      };\n    case \"PS256\":\n      return {\n        name: \"RSA-PSS\",\n        hash: {\n          name: \"SHA-256\"\n        },\n        saltLength: 32\n      };\n    case \"PS384\":\n      return {\n        name: \"RSA-PSS\",\n        hash: {\n          name: \"SHA-384\"\n        },\n        saltLength: 48\n      };\n    case \"PS512\":\n      return {\n        name: \"RSA-PSS\",\n        hash: {\n          name: \"SHA-512\"\n        },\n        saltLength: 64\n      };\n    case \"ES256\":\n      return {\n        name: \"ECDSA\",\n        hash: {\n          name: \"SHA-256\"\n        },\n        namedCurve: \"P-256\"\n      };\n    case \"ES384\":\n      return {\n        name: \"ECDSA\",\n        hash: {\n          name: \"SHA-384\"\n        },\n        namedCurve: \"P-384\"\n      };\n    case \"ES512\":\n      return {\n        name: \"ECDSA\",\n        hash: {\n          name: \"SHA-512\"\n        },\n        namedCurve: \"P-521\"\n      };\n    case \"EdDSA\":\n      return {\n        name: \"Ed25519\",\n        namedCurve: \"Ed25519\"\n      };\n    default:\n      throw new JwtAlgorithmNotImplemented(name);\n  }\n}\nfunction isCryptoKey(key) {\n  const runtime = getRuntimeKey();\n  if (runtime === \"node\" && !!crypto.webcrypto) {\n    return key instanceof crypto.webcrypto.CryptoKey;\n  }\n  return key instanceof CryptoKey;\n}\nexport {\n  signing,\n  verifying\n};\n", "// src/helper/adapter/index.ts\nvar env = (c, runtime) => {\n  const global = globalThis;\n  const globalEnv = global?.process?.env;\n  runtime ??= getRuntimeKey();\n  const runtimeEnvHandlers = {\n    bun: () => globalEnv,\n    node: () => globalEnv,\n    \"edge-light\": () => globalEnv,\n    deno: () => {\n      return Deno.env.toObject();\n    },\n    workerd: () => c.env,\n    fastly: () => ({}),\n    other: () => ({})\n  };\n  return runtimeEnvHandlers[runtime]();\n};\nvar knownUserAgents = {\n  deno: \"Deno\",\n  bun: \"Bun\",\n  workerd: \"Cloudflare-Workers\",\n  node: \"Node.js\"\n};\nvar getRuntimeKey = () => {\n  const global = globalThis;\n  const userAgentSupported = typeof navigator !== \"undefined\" && typeof navigator.userAgent === \"string\";\n  if (userAgentSupported) {\n    for (const [runtimeKey, userAgent] of Object.entries(knownUserAgents)) {\n      if (checkUserAgentEquals(userAgent)) {\n        return runtimeKey;\n      }\n    }\n  }\n  if (typeof global?.EdgeRuntime === \"string\") {\n    return \"edge-light\";\n  }\n  if (global?.fastly !== void 0) {\n    return \"fastly\";\n  }\n  if (global?.process?.release?.name === \"node\") {\n    return \"node\";\n  }\n  return \"other\";\n};\nvar checkUserAgentEquals = (platform) => {\n  const userAgent = navigator.userAgent;\n  return userAgent.startsWith(platform);\n};\nexport {\n  checkUserAgentEquals,\n  env,\n  getRuntimeKey,\n  knownUserAgents\n};\n", "// src/utils/jwt/types.ts\nvar JwtAlgorithmNotImplemented = class extends Error {\n  constructor(alg) {\n    super(`${alg} is not an implemented algorithm`);\n    this.name = \"JwtAlgorithmNotImplemented\";\n  }\n};\nvar JwtTokenInvalid = class extends Error {\n  constructor(token) {\n    super(`invalid JWT token: ${token}`);\n    this.name = \"JwtTokenInvalid\";\n  }\n};\nvar JwtTokenNotBefore = class extends Error {\n  constructor(token) {\n    super(`token (${token}) is being used before it's valid`);\n    this.name = \"JwtTokenNotBefore\";\n  }\n};\nvar JwtTokenExpired = class extends Error {\n  constructor(token) {\n    super(`token (${token}) expired`);\n    this.name = \"JwtTokenExpired\";\n  }\n};\nvar JwtTokenIssuedAt = class extends Error {\n  constructor(currentTimestamp, iat) {\n    super(\n      `Invalid \"iat\" claim, must be a valid number lower than \"${currentTimestamp}\" (iat: \"${iat}\")`\n    );\n    this.name = \"JwtTokenIssuedAt\";\n  }\n};\nvar JwtHeaderInvalid = class extends Error {\n  constructor(header) {\n    super(`jwt header is invalid: ${JSON.stringify(header)}`);\n    this.name = \"JwtHeaderInvalid\";\n  }\n};\nvar JwtHeaderRequiresKid = class extends Error {\n  constructor(header) {\n    super(`required \"kid\" in jwt header: ${JSON.stringify(header)}`);\n    this.name = \"JwtHeaderRequiresKid\";\n  }\n};\nvar JwtTokenSignatureMismatched = class extends Error {\n  constructor(token) {\n    super(`token(${token}) signature mismatched`);\n    this.name = \"JwtTokenSignatureMismatched\";\n  }\n};\nvar CryptoKeyUsage = /* @__PURE__ */ ((CryptoKeyUsage2) => {\n  CryptoKeyUsage2[\"Encrypt\"] = \"encrypt\";\n  CryptoKeyUsage2[\"Decrypt\"] = \"decrypt\";\n  CryptoKeyUsage2[\"Sign\"] = \"sign\";\n  CryptoKeyUsage2[\"Verify\"] = \"verify\";\n  CryptoKeyUsage2[\"DeriveKey\"] = \"deriveKey\";\n  CryptoKeyUsage2[\"DeriveBits\"] = \"deriveBits\";\n  CryptoKeyUsage2[\"WrapKey\"] = \"wrapKey\";\n  CryptoKeyUsage2[\"UnwrapKey\"] = \"unwrapKey\";\n  return CryptoKeyUsage2;\n})(CryptoKeyUsage || {});\nexport {\n  CryptoKeyUsage,\n  JwtAlgorithmNotImplemented,\n  JwtHeaderInvalid,\n  JwtHeaderRequiresKid,\n  JwtTokenExpired,\n  JwtTokenInvalid,\n  JwtTokenIssuedAt,\n  JwtTokenNotBefore,\n  JwtTokenSignatureMismatched\n};\n", "// src/utils/jwt/utf8.ts\nvar utf8Encoder = new TextEncoder();\nvar utf8Decoder = new TextDecoder();\nexport {\n  utf8Decoder,\n  utf8Encoder\n};\n", "/*\n Copyright (c) 2012 N<PERSON><PERSON> <nevins.bartolo<PERSON><EMAIL>>\n Copyright (c) 2012 <PERSON> <<EMAIL>>\n Copyright (c) 2025 <PERSON>z <<EMAIL>>\n\n Redistribution and use in source and binary forms, with or without\n modification, are permitted provided that the following conditions\n are met:\n 1. Redistributions of source code must retain the above copyright\n notice, this list of conditions and the following disclaimer.\n 2. Redistributions in binary form must reproduce the above copyright\n notice, this list of conditions and the following disclaimer in the\n documentation and/or other materials provided with the distribution.\n 3. The name of the author may not be used to endorse or promote products\n derived from this software without specific prior written permission.\n\n THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\n IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\n OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\n INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n// The Node.js crypto module is used as a fallback for the Web Crypto API. When\n// building for the browser, inclusion of the crypto module should be disabled,\n// which the package hints at in its package.json for bundlers that support it.\nimport nodeCrypto from \"crypto\";\n\n/**\n * The random implementation to use as a fallback.\n * @type {?function(number):!Array.<number>}\n * @inner\n */\nvar randomFallback = null;\n\n/**\n * Generates cryptographically secure random bytes.\n * @function\n * @param {number} len Bytes length\n * @returns {!Array.<number>} Random bytes\n * @throws {Error} If no random implementation is available\n * @inner\n */\nfunction randomBytes(len) {\n  // Web Crypto API. Globally available in the browser and in Node.js >=23.\n  try {\n    return crypto.getRandomValues(new Uint8Array(len));\n  } catch {}\n  // Node.js crypto module for non-browser environments.\n  try {\n    return nodeCrypto.randomBytes(len);\n  } catch {}\n  // Custom fallback specified with `setRandomFallback`.\n  if (!randomFallback) {\n    throw Error(\n      \"Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative\",\n    );\n  }\n  return randomFallback(len);\n}\n\n/**\n * Sets the pseudo random number generator to use as a fallback if neither node's `crypto` module nor the Web Crypto\n *  API is available. Please note: It is highly important that the PRNG used is cryptographically secure and that it\n *  is seeded properly!\n * @param {?function(number):!Array.<number>} random Function taking the number of bytes to generate as its\n *  sole argument, returning the corresponding array of cryptographically secure random byte values.\n * @see http://nodejs.org/api/crypto.html\n * @see http://www.w3.org/TR/WebCryptoAPI/\n */\nexport function setRandomFallback(random) {\n  randomFallback = random;\n}\n\n/**\n * Synchronously generates a salt.\n * @param {number=} rounds Number of rounds to use, defaults to 10 if omitted\n * @param {number=} seed_length Not supported.\n * @returns {string} Resulting salt\n * @throws {Error} If a random fallback is required but not set\n */\nexport function genSaltSync(rounds, seed_length) {\n  rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;\n  if (typeof rounds !== \"number\")\n    throw Error(\n      \"Illegal arguments: \" + typeof rounds + \", \" + typeof seed_length,\n    );\n  if (rounds < 4) rounds = 4;\n  else if (rounds > 31) rounds = 31;\n  var salt = [];\n  salt.push(\"$2b$\");\n  if (rounds < 10) salt.push(\"0\");\n  salt.push(rounds.toString());\n  salt.push(\"$\");\n  salt.push(base64_encode(randomBytes(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\n  return salt.join(\"\");\n}\n\n/**\n * Asynchronously generates a salt.\n * @param {(number|function(Error, string=))=} rounds Number of rounds to use, defaults to 10 if omitted\n * @param {(number|function(Error, string=))=} seed_length Not supported.\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting salt\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nexport function genSalt(rounds, seed_length, callback) {\n  if (typeof seed_length === \"function\")\n    (callback = seed_length), (seed_length = undefined); // Not supported.\n  if (typeof rounds === \"function\") (callback = rounds), (rounds = undefined);\n  if (typeof rounds === \"undefined\") rounds = GENSALT_DEFAULT_LOG2_ROUNDS;\n  else if (typeof rounds !== \"number\")\n    throw Error(\"illegal arguments: \" + typeof rounds);\n\n  function _async(callback) {\n    nextTick(function () {\n      // Pretty thin, but salting is fast enough\n      try {\n        callback(null, genSaltSync(rounds));\n      } catch (err) {\n        callback(err);\n      }\n    });\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Synchronously generates a hash for the given password.\n * @param {string} password Password to hash\n * @param {(number|string)=} salt Salt length to generate or salt to use, default to 10\n * @returns {string} Resulting hash\n */\nexport function hashSync(password, salt) {\n  if (typeof salt === \"undefined\") salt = GENSALT_DEFAULT_LOG2_ROUNDS;\n  if (typeof salt === \"number\") salt = genSaltSync(salt);\n  if (typeof password !== \"string\" || typeof salt !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password + \", \" + typeof salt);\n  return _hash(password, salt);\n}\n\n/**\n * Asynchronously generates a hash for the given password.\n * @param {string} password Password to hash\n * @param {number|string} salt Salt length to generate or salt to use\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash\n * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nexport function hash(password, salt, callback, progressCallback) {\n  function _async(callback) {\n    if (typeof password === \"string\" && typeof salt === \"number\")\n      genSalt(salt, function (err, salt) {\n        _hash(password, salt, callback, progressCallback);\n      });\n    else if (typeof password === \"string\" && typeof salt === \"string\")\n      _hash(password, salt, callback, progressCallback);\n    else\n      nextTick(\n        callback.bind(\n          this,\n          Error(\"Illegal arguments: \" + typeof password + \", \" + typeof salt),\n        ),\n      );\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Compares two strings of the same length in constant time.\n * @param {string} known Must be of the correct length\n * @param {string} unknown Must be the same length as `known`\n * @returns {boolean}\n * @inner\n */\nfunction safeStringCompare(known, unknown) {\n  var diff = known.length ^ unknown.length;\n  for (var i = 0; i < known.length; ++i) {\n    diff |= known.charCodeAt(i) ^ unknown.charCodeAt(i);\n  }\n  return diff === 0;\n}\n\n/**\n * Synchronously tests a password against a hash.\n * @param {string} password Password to compare\n * @param {string} hash Hash to test against\n * @returns {boolean} true if matching, otherwise false\n * @throws {Error} If an argument is illegal\n */\nexport function compareSync(password, hash) {\n  if (typeof password !== \"string\" || typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password + \", \" + typeof hash);\n  if (hash.length !== 60) return false;\n  return safeStringCompare(\n    hashSync(password, hash.substring(0, hash.length - 31)),\n    hash,\n  );\n}\n\n/**\n * Asynchronously tests a password against a hash.\n * @param {string} password Password to compare\n * @param {string} hashValue Hash to test against\n * @param {function(Error, boolean)=} callback Callback receiving the error, if any, otherwise the result\n * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nexport function compare(password, hashValue, callback, progressCallback) {\n  function _async(callback) {\n    if (typeof password !== \"string\" || typeof hashValue !== \"string\") {\n      nextTick(\n        callback.bind(\n          this,\n          Error(\n            \"Illegal arguments: \" + typeof password + \", \" + typeof hashValue,\n          ),\n        ),\n      );\n      return;\n    }\n    if (hashValue.length !== 60) {\n      nextTick(callback.bind(this, null, false));\n      return;\n    }\n    hash(\n      password,\n      hashValue.substring(0, 29),\n      function (err, comp) {\n        if (err) callback(err);\n        else callback(null, safeStringCompare(comp, hashValue));\n      },\n      progressCallback,\n    );\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Gets the number of rounds used to encrypt the specified hash.\n * @param {string} hash Hash to extract the used number of rounds from\n * @returns {number} Number of rounds used\n * @throws {Error} If `hash` is not a string\n */\nexport function getRounds(hash) {\n  if (typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof hash);\n  return parseInt(hash.split(\"$\")[2], 10);\n}\n\n/**\n * Gets the salt portion from a hash. Does not validate the hash.\n * @param {string} hash Hash to extract the salt from\n * @returns {string} Extracted salt part\n * @throws {Error} If `hash` is not a string or otherwise invalid\n */\nexport function getSalt(hash) {\n  if (typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof hash);\n  if (hash.length !== 60)\n    throw Error(\"Illegal hash length: \" + hash.length + \" != 60\");\n  return hash.substring(0, 29);\n}\n\n/**\n * Tests if a password will be truncated when hashed, that is its length is\n * greater than 72 bytes when converted to UTF-8.\n * @param {string} password The password to test\n * @returns {boolean} `true` if truncated, otherwise `false`\n */\nexport function truncates(password) {\n  if (typeof password !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password);\n  return utf8Length(password) > 72;\n}\n\n/**\n * Continues with the callback on the next tick.\n * @function\n * @param {function(...[*])} callback Callback to execute\n * @inner\n */\nvar nextTick =\n  typeof process !== \"undefined\" &&\n  process &&\n  typeof process.nextTick === \"function\"\n    ? typeof setImmediate === \"function\"\n      ? setImmediate\n      : process.nextTick\n    : setTimeout;\n\n/** Calculates the byte length of a string encoded as UTF8. */\nfunction utf8Length(string) {\n  var len = 0,\n    c = 0;\n  for (var i = 0; i < string.length; ++i) {\n    c = string.charCodeAt(i);\n    if (c < 128) len += 1;\n    else if (c < 2048) len += 2;\n    else if (\n      (c & 0xfc00) === 0xd800 &&\n      (string.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      ++i;\n      len += 4;\n    } else len += 3;\n  }\n  return len;\n}\n\n/** Converts a string to an array of UTF8 bytes. */\nfunction utf8Array(string) {\n  var offset = 0,\n    c1,\n    c2;\n  var buffer = new Array(utf8Length(string));\n  for (var i = 0, k = string.length; i < k; ++i) {\n    c1 = string.charCodeAt(i);\n    if (c1 < 128) {\n      buffer[offset++] = c1;\n    } else if (c1 < 2048) {\n      buffer[offset++] = (c1 >> 6) | 192;\n      buffer[offset++] = (c1 & 63) | 128;\n    } else if (\n      (c1 & 0xfc00) === 0xd800 &&\n      ((c2 = string.charCodeAt(i + 1)) & 0xfc00) === 0xdc00\n    ) {\n      c1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff);\n      ++i;\n      buffer[offset++] = (c1 >> 18) | 240;\n      buffer[offset++] = ((c1 >> 12) & 63) | 128;\n      buffer[offset++] = ((c1 >> 6) & 63) | 128;\n      buffer[offset++] = (c1 & 63) | 128;\n    } else {\n      buffer[offset++] = (c1 >> 12) | 224;\n      buffer[offset++] = ((c1 >> 6) & 63) | 128;\n      buffer[offset++] = (c1 & 63) | 128;\n    }\n  }\n  return buffer;\n}\n\n// A base64 implementation for the bcrypt algorithm. This is partly non-standard.\n\n/**\n * bcrypt's own non-standard base64 dictionary.\n * @type {!Array.<string>}\n * @const\n * @inner\n **/\nvar BASE64_CODE =\n  \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\"\");\n\n/**\n * @type {!Array.<number>}\n * @const\n * @inner\n **/\nvar BASE64_INDEX = [\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63,\n  -1, -1, -1, -1, -1, -1, -1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,\n  16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, -1, -1, -1, -1, -1, -1, 28,\n  29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,\n  48, 49, 50, 51, 52, 53, -1, -1, -1, -1, -1,\n];\n\n/**\n * Encodes a byte array to base64 with up to len bytes of input.\n * @param {!Array.<number>} b Byte array\n * @param {number} len Maximum input length\n * @returns {string}\n * @inner\n */\nfunction base64_encode(b, len) {\n  var off = 0,\n    rs = [],\n    c1,\n    c2;\n  if (len <= 0 || len > b.length) throw Error(\"Illegal len: \" + len);\n  while (off < len) {\n    c1 = b[off++] & 0xff;\n    rs.push(BASE64_CODE[(c1 >> 2) & 0x3f]);\n    c1 = (c1 & 0x03) << 4;\n    if (off >= len) {\n      rs.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = b[off++] & 0xff;\n    c1 |= (c2 >> 4) & 0x0f;\n    rs.push(BASE64_CODE[c1 & 0x3f]);\n    c1 = (c2 & 0x0f) << 2;\n    if (off >= len) {\n      rs.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = b[off++] & 0xff;\n    c1 |= (c2 >> 6) & 0x03;\n    rs.push(BASE64_CODE[c1 & 0x3f]);\n    rs.push(BASE64_CODE[c2 & 0x3f]);\n  }\n  return rs.join(\"\");\n}\n\n/**\n * Decodes a base64 encoded string to up to len bytes of output.\n * @param {string} s String to decode\n * @param {number} len Maximum output length\n * @returns {!Array.<number>}\n * @inner\n */\nfunction base64_decode(s, len) {\n  var off = 0,\n    slen = s.length,\n    olen = 0,\n    rs = [],\n    c1,\n    c2,\n    c3,\n    c4,\n    o,\n    code;\n  if (len <= 0) throw Error(\"Illegal len: \" + len);\n  while (off < slen - 1 && olen < len) {\n    code = s.charCodeAt(off++);\n    c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    code = s.charCodeAt(off++);\n    c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    if (c1 == -1 || c2 == -1) break;\n    o = (c1 << 2) >>> 0;\n    o |= (c2 & 0x30) >> 4;\n    rs.push(String.fromCharCode(o));\n    if (++olen >= len || off >= slen) break;\n    code = s.charCodeAt(off++);\n    c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    if (c3 == -1) break;\n    o = ((c2 & 0x0f) << 4) >>> 0;\n    o |= (c3 & 0x3c) >> 2;\n    rs.push(String.fromCharCode(o));\n    if (++olen >= len || off >= slen) break;\n    code = s.charCodeAt(off++);\n    c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    o = ((c3 & 0x03) << 6) >>> 0;\n    o |= c4;\n    rs.push(String.fromCharCode(o));\n    ++olen;\n  }\n  var res = [];\n  for (off = 0; off < olen; off++) res.push(rs[off].charCodeAt(0));\n  return res;\n}\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar BCRYPT_SALT_LEN = 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar GENSALT_DEFAULT_LOG2_ROUNDS = 10;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar BLOWFISH_NUM_ROUNDS = 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar MAX_EXECUTION_TIME = 100;\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar P_ORIG = [\n  0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344, 0xa4093822, 0x299f31d0,\n  0x082efa98, 0xec4e6c89, 0x452821e6, 0x38d01377, 0xbe5466cf, 0x34e90c6c,\n  0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5, 0xb5470917, 0x9216d5d9, 0x8979fb1b,\n];\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar S_ORIG = [\n  0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7, 0xb8e1afed, 0x6a267e96,\n  0xba7c9045, 0xf12c7f99, 0x24a19947, 0xb3916cf7, 0x0801f2e2, 0x858efc16,\n  0x636920d8, 0x71574e69, 0xa458fea3, 0xf4933d7e, 0x0d95748f, 0x728eb658,\n  0x718bcd58, 0x82154aee, 0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013,\n  0xc5d1b023, 0x286085f0, 0xca417918, 0xb8db38ef, 0x8e79dcb0, 0x603a180e,\n  0x6c9e0e8b, 0xb01e8a3e, 0xd71577c1, 0xbd314b27, 0x78af2fda, 0x55605c60,\n  0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440, 0x55ca396a, 0x2aab10b6,\n  0xb4cc5c34, 0x1141e8ce, 0xa15486af, 0x7c72e993, 0xb3ee1411, 0x636fbc2a,\n  0x2ba9c55d, 0x741831f6, 0xce5c3e16, 0x9b87931e, 0xafd6ba33, 0x6c24cf5c,\n  0x7a325381, 0x28958677, 0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\n  0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032, 0xef845d5d, 0xe98575b1,\n  0xdc262302, 0xeb651b88, 0x23893e81, 0xd396acc5, 0x0f6d6ff3, 0x83f44239,\n  0x2e0b4482, 0xa4842004, 0x69c8f04a, 0x9e1f9b5e, 0x21c66842, 0xf6e96c9a,\n  0x670c9c61, 0xabd388f0, 0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3,\n  0x6eef0b6c, 0x137a3be4, 0xba3bf050, 0x7efb2a98, 0xa1f1651d, 0x39af0176,\n  0x66ca593e, 0x82430e88, 0x8cee8619, 0x456f9fb4, 0x7d84a5c3, 0x3b8b5ebe,\n  0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6, 0x4ed3aa62, 0x363f7706,\n  0x1bfedf72, 0x429b023d, 0x37d0d724, 0xd00a1248, 0xdb0fead3, 0x49f1c09b,\n  0x075372c9, 0x80991b7b, 0x25d479d8, 0xf6e8def7, 0xe3fe501a, 0xb6794c3b,\n  0x976ce0bd, 0x04c006ba, 0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\n  0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f, 0x6dfc511f, 0x9b30952c,\n  0xcc814544, 0xaf5ebd09, 0xbee3d004, 0xde334afd, 0x660f2807, 0x192e4bb3,\n  0xc0cba857, 0x45c8740f, 0xd20b5f39, 0xb9d3fbdb, 0x5579c0bd, 0x1a60320a,\n  0xd6a100c6, 0x402c7279, 0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8,\n  0x3c7516df, 0xfd616b15, 0x2f501ec8, 0xad0552ab, 0x323db5fa, 0xfd238760,\n  0x53317b48, 0x3e00df82, 0x9e5c57bb, 0xca6f8ca0, 0x1a87562e, 0xdf1769db,\n  0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573, 0x695b27b0, 0xbbca58c8,\n  0xe1ffa35d, 0xb8f011a0, 0x10fa3d98, 0xfd2183b8, 0x4afcb56c, 0x2dd1d35b,\n  0x9a53e479, 0xb6f84565, 0xd28e49bc, 0x4bfb9790, 0xe1ddf2da, 0xa4cb7e33,\n  0x62fb1341, 0xcee4c6e8, 0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\n  0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0, 0xd08ed1d0, 0xafc725e0,\n  0x8e3c5b2f, 0x8e7594b7, 0x8ff6e2fb, 0xf2122b64, 0x8888b812, 0x900df01c,\n  0x4fad5ea0, 0x688fc31c, 0xd1cff191, 0xb3a8c1ad, 0x2f2f2218, 0xbe0e1777,\n  0xea752dfe, 0x8b021fa1, 0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299,\n  0xb4a84fe0, 0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9, 0x165fa266, 0x80957705,\n  0x93cc7314, 0x211a1477, 0xe6ad2065, 0x77b5fa86, 0xc75442f5, 0xfb9d35cf,\n  0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49, 0x00250e2d, 0x2071b35e,\n  0x226800bb, 0x57b8e0af, 0x2464369b, 0xf009b91e, 0x5563911d, 0x59dfa6aa,\n  0x78c14389, 0xd95a537f, 0x207d5ba2, 0x02e5b9c5, 0x83260376, 0x6295cfa9,\n  0x11c81968, 0x4e734a41, 0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\n  0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400, 0x08ba6fb5, 0x571be91f,\n  0xf296ec6b, 0x2a0dd915, 0xb6636521, 0xe7b9f9b6, 0xff34052e, 0xc5855664,\n  0x53b02d5d, 0xa99f8fa1, 0x08ba4799, 0x6e85076a, 0x4b7a70e9, 0xb5b32944,\n  0xdb75092e, 0xc4192623, 0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266,\n  0xecaa8c71, 0x699a17ff, 0x5664526c, 0xc2b19ee1, 0x193602a5, 0x75094c29,\n  0xa0591340, 0xe4183a3e, 0x3f54989a, 0x5b429d65, 0x6b8fe4d6, 0x99f73fd6,\n  0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1, 0x4cdd2086, 0x8470eb26,\n  0x6382e9c6, 0x021ecc5e, 0x09686b3f, 0x3ebaefc9, 0x3c971814, 0x6b6a70a1,\n  0x687f3584, 0x52a0e286, 0xb79c5305, 0xaa500737, 0x3e07841c, 0x7fdeae5c,\n  0x8e7d44ec, 0x5716f2b8, 0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\n  0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd, 0xd19113f9, 0x7ca92ff6,\n  0x94324773, 0x22f54701, 0x3ae5e581, 0x37c2dadc, 0xc8b57634, 0x9af3dda7,\n  0xa9446146, 0x0fd0030e, 0xecc8c73e, 0xa4751e41, 0xe238cd99, 0x3bea0e2f,\n  0x3280bba1, 0x183eb331, 0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf,\n  0x2cb81290, 0x24977c79, 0x5679b072, 0xbcaf89af, 0xde9a771f, 0xd9930810,\n  0xb38bae12, 0xdccf3f2e, 0x5512721f, 0x2e6b7124, 0x501adde6, 0x9f84cd87,\n  0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c, 0xec7aec3a, 0xdb851dfa,\n  0x63094366, 0xc464c3d2, 0xef1c1847, 0x3215d908, 0xdd433b37, 0x24c2ba16,\n  0x12a14d43, 0x2a65c451, 0x50940002, 0x133ae4dd, 0x71dff89e, 0x10314e55,\n  0x81ac77d6, 0x5f11199b, 0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\n  0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e, 0x86e34570, 0xeae96fb1,\n  0x860e5e0a, 0x5a3e2ab3, 0x771fe71c, 0x4e3d06fa, 0x2965dcb9, 0x99e71d0f,\n  0x803e89d6, 0x5266c825, 0x2e4cc978, 0x9c10b36a, 0xc6150eba, 0x94e2ea78,\n  0xa5fc3c53, 0x1e0a2df4, 0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960,\n  0x5223a708, 0xf71312b6, 0xebadfe6e, 0xeac31f66, 0xe3bc4595, 0xa67bc883,\n  0xb17f37d1, 0x018cff28, 0xc332ddef, 0xbe6c5aa5, 0x65582185, 0x68ab9802,\n  0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84, 0x1521b628, 0x29076170,\n  0xecdd4775, 0x619f1510, 0x13cca830, 0xeb61bd96, 0x0334fe1e, 0xaa0363cf,\n  0xb5735c90, 0x4c70a239, 0xd59e9e0b, 0xcbaade14, 0xeecc86bc, 0x60622ca7,\n  0x9cab5cab, 0xb2f3846e, 0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\n  0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7, 0x9b540b19, 0x875fa099,\n  0x95f7997e, 0x623d7da8, 0xf837889a, 0x97e32d77, 0x11ed935f, 0x16681281,\n  0x0e358829, 0xc7e61fd6, 0x96dedfa1, 0x7858ba99, 0x57f584a5, 0x1b227263,\n  0x9b83c3ff, 0x1ac24696, 0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128,\n  0x58ebf2ef, 0x34c6ffea, 0xfe28ed61, 0xee7c3c73, 0x5d4a14d9, 0xe864b7e3,\n  0x42105d14, 0x203e13e0, 0x45eee2b6, 0xa3aaabea, 0xdb6c4f15, 0xfacb4fd0,\n  0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105, 0xd81e799e, 0x86854dc7,\n  0xe44b476a, 0x3d816250, 0xcf62a1f2, 0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3,\n  0x7f1524c3, 0x69cb7492, 0x47848a0b, 0x5692b285, 0x095bbf00, 0xad19489d,\n  0x1462b174, 0x23820e00, 0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\n  0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb, 0x7cde3759, 0xcbee7460,\n  0x4085f2a7, 0xce77326e, 0xa6078084, 0x19f8509e, 0xe8efd855, 0x61d99735,\n  0xa969a7aa, 0xc50c06c2, 0x5a04abfc, 0x800bcadc, 0x9e447a2e, 0xc3453484,\n  0xfdd56705, 0x0e1e9ec9, 0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340,\n  0xc5c43465, 0x713e38d8, 0x3d28f89e, 0xf16dff20, 0x153e21e7, 0x8fb03d4a,\n  0xe6e39f2b, 0xdb83adf7, 0xe93d5a68, 0x948140f7, 0xf64c261c, 0x94692934,\n  0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068, 0xd4082471, 0x3320f46a,\n  0x43b7d4b7, 0x500061af, 0x1e39f62e, 0x97244546, 0x14214f74, 0xbf8b8840,\n  0x4d95fc1d, 0x96b591af, 0x70f4ddd3, 0x66a02f45, 0xbfbc09ec, 0x03bd9785,\n  0x7fac6dd0, 0x31cb8504, 0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\n  0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb, 0x68dc1462, 0xd7486900,\n  0x680ec0a4, 0x27a18dee, 0x4f3ffea2, 0xe887ad8c, 0xb58ce006, 0x7af4d6b6,\n  0xaace1e7c, 0xd3375fec, 0xce78a399, 0x406b2a42, 0x20fe9e35, 0xd9f385b9,\n  0xee39d7ab, 0x3b124e8b, 0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2,\n  0x3a6efa74, 0xdd5b4332, 0x6841e7f7, 0xca7820fb, 0xfb0af54e, 0xd8feb397,\n  0x454056ac, 0xba489527, 0x55533a3a, 0x20838d87, 0xfe6ba9b7, 0xd096954b,\n  0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33, 0xa62a4a56, 0x3f3125f9,\n  0x5ef47e1c, 0x9029317c, 0xfdf8e802, 0x04272f70, 0x80bb155c, 0x05282ce3,\n  0x95c11548, 0xe4c66d22, 0x48c1133f, 0xc70f86dc, 0x07f9c9ee, 0x41041f0f,\n  0x404779a4, 0x5d886e17, 0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\n  0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b, 0x0e12b4c2, 0x02e1329e,\n  0xaf664fd1, 0xcad18115, 0x6b2395e0, 0x333e92e1, 0x3b240b62, 0xeebeb922,\n  0x85b2a20e, 0xe6ba0d99, 0xde720c8c, 0x2da2f728, 0xd0127845, 0x95b794fd,\n  0x647d0862, 0xe7ccf5f0, 0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e,\n  0x0a476341, 0x992eff74, 0x3a6f6eab, 0xf4f8fd37, 0xa812dc60, 0xa1ebddf8,\n  0x991be14c, 0xdb6e6b0d, 0xc67b5510, 0x6d672c37, 0x2765d43b, 0xdcd0e804,\n  0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b, 0x667b9ffb, 0xcedb7d9c,\n  0xa091cf0b, 0xd9155ea3, 0xbb132f88, 0x515bad24, 0x7b9479bf, 0x763bd6eb,\n  0x37392eb3, 0xcc115979, 0x8026e297, 0xf42e312d, 0x6842ada7, 0xc66a2b3b,\n  0x12754ccc, 0x782ef11c, 0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\n  0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9, 0x44421659, 0x0a121386,\n  0xd90cec6e, 0xd5abea2a, 0x64af674e, 0xda86a85f, 0xbebfe988, 0x64e4c3fe,\n  0x9dbc8057, 0xf0f7c086, 0x60787bf8, 0x6003604d, 0xd1fd8346, 0xf6381fb0,\n  0x7745ae04, 0xd736fccc, 0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f,\n  0x77a057be, 0xbde8ae24, 0x55464299, 0xbf582e61, 0x4e58f48f, 0xf2ddfda2,\n  0xf474ef38, 0x8789bdc2, 0x5366f9c3, 0xc8b38e74, 0xb475f255, 0x46fcd9b9,\n  0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2, 0x466e598e, 0x20b45770,\n  0x8cd55591, 0xc902de4c, 0xb90bace1, 0xbb8205d0, 0x11a86248, 0x7574a99e,\n  0xb77f19b6, 0xe0a9dc09, 0x662d09a1, 0xc4324633, 0xe85a1f02, 0x09f0be8c,\n  0x4a99a025, 0x1d6efe10, 0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\n  0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52, 0x50115e01, 0xa70683fa,\n  0xa002b5c4, 0x0de6d027, 0x9af88c27, 0x773f8641, 0xc3604c06, 0x61a806b5,\n  0xf0177a28, 0xc0f586e0, 0x006058aa, 0x30dc7d62, 0x11e69ed7, 0x2338ea63,\n  0x53c2dd94, 0xc2c21634, 0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76,\n  0x6f05e409, 0x4b7c0188, 0x39720a3d, 0x7c927c24, 0x86e3725f, 0x724d9db9,\n  0x1ac15bb4, 0xd39eb8fc, 0xed545578, 0x08fca5b5, 0xd83d7cd3, 0x4dad0fc4,\n  0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c, 0x6fd5c7e7, 0x56e14ec4,\n  0x362abfce, 0xddc6c837, 0xd79a3234, 0x92638212, 0x670efa8e, 0x406000e0,\n  0x3a39ce37, 0xd3faf5cf, 0xabc27737, 0x5ac52d1b, 0x5cb0679e, 0x4fa33742,\n  0xd3822740, 0x99bc9bbe, 0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\n  0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4, 0x5748ab2f, 0xbc946e79,\n  0xc6a376d2, 0x6549c2c8, 0x530ff8ee, 0x468dde7d, 0xd5730a1d, 0x4cd04dc6,\n  0x2939bbdb, 0xa9ba4650, 0xac9526e8, 0xbe5ee304, 0xa1fad5f0, 0x6a2d519a,\n  0x63ef8ce2, 0x9a86ee22, 0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4,\n  0x83c061ba, 0x9be96a4d, 0x8fe51550, 0xba645bd6, 0x2826a2f9, 0xa73a3ae1,\n  0x4ba99586, 0xef5562e9, 0xc72fefd3, 0xf752f7da, 0x3f046f69, 0x77fa0a59,\n  0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593, 0xe990fd5a, 0x9e34d797,\n  0x2cf0b7d9, 0x022b8b51, 0x96d5ac3a, 0x017da67d, 0xd1cf3ed6, 0x7c7d2d28,\n  0x1f9f25cf, 0xadf2b89b, 0x5ad6b472, 0x5a88f54c, 0xe029ac71, 0xe019a5e6,\n  0x47b0acfd, 0xed93fa9b, 0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\n  0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c, 0x15056dd4, 0x88f46dba,\n  0x03a16125, 0x0564f0bd, 0xc3eb9e15, 0x3c9057a2, 0x97271aec, 0xa93a072a,\n  0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb, 0x26dcf319, 0x7533d928, 0xb155fdf5,\n  0x03563482, 0x8aba3cbb, 0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f,\n  0x4de81751, 0x3830dc8e, 0x379d5862, 0x9320f991, 0xea7a90c2, 0xfb3e7bce,\n  0x5121ce64, 0x774fbe32, 0xa8b6e37e, 0xc3293d46, 0x48de5369, 0x6413e680,\n  0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166, 0xb39a460a, 0x6445c0dd,\n  0x586cdecf, 0x1c20c8ae, 0x5bbef7dd, 0x1b588d40, 0xccd2017f, 0x6bb4e3bb,\n  0xdda26a7e, 0x3a59ff45, 0x3e350a44, 0xbcb4cdd5, 0x72eacea8, 0xfa6484bb,\n  0x8d6612ae, 0xbf3c6f47, 0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\n  0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d, 0x4040cb08, 0x4eb4e2cc,\n  0x34d2466a, 0x0115af84, 0xe1b00428, 0x95983a1d, 0x06b89fb4, 0xce6ea048,\n  0x6f3f3b82, 0x3520ab82, 0x011a1d4b, 0x277227f8, 0x611560b1, 0xe7933fdc,\n  0xbb3a792b, 0x344525bd, 0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9,\n  0xe01cc87e, 0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7, 0x1a908749, 0xd44fbd9a,\n  0xd0dadecb, 0xd50ada38, 0x0339c32a, 0xc6913667, 0x8df9317c, 0xe0b12b4f,\n  0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c, 0xbf97222c, 0x15e6fc2a,\n  0x0f91fc71, 0x9b941525, 0xfae59361, 0xceb69ceb, 0xc2a86459, 0x12baa8d1,\n  0xb6c1075e, 0xe3056a0c, 0x10d25065, 0xcb03a442, 0xe0ec6e0e, 0x1698db3b,\n  0x4c98a0be, 0x3278e964, 0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\n  0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8, 0xdf359f8d, 0x9b992f2e,\n  0xe60b6f47, 0x0fe3f11d, 0xe54cda54, 0x1edad891, 0xce6279cf, 0xcd3e7e6f,\n  0x1618b166, 0xfd2c1d05, 0x848fd2c5, 0xf6fb2299, 0xf523f357, 0xa6327623,\n  0x93a83531, 0x56cccd02, 0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc,\n  0xde966292, 0x81b949d0, 0x4c50901b, 0x71c65614, 0xe6c6c7bd, 0x327a140a,\n  0x45e1d006, 0xc3f27b9a, 0xc9aa53fd, 0x62a80f00, 0xbb25bfe2, 0x35bdd2f6,\n  0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b, 0x53113ec0, 0x1640e3d3,\n  0x38abbd60, 0x2547adf0, 0xba38209c, 0xf746ce76, 0x77afa1c5, 0x20756060,\n  0x85cbfe4e, 0x8ae88dd8, 0x7aaaf9b0, 0x4cf9aa7e, 0x1948c25c, 0x02fb8a8c,\n  0x01c36ae4, 0xd6ebe1f9, 0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\n  0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6,\n];\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar C_ORIG = [\n  0x4f727068, 0x65616e42, 0x65686f6c, 0x64657253, 0x63727944, 0x6f756274,\n];\n\n/**\n * @param {Array.<number>} lr\n * @param {number} off\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @returns {Array.<number>}\n * @inner\n */\nfunction _encipher(lr, off, P, S) {\n  // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\n  var n,\n    l = lr[off],\n    r = lr[off + 1];\n\n  l ^= P[0];\n\n  /*\n    for (var i=0, k=BLOWFISH_NUM_ROUNDS-2; i<=k;)\n        // Feistel substitution on left word\n        n  = S[l >>> 24],\n        n += S[0x100 | ((l >> 16) & 0xff)],\n        n ^= S[0x200 | ((l >> 8) & 0xff)],\n        n += S[0x300 | (l & 0xff)],\n        r ^= n ^ P[++i],\n        // Feistel substitution on right word\n        n  = S[r >>> 24],\n        n += S[0x100 | ((r >> 16) & 0xff)],\n        n ^= S[0x200 | ((r >> 8) & 0xff)],\n        n += S[0x300 | (r & 0xff)],\n        l ^= n ^ P[++i];\n    */\n\n  //The following is an unrolled version of the above loop.\n  //Iteration 0\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[1];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[2];\n  //Iteration 1\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[3];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[4];\n  //Iteration 2\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[5];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[6];\n  //Iteration 3\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[7];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[8];\n  //Iteration 4\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[9];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[10];\n  //Iteration 5\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[11];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[12];\n  //Iteration 6\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[13];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[14];\n  //Iteration 7\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[15];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[16];\n\n  lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\n  lr[off + 1] = l;\n  return lr;\n}\n\n/**\n * @param {Array.<number>} data\n * @param {number} offp\n * @returns {{key: number, offp: number}}\n * @inner\n */\nfunction _streamtoword(data, offp) {\n  for (var i = 0, word = 0; i < 4; ++i)\n    (word = (word << 8) | (data[offp] & 0xff)),\n      (offp = (offp + 1) % data.length);\n  return { key: word, offp: offp };\n}\n\n/**\n * @param {Array.<number>} key\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @inner\n */\nfunction _key(key, P, S) {\n  var offset = 0,\n    lr = [0, 0],\n    plen = P.length,\n    slen = S.length,\n    sw;\n  for (var i = 0; i < plen; i++)\n    (sw = _streamtoword(key, offset)),\n      (offset = sw.offp),\n      (P[i] = P[i] ^ sw.key);\n  for (i = 0; i < plen; i += 2)\n    (lr = _encipher(lr, 0, P, S)), (P[i] = lr[0]), (P[i + 1] = lr[1]);\n  for (i = 0; i < slen; i += 2)\n    (lr = _encipher(lr, 0, P, S)), (S[i] = lr[0]), (S[i + 1] = lr[1]);\n}\n\n/**\n * Expensive key schedule Blowfish.\n * @param {Array.<number>} data\n * @param {Array.<number>} key\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @inner\n */\nfunction _ekskey(data, key, P, S) {\n  var offp = 0,\n    lr = [0, 0],\n    plen = P.length,\n    slen = S.length,\n    sw;\n  for (var i = 0; i < plen; i++)\n    (sw = _streamtoword(key, offp)), (offp = sw.offp), (P[i] = P[i] ^ sw.key);\n  offp = 0;\n  for (i = 0; i < plen; i += 2)\n    (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[0] ^= sw.key),\n      (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[1] ^= sw.key),\n      (lr = _encipher(lr, 0, P, S)),\n      (P[i] = lr[0]),\n      (P[i + 1] = lr[1]);\n  for (i = 0; i < slen; i += 2)\n    (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[0] ^= sw.key),\n      (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[1] ^= sw.key),\n      (lr = _encipher(lr, 0, P, S)),\n      (S[i] = lr[0]),\n      (S[i + 1] = lr[1]);\n}\n\n/**\n * Internaly crypts a string.\n * @param {Array.<number>} b Bytes to crypt\n * @param {Array.<number>} salt Salt bytes to use\n * @param {number} rounds Number of rounds\n * @param {function(Error, Array.<number>=)=} callback Callback receiving the error, if any, and the resulting bytes. If\n *  omitted, the operation will be performed synchronously.\n *  @param {function(number)=} progressCallback Callback called with the current progress\n * @returns {!Array.<number>|undefined} Resulting bytes if callback has been omitted, otherwise `undefined`\n * @inner\n */\nfunction _crypt(b, salt, rounds, callback, progressCallback) {\n  var cdata = C_ORIG.slice(),\n    clen = cdata.length,\n    err;\n\n  // Validate\n  if (rounds < 4 || rounds > 31) {\n    err = Error(\"Illegal number of rounds (4-31): \" + rounds);\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  if (salt.length !== BCRYPT_SALT_LEN) {\n    err = Error(\n      \"Illegal salt length: \" + salt.length + \" != \" + BCRYPT_SALT_LEN,\n    );\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  rounds = (1 << rounds) >>> 0;\n\n  var P,\n    S,\n    i = 0,\n    j;\n\n  //Use typed arrays when available - huge speedup!\n  if (typeof Int32Array === \"function\") {\n    P = new Int32Array(P_ORIG);\n    S = new Int32Array(S_ORIG);\n  } else {\n    P = P_ORIG.slice();\n    S = S_ORIG.slice();\n  }\n\n  _ekskey(salt, b, P, S);\n\n  /**\n   * Calcualtes the next round.\n   * @returns {Array.<number>|undefined} Resulting array if callback has been omitted, otherwise `undefined`\n   * @inner\n   */\n  function next() {\n    if (progressCallback) progressCallback(i / rounds);\n    if (i < rounds) {\n      var start = Date.now();\n      for (; i < rounds; ) {\n        i = i + 1;\n        _key(b, P, S);\n        _key(salt, P, S);\n        if (Date.now() - start > MAX_EXECUTION_TIME) break;\n      }\n    } else {\n      for (i = 0; i < 64; i++)\n        for (j = 0; j < clen >> 1; j++) _encipher(cdata, j << 1, P, S);\n      var ret = [];\n      for (i = 0; i < clen; i++)\n        ret.push(((cdata[i] >> 24) & 0xff) >>> 0),\n          ret.push(((cdata[i] >> 16) & 0xff) >>> 0),\n          ret.push(((cdata[i] >> 8) & 0xff) >>> 0),\n          ret.push((cdata[i] & 0xff) >>> 0);\n      if (callback) {\n        callback(null, ret);\n        return;\n      } else return ret;\n    }\n    if (callback) nextTick(next);\n  }\n\n  // Async\n  if (typeof callback !== \"undefined\") {\n    next();\n\n    // Sync\n  } else {\n    var res;\n    while (true) if (typeof (res = next()) !== \"undefined\") return res || [];\n  }\n}\n\n/**\n * Internally hashes a password.\n * @param {string} password Password to hash\n * @param {?string} salt Salt to use, actually never null\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash. If omitted,\n *  hashing is performed synchronously.\n *  @param {function(number)=} progressCallback Callback called with the current progress\n * @returns {string|undefined} Resulting hash if callback has been omitted, otherwise `undefined`\n * @inner\n */\nfunction _hash(password, salt, callback, progressCallback) {\n  var err;\n  if (typeof password !== \"string\" || typeof salt !== \"string\") {\n    err = Error(\"Invalid string / salt: Not a string\");\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n\n  // Validate the salt\n  var minor, offset;\n  if (salt.charAt(0) !== \"$\" || salt.charAt(1) !== \"2\") {\n    err = Error(\"Invalid salt version: \" + salt.substring(0, 2));\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  if (salt.charAt(2) === \"$\") (minor = String.fromCharCode(0)), (offset = 3);\n  else {\n    minor = salt.charAt(2);\n    if (\n      (minor !== \"a\" && minor !== \"b\" && minor !== \"y\") ||\n      salt.charAt(3) !== \"$\"\n    ) {\n      err = Error(\"Invalid salt revision: \" + salt.substring(2, 4));\n      if (callback) {\n        nextTick(callback.bind(this, err));\n        return;\n      } else throw err;\n    }\n    offset = 4;\n  }\n\n  // Extract number of rounds\n  if (salt.charAt(offset + 2) > \"$\") {\n    err = Error(\"Missing salt rounds\");\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10,\n    r2 = parseInt(salt.substring(offset + 1, offset + 2), 10),\n    rounds = r1 + r2,\n    real_salt = salt.substring(offset + 3, offset + 25);\n  password += minor >= \"a\" ? \"\\x00\" : \"\";\n\n  var passwordb = utf8Array(password),\n    saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);\n\n  /**\n   * Finishes hashing.\n   * @param {Array.<number>} bytes Byte array\n   * @returns {string}\n   * @inner\n   */\n  function finish(bytes) {\n    var res = [];\n    res.push(\"$2\");\n    if (minor >= \"a\") res.push(minor);\n    res.push(\"$\");\n    if (rounds < 10) res.push(\"0\");\n    res.push(rounds.toString());\n    res.push(\"$\");\n    res.push(base64_encode(saltb, saltb.length));\n    res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));\n    return res.join(\"\");\n  }\n\n  // Sync\n  if (typeof callback == \"undefined\")\n    return finish(_crypt(passwordb, saltb, rounds));\n  // Async\n  else {\n    _crypt(\n      passwordb,\n      saltb,\n      rounds,\n      function (err, bytes) {\n        if (err) callback(err, null);\n        else callback(null, finish(bytes));\n      },\n      progressCallback,\n    );\n  }\n}\n\n/**\n * Encodes a byte array to base64 with up to len bytes of input, using the custom bcrypt alphabet.\n * @function\n * @param {!Array.<number>} bytes Byte array\n * @param {number} length Maximum input length\n * @returns {string}\n */\nexport function encodeBase64(bytes, length) {\n  return base64_encode(bytes, length);\n}\n\n/**\n * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\n * @function\n * @param {string} string String to decode\n * @param {number} length Maximum output length\n * @returns {!Array.<number>}\n */\nexport function decodeBase64(string, length) {\n  return base64_decode(string, length);\n}\n\nexport default {\n  setRandomFallback,\n  genSaltSync,\n  genSalt,\n  hashSync,\n  hash,\n  compareSync,\n  compare,\n  getRounds,\n  getSalt,\n  truncates,\n  encodeBase64,\n  decodeBase64,\n};\n", "import { <PERSON>o } from 'hono';\nimport { CloudflareBindings } from '../types/interfaces';\nimport { authMiddleware, superAdminOnly } from '../middleware/auth';\nimport * as bcrypt from 'bcryptjs';\nimport * as response from '../utils/response';\n\nexport function setupAdminRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {\n  // Apply middleware for all admin management routes\n  app.use('/api/admins', authMiddleware(), superAdminOnly());\n  app.use('/api/admins/*', authMiddleware(), superAdminOnly());\n\n  // Get all admins (super admin only)\n  app.get('/api/admins', async (c) => {\n    try {\n      const query = c.req.query();\n      const page = parseInt(query.page || '1');\n      const limit = parseInt(query.limit || '20');\n      const role = query.role;\n      const status = query.status;\n      const search = query.search;\n      const offset = (page - 1) * limit;\n      \n      let queryStr = 'SELECT id, username, role, status, product_ids, created_at, updated_at FROM admins';\n      const params: any[] = [];\n      const conditions: string[] = [];\n      \n      if (role) {\n        conditions.push('role = ?');\n        params.push(role);\n      }\n      \n      if (status) {\n        conditions.push('status = ?');\n        params.push(status);\n      }\n      \n      if (search) {\n        conditions.push('username LIKE ?');\n        params.push(`%${search}%`);\n      }\n      \n      if (conditions.length > 0) {\n        queryStr += ' WHERE ' + conditions.join(' AND ');\n      }\n      \n      queryStr += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';\n      params.push(limit, offset);\n      \n      const admins = await c.env.DB.prepare(queryStr).bind(...params).all();\n      \n      // Get total count\n      let countQuery = 'SELECT COUNT(*) as total FROM admins';\n      if (conditions.length > 0) {\n        countQuery += ' WHERE ' + conditions.join(' AND ');\n      }\n      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();\n      \n      return response.success(c, '管理员列表获取成功', {\n        admins: admins.results,\n        total: (countResult?.total as number) || 0,\n        page,\n        limit,\n        totalPages: Math.ceil(((countResult?.total as number) || 0) / limit),\n      });\n    } catch (error) {\n      console.error('Get admins error:', error);\n      return response.error(c, '服务器内部错误', 'GET_ADMINS_ERROR', 500);\n    }\n  });\n\n  // Create admin (super admin only)\n  app.post('/api/admins', async (c) => {\n    try {\n      const body = await c.req.json();\n      const { username, password, role, product_ids } = body;\n      \n      if (!username || !password || !role) {\n        return response.error(c, '用户名、密码和角色不能为空', 'MISSING_REQUIRED_FIELDS', 400);\n      }\n      \n      // Check if username already exists\n      const existing = await c.env.DB.prepare('SELECT id FROM admins WHERE username = ?').bind(username).first();\n      if (existing) {\n        return response.error(c, '用户名已存在', 'DUPLICATE_USERNAME', 400);\n      }\n      \n      // Validate product_ids if provided\n      if (product_ids && product_ids.length > 0) {\n        const productCheck = await c.env.DB.prepare(`\n          SELECT COUNT(*) as count FROM products WHERE id IN (${product_ids.map(() => '?').join(',')})\n        `).bind(...product_ids).first();\n        \n        if ((productCheck?.count as number) !== product_ids.length) {\n          return response.error(c, '一个或多个产品ID无效', 'INVALID_PRODUCT_IDS', 400);\n        }\n      }\n      \n      // Hash password\n      const passwordHash = await bcrypt.hash(password, 10);\n      const productIdsJson = product_ids ? JSON.stringify(product_ids) : null;\n      \n      const result = await c.env.DB.prepare(`\n        INSERT INTO admins (username, password_hash, role, status, product_ids, created_at, updated_at)\n        VALUES (?, ?, ?, 'active', ?, datetime('now'), datetime('now'))\n      `).bind(username, passwordHash, role, productIdsJson).run();\n      \n      return response.success(c, '管理员创建成功', { id: result.meta.last_row_id });\n    } catch (error) {\n      console.error('Create admin error:', error);\n      return response.error(c, '服务器内部错误', 'CREATE_ADMIN_ERROR', 500);\n    }\n  });\n\n  // Get admin by ID (super admin only)\n  app.get('/api/admins/:id', async (c) => {\n    try {\n      const id = parseInt(c.req.param('id'));\n      \n      if (!id || id <= 0) {\n        return response.error(c, '无效的管理员ID', 'INVALID_ADMIN_ID', 400);\n      }\n      \n      const admin = await c.env.DB.prepare(`\n        SELECT id, username, role, status, product_ids, created_at, updated_at \n        FROM admins \n        WHERE id = ?\n      `).bind(id).first();\n      \n      if (!admin) {\n        return response.error(c, '管理员不存在', 'ADMIN_NOT_FOUND', 404);\n      }\n      \n      // Parse product_ids\n      let assignedProducts = [];\n      if (admin.product_ids) {\n        try {\n          const productIds = JSON.parse(admin.product_ids as string);\n          const products = await c.env.DB.prepare(`\n            SELECT id, name FROM products WHERE id IN (${productIds.map(() => '?').join(',')})\n          `).bind(...productIds).all();\n          assignedProducts = products.results;\n        } catch (e) {\n          // Handle legacy comma-separated format\n          const productIds = (admin.product_ids as string).split(',').map(id => parseInt(id.trim()));\n          const products = await c.env.DB.prepare(`\n            SELECT id, name FROM products WHERE id IN (${productIds.map(() => '?').join(',')})\n          `).bind(...productIds).all();\n          assignedProducts = products.results;\n        }\n      }\n      \n      return response.success(c, '管理员信息获取成功', {\n        admin: {\n          ...admin,\n          assigned_products: assignedProducts,\n        },\n      });\n    } catch (error) {\n      console.error('Get admin error:', error);\n      return response.error(c, '服务器内部错误', 'GET_ADMIN_ERROR', 500);\n    }\n  });\n\n  // Update admin (super admin only)\n  app.put('/api/admins/:id', async (c) => {\n    try {\n      const id = parseInt(c.req.param('id'));\n      const updateData = await c.req.json();\n      \n      if (!id || id <= 0) {\n        return response.error(c, '无效的管理员ID', 'INVALID_ADMIN_ID', 400);\n      }\n      \n      // Check if admin exists\n      const existing = await c.env.DB.prepare('SELECT id FROM admins WHERE id = ?').bind(id).first();\n      if (!existing) {\n        return response.error(c, '管理员不存在', 'ADMIN_NOT_FOUND', 404);\n      }\n      \n      // Validate product_ids if provided\n      if (updateData.product_ids && updateData.product_ids.length > 0) {\n        const productCheck = await c.env.DB.prepare(`\n          SELECT COUNT(*) as count FROM products WHERE id IN (${updateData.product_ids.map(() => '?').join(',')})\n        `).bind(...updateData.product_ids).first();\n        \n        if ((productCheck?.count as number) !== updateData.product_ids.length) {\n          return response.error(c, '一个或多个产品ID无效', 'INVALID_PRODUCT_IDS', 400);\n        }\n      }\n      \n      // Build update query\n      const updateFields: string[] = [];\n      const params: any[] = [];\n      \n      if (updateData.password !== undefined) {\n        const passwordHash = await bcrypt.hash(updateData.password, 10);\n        updateFields.push('password_hash = ?');\n        params.push(passwordHash);\n      }\n      if (updateData.role !== undefined) {\n        updateFields.push('role = ?');\n        params.push(updateData.role);\n      }\n      if (updateData.product_ids !== undefined) {\n        updateFields.push('product_ids = ?');\n        params.push(updateData.product_ids ? JSON.stringify(updateData.product_ids) : null);\n      }\n      if (updateData.status !== undefined) {\n        updateFields.push('status = ?');\n        params.push(updateData.status);\n      }\n      \n      updateFields.push('updated_at = datetime(\\'now\\')');\n      params.push(id);\n      \n      const query = `UPDATE admins SET ${updateFields.join(', ')} WHERE id = ?`;\n      await c.env.DB.prepare(query).bind(...params).run();\n      \n      // Clear cache for this admin\n      const cacheKey = `admin:auth:${id}`;\n      await c.env.CACHE.delete(cacheKey);\n      \n      return response.success(c, '管理员更新成功');\n    } catch (error) {\n      console.error('Update admin error:', error);\n      return response.error(c, '服务器内部错误', 'UPDATE_ADMIN_ERROR', 500);\n    }\n  });\n\n  // Delete admin (super admin only)\n  app.delete('/api/admins/:id', async (c) => {\n    try {\n      const currentAdmin = c.get('admin');\n      const id = parseInt(c.req.param('id'));\n      \n      if (!id || id <= 0) {\n        return response.error(c, '无效的管理员ID', 'INVALID_ADMIN_ID', 400);\n      }\n      \n      // Prevent self-deletion\n      if (currentAdmin.admin_id === id) {\n        return response.error(c, '不能删除自己的账号', 'CANNOT_DELETE_SELF', 400);\n      }\n      \n      // Check if admin exists\n      const existing = await c.env.DB.prepare('SELECT id FROM admins WHERE id = ?').bind(id).first();\n      if (!existing) {\n        return response.error(c, '管理员不存在', 'ADMIN_NOT_FOUND', 404);\n      }\n      \n      // Check if admin has active licenses\n      const activeLicenses = await c.env.DB.prepare('SELECT COUNT(*) as count FROM licenses WHERE admin_id = ? AND status = \\'active\\'').bind(id).first();\n      if (activeLicenses && (activeLicenses.count as number) > 0) {\n        return response.error(c, '不能删除拥有活跃许可证的管理员', 'ADMIN_HAS_ACTIVE_LICENSES', 409);\n      }\n      \n      await c.env.DB.prepare('DELETE FROM admins WHERE id = ?').bind(id).run();\n      \n      // Clear cache\n      const cacheKey = `admin:auth:${id}`;\n      await c.env.CACHE.delete(cacheKey);\n      \n      return response.success(c, '管理员删除成功');\n    } catch (error) {\n      console.error('Delete admin error:', error);\n      return response.error(c, '服务器内部错误', 'DELETE_ADMIN_ERROR', 500);\n    }\n  });\n}", "import { Context, Next } from 'hono';\nimport { JWTService } from '../utils/auth';\nimport { CloudflareBindings, JWTPayload } from '../types/interfaces';\nimport * as response from '../utils/response';\n\ndeclare module 'hono' {\n  interface ContextVariableMap {\n    admin: JWTPayload;\n  }\n}\n\nexport function authMiddleware() {\n  return async (c: Context<{ Bindings: CloudflareBindings }>, next: Next) => {\n    const authHeader = c.req.header('Authorization');\n    \n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return response.error(c, '未授权访问：缺少或无效的token', 'MISSING_TOKEN', 401);\n    }\n\n    const token = authHeader.substring(7);\n    const jwtService = new JWTService(c.env.JWT_SECRET);\n\n    try {\n      const payload = await jwtService.verifyToken(token);\n      \n      // 检查token是否过期\n      if (payload.exp < Math.floor(Date.now() / 1000)) {\n        return response.error(c, '未授权访问：token已过期', 'TOKEN_EXPIRED', 401);\n      }\n\n      c.set('admin', payload);\n      await next();\n    } catch (err) {\n      return response.error(c, '未授权访问：无效的token', 'INVALID_TOKEN', 401);\n    }\n  };\n}\n\nexport function superAdminOnly() {\n  return async (c: Context<{ Bindings: CloudflareBindings }>, next: Next) => {\n    const admin = c.get('admin');\n    \n    if (!admin || admin.role !== 'super') {\n      return response.error(c, '权限不足：需要超级管理员权限', 'INSUFFICIENT_PERMISSIONS', 403);\n    }\n\n    await next();\n  };\n}\n\n// 检查普通管理员是否有权限操作指定产品\nexport function checkProductAccess(productIdParam?: string) {\n  return async (c: Context<{ Bindings: CloudflareBindings }>, next: Next) => {\n    const admin = c.get('admin');\n    \n    // 超级管理员跳过检查\n    if (admin.role === 'super') {\n      await next();\n      return;\n    }\n\n    // 获取产品ID\n    let productId = productIdParam;\n    if (!productId) {\n      // 从路径参数或请求体中获取product_id\n      productId = c.req.param('product_id') || c.req.param('id');\n      if (!productId) {\n        try {\n          const body = await c.req.json();\n          productId = body.product_id;\n        } catch {\n          // 忽略解析错误\n        }\n      }\n    }\n\n    if (productId) {\n      // 从缓存中获取管理员权限信息\n      const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);\n      if (adminInfo) {\n        const { product_ids } = JSON.parse(adminInfo);\n        if (!product_ids || !product_ids.includes(productId.toString())) {\n          return response.error(c, '权限不足：无权限操作该产品', 'PRODUCT_ACCESS_DENIED', 403);\n        }\n      } else {\n        // 缓存未命中，从数据库查询\n        const adminRecord = await c.env.DB.prepare('SELECT product_ids FROM admins WHERE id = ?').bind(admin.admin_id).first();\n        if (adminRecord && adminRecord.product_ids) {\n          const productIds = JSON.parse(adminRecord.product_ids as string);\n          if (!productIds.includes(parseInt(productId))) {\n            return response.error(c, '权限不足：无权限操作该产品', 'PRODUCT_ACCESS_DENIED', 403);\n          }\n        } else {\n          return response.error(c, '权限不足：无权限操作该产品', 'PRODUCT_ACCESS_DENIED', 403);\n        }\n      }\n    }\n\n    await next();\n  };\n}", "import { Hono } from 'hono';\nimport { CloudflareBindings } from '../types/interfaces';\nimport { authMiddleware } from '../middleware/auth';\nimport { generateRandomKey } from '../utils/auth';\nimport * as response from '../utils/response';\n\nexport function setupLicenseRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {\n  // Get licenses\n  app.get('/api/v1/licenses', authMiddleware(), async (c) => {\n    try {\n      const admin = c.get('admin');\n      const query = c.req.query();\n      const page = parseInt(query.page || '1');\n      const limit = parseInt(query.limit || '20');\n      const product_id = query.product_id ? parseInt(query.product_id) : undefined;\n      const status = query.status;\n      const search = query.search;\n      const offset = (page - 1) * limit;\n      \n      let queryStr = `\n        SELECT l.*, p.name as product_name \n        FROM licenses l \n        JOIN products p ON l.product_id = p.id\n      `;\n      const params: any[] = [];\n      const conditions: string[] = [];\n      \n      // Normal admins can only see licenses for their assigned products\n      if (admin.role !== 'super') {\n        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);\n        if (adminInfo) {\n          const { product_ids } = JSON.parse(adminInfo);\n          if (product_ids && product_ids.length > 0) {\n            conditions.push(`l.product_id IN (${product_ids.map(() => '?').join(',')})`)\n            params.push(...product_ids);\n          } else {\n            // Admin has no assigned products\n            return response.success(c, '未找到许可证', {\n              licenses: [],\n              pagination: { page, limit, total: 0, totalPages: 0 },\n            });\n          }\n        }\n      }\n      \n      if (product_id) {\n        conditions.push('l.product_id = ?');\n        params.push(product_id);\n      }\n      \n      if (status) {\n        conditions.push('l.status = ?');\n        params.push(status);\n      }\n      \n      if (search) {\n        conditions.push('l.license_key LIKE ?');\n        params.push(`%${search}%`);\n      }\n      \n      if (conditions.length > 0) {\n        queryStr += ' WHERE ' + conditions.join(' AND ');\n      }\n      \n      queryStr += ' ORDER BY l.created_at DESC LIMIT ? OFFSET ?';\n      params.push(limit, offset);\n      \n      const licenses = await c.env.DB.prepare(queryStr).bind(...params).all();\n      \n      // Get total count\n      let countQuery = 'SELECT COUNT(*) as total FROM licenses l';\n      if (conditions.length > 0) {\n        countQuery += ' WHERE ' + conditions.join(' AND ');\n      }\n      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();\n      \n      return response.success(c, '许可证列表获取成功', {\n        licenses: licenses.results,\n        pagination: {\n          page,\n          limit,\n          total: (countResult?.total as number) || 0,\n          totalPages: Math.ceil(((countResult?.total as number) || 0) / limit),\n        },\n      });\n    } catch (error) {\n      console.error('Get licenses error:', error);\n      return response.error(c, 'Internal server error', 'GET_LICENSES_ERROR', 500);\n    }\n  });\n\n  // Create licenses\n  app.post('/api/v1/licenses', authMiddleware(), async (c) => {\n    try {\n      const admin = c.get('admin');\n      const body = await c.req.json();\n      const { product_id, count, expires_at, max_devices } = body;\n      \n      if (!product_id || !count) {\n        return response.error(c, '产品ID和数量不能为空', 'MISSING_REQUIRED_FIELDS', 400);\n      }\n      \n      if (count <= 0 || count > 1000) {\n        return response.error(c, '数量必须在1到1000之间', 'INVALID_COUNT', 400);\n      }\n      \n      // Check if product exists and admin has access\n      const product = await c.env.DB.prepare('SELECT * FROM products WHERE id = ? AND status = \\'active\\'').bind(product_id).first();\n      if (!product) {\n        return response.error(c, 'Product not found or inactive', 'PRODUCT_NOT_FOUND', 400);\n      }\n      \n      // Check admin permissions\n      if (admin.role !== 'super') {\n        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);\n        if (adminInfo) {\n          const { product_ids } = JSON.parse(adminInfo);\n          if (!product_ids || !product_ids.includes(product_id.toString())) {\n            return response.error(c, 'Access denied to this product', 'PRODUCT_ACCESS_DENIED', 403);\n          }\n        }\n      }\n      \n      // Generate licenses\n      const licenses: string[] = [];\n      const insertPromises: Promise<any>[] = [];\n      \n      for (let i = 0; i < count; i++) {\n        const licenseKey = generateRandomKey(24);\n        licenses.push(licenseKey);\n        \n        insertPromises.push(\n          c.env.DB.prepare(`\n            INSERT INTO licenses (product_id, license_key, status, expires_at, max_devices, admin_id, created_at, updated_at)\n            VALUES (?, ?, 'active', ?, ?, ?, datetime('now'), datetime('now'))\n          `).bind(product_id, licenseKey, expires_at || null, max_devices || null, admin.admin_id).run()\n        );\n      }\n      \n      await Promise.all(insertPromises);\n      \n      // Create order record\n      const unitPrice = 10.0; // Default price, should be configurable\n      const totalPrice = unitPrice * count;\n      \n      await c.env.DB.prepare(`\n        INSERT INTO orders (admin_id, product_id, license_count, unit_price, total_price, status, created_at, updated_at)\n        VALUES (?, ?, ?, ?, ?, 'completed', datetime('now'), datetime('now'))\n      `).bind(admin.admin_id, product_id, count, unitPrice, totalPrice).run();\n      \n      return response.success(c, `成功创建${count}个许可证`, { licenses }, 201);\n    } catch (error) {\n      console.error('Create licenses error:', error);\n      return response.error(c, 'Internal server error', 'CREATE_LICENSES_ERROR', 500);\n    }\n  });\n\n  // Get license details\n  app.get('/api/v1/licenses/:id', authMiddleware(), async (c) => {\n    try {\n      const admin = c.get('admin');\n      const id = parseInt(c.req.param('id'));\n      \n      if (!id || id <= 0) {\n        return response.error(c, '无效的许可证ID', 'INVALID_LICENSE_ID', 400);\n      }\n      \n      const license = await c.env.DB.prepare(`\n        SELECT l.*, p.name as product_name, p.verification_strategy\n        FROM licenses l\n        JOIN products p ON l.product_id = p.id\n        WHERE l.id = ?\n      `).bind(id).first();\n      \n      if (!license) {\n        return response.error(c, 'License not found', 'LICENSE_NOT_FOUND', 404);\n      }\n      \n      // Check admin permissions\n      if (admin.role !== 'super') {\n        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);\n        if (adminInfo) {\n          const { product_ids } = JSON.parse(adminInfo);\n          if (!product_ids || !product_ids.includes((license.product_id as number).toString())) {\n            return response.error(c, 'Access denied to this license', 'LICENSE_ACCESS_DENIED', 403);\n          }\n        }\n      }\n      \n      // Get device bindings\n      const devices = await c.env.DB.prepare('SELECT * FROM devices WHERE license_id = ?').bind(id).all();\n      \n      // Get verification logs\n      const logs = await c.env.DB.prepare(`\n        SELECT * FROM verification_logs \n        WHERE license_key = ? \n        ORDER BY created_at DESC \n        LIMIT 50\n      `).bind(license.license_key).all();\n      \n      return response.success(c, '许可证信息获取成功', {\n        license,\n        devices: devices.results,\n        verification_logs: logs.results,\n      });\n    } catch (error) {\n      console.error('Get license error:', error);\n      return response.error(c, 'Internal server error', 'GET_LICENSE_ERROR', 500);\n    }\n  });\n\n  // Update license\n  app.put('/api/v1/licenses/:id', authMiddleware(), async (c) => {\n    try {\n      const admin = c.get('admin');\n      const id = parseInt(c.req.param('id'));\n      const updateData = await c.req.json();\n      \n      if (!id || id <= 0) {\n        return response.error(c, '无效的许可证ID', 'INVALID_LICENSE_ID', 400);\n      }\n      \n      // Check if license exists and get product info\n      const license = await c.env.DB.prepare(`\n        SELECT l.*, p.id as product_id\n        FROM licenses l\n        JOIN products p ON l.product_id = p.id\n        WHERE l.id = ?\n      `).bind(id).first();\n      \n      if (!license) {\n        return response.error(c, 'License not found', 'LICENSE_NOT_FOUND', 404);\n      }\n      \n      // Check admin permissions\n      if (admin.role !== 'super') {\n        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);\n        if (adminInfo) {\n          const { product_ids } = JSON.parse(adminInfo);\n          if (!product_ids || !product_ids.includes((license.product_id as number).toString())) {\n            return response.error(c, 'Access denied to this license', 'LICENSE_ACCESS_DENIED', 403);\n          }\n        }\n      }\n      \n      // Build update query\n      const updateFields: string[] = [];\n      const params: any[] = [];\n      \n      if (updateData.status !== undefined) {\n        updateFields.push('status = ?');\n        params.push(updateData.status);\n      }\n      if (updateData.expires_at !== undefined) {\n        updateFields.push('expires_at = ?');\n        params.push(updateData.expires_at);\n      }\n      if (updateData.max_devices !== undefined) {\n        updateFields.push('max_devices = ?');\n        params.push(updateData.max_devices);\n      }\n      \n      updateFields.push('updated_at = datetime(\\'now\\')');\n      params.push(id);\n      \n      const query = `UPDATE licenses SET ${updateFields.join(', ')} WHERE id = ?`;\n      await c.env.DB.prepare(query).bind(...params).run();\n      \n      // Clear cache\n      const cacheKey = `license:verify:${license.license_key}`;\n      await c.env.CACHE.delete(cacheKey);\n      \n      return response.success(c, '许可证更新成功');\n    } catch (error) {\n      console.error('Update license error:', error);\n      return response.error(c, 'Internal server error', 'UPDATE_LICENSE_ERROR', 500);\n    }\n  });\n}", "import { Hono } from 'hono';\nimport { CloudflareBindings } from '../types/interfaces';\nimport * as response from '../utils/response';\n\nexport function setupClientRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {\n  // License verification endpoint\n  app.post('/verify', async (c) => {\n    try {\n      const body = await c.req.json();\n      const { license_key, device_id, product_features } = body;\n      \n      if (!license_key) {\n        return response.error(c, '许可证密钥为必填项', 'MISSING_LICENSE_KEY', 400);\n      }\n      \n      // Check cache first\n      const cacheKey = `license:verify:${license_key}`;\n      const cached = await c.env.CACHE.get(cacheKey);\n      \n      if (cached) {\n        const cachedData = JSON.parse(cached);\n        return response.success(c, '许可证验证成功(缓存)', {\n          license_info: cachedData,\n        });\n      }\n\n      // Query database\n      const license = await c.env.DB.prepare(`\n        SELECT l.*, p.name as product_name, p.verification_strategy, p.max_devices as product_max_devices, p.features\n        FROM licenses l\n        JOIN products p ON l.product_id = p.id\n        WHERE l.license_key = ? AND l.status = 'active' AND p.status = 'active'\n      `).bind(license_key).first();\n\n      if (!license) {\n        // Log failed verification\n        await c.env.DB.prepare(`\n          INSERT INTO verification_logs (license_key, device_id, result, reason, ip_address, user_agent, created_at)\n          VALUES (?, ?, 'failed', 'Invalid or inactive license', ?, ?, datetime('now'))\n        `).bind(\n          license_key,\n          device_id || null,\n          c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || '',\n          c.req.header('User-Agent') || ''\n        ).run();\n\n        return response.error(c, '无效或已停用的许可证密钥', 'INVALID_LICENSE', 400);\n      }\n\n      // Check expiration\n      if (license.expires_at && new Date(license.expires_at as string) < new Date()) {\n        return response.error(c, '许可证已过期', 'LICENSE_EXPIRED', 400);\n      }\n\n      // Check device limits\n      let currentDevices = 0;\n      if (license.verification_strategy === 'device_count' && device_id) {\n        const deviceCount = await c.env.DB.prepare(`\n          SELECT COUNT(*) as count FROM devices WHERE license_id = ?\n        `).bind(license.id).first();\n        \n        currentDevices = (deviceCount?.count as number) || 0;\n        const maxDevices = (license.max_devices as number) || (license.product_max_devices as number) || 1;\n\n        // Check if device already exists\n        const existingDevice = await c.env.DB.prepare(`\n          SELECT id FROM devices WHERE license_id = ? AND device_id = ?\n        `).bind(license.id, device_id).first();\n\n        if (!existingDevice && currentDevices >= maxDevices) {\n          return response.error(c, `设备数量超限，最多允许${maxDevices}台设备`, 'DEVICE_LIMIT_EXCEEDED', 400);\n        }\n\n        // Add or update device\n        if (!existingDevice) {\n          await c.env.DB.prepare(`\n            INSERT INTO devices (license_id, device_id, device_info, last_verification, created_at, updated_at)\n            VALUES (?, ?, ?, datetime('now'), datetime('now'), datetime('now'))\n          `).bind(license.id as number, device_id, JSON.stringify({ user_agent: c.req.header('User-Agent') })).run();\n          currentDevices++;\n        } else {\n          await c.env.DB.prepare(`\n            UPDATE devices SET last_verification = datetime('now'), updated_at = datetime('now')\n            WHERE license_id = ? AND device_id = ?\n          `).bind(license.id as number, device_id).run();\n        }\n      }\n\n      // Parse features\n      let features: string[] = [];\n      if (license.features) {\n        try {\n          features = JSON.parse(license.features as string);\n        } catch {\n          features = (license.features as string).split(',').map((f: string) => f.trim());\n        }\n      }\n\n      const licenseInfo = {\n        product_name: license.product_name,\n        expires_at: license.expires_at,\n        max_devices: license.max_devices || license.product_max_devices,\n        current_devices: currentDevices,\n        features,\n      };\n\n      // Cache the result for 5 minutes\n      await c.env.CACHE.put(cacheKey, JSON.stringify(licenseInfo), { expirationTtl: 300 });\n\n      // Log successful verification\n      await c.env.DB.prepare(`\n        INSERT INTO verification_logs (license_key, device_id, result, reason, ip_address, user_agent, created_at)\n        VALUES (?, ?, 'success', 'License verified successfully', ?, ?, datetime('now'))\n      `).bind(\n        license_key,\n        device_id || null,\n        c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || '',\n        c.req.header('User-Agent') || ''\n      ).run();\n\n      return response.success(c, '许可证验证成功', {\n        license_info: licenseInfo,\n      });\n\n    } catch (error) {\n      console.error('License verification error:', error);\n      return response.error(c, '服务器内部错误', 'VERIFICATION_ERROR', 500);\n    }\n  });\n\n  // Device unbinding endpoint\n  app.post('/device/unbind', async (c) => {\n    try {\n      const body = await c.req.json();\n      const { license_key, device_id } = body;\n\n      if (!license_key || !device_id) {\n        return response.error(c, '许可证密钥和设备ID为必填项', 'MISSING_PARAMETERS', 400);\n      }\n\n      // Get license info\n      const license = await c.env.DB.prepare(`\n        SELECT id FROM licenses WHERE license_key = ? AND status = 'active'\n      `).bind(license_key).first();\n\n      if (!license) {\n        return response.error(c, '无效或已停用的许可证密钥', 'INVALID_LICENSE', 400);\n      }\n\n      // Remove device binding\n      const result = await c.env.DB.prepare(`\n        DELETE FROM devices WHERE license_id = ? AND device_id = ?\n      `).bind(license.id as number, device_id).run();\n\n      if (result.meta.changes === 0) {\n        return response.error(c, '未找到设备绑定', 'DEVICE_NOT_FOUND', 400);\n      }\n\n      // Clear cache\n      const cacheKey = `license:verify:${license_key}`;\n      await c.env.CACHE.delete(cacheKey);\n\n      return response.success(c, '设备解绑成功');\n\n    } catch (error) {\n      console.error('Device unbind error:', error);\n      return response.error(c, '服务器内部错误', 'UNBIND_ERROR', 500);\n    }\n  });\n}", "import { Hono } from 'hono';\nimport { CloudflareBindings } from '../types/interfaces';\nimport { authMiddleware } from '../middleware/auth';\nimport * as response from '../utils/response';\n\nexport function setupOrderRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {\n  // Get orders\n  app.get('/api/orders', authMiddleware(), async (c) => {\n    try {\n      const admin = c.get('admin');\n      const query = c.req.query();\n      const page = parseInt(query.page || '1');\n      const limit = parseInt(query.limit || '20');\n      const status = query.status;\n      const product_id = query.product_id ? parseInt(query.product_id) : undefined;\n      const start_date = query.start_date;\n      const end_date = query.end_date;\n      const offset = (page - 1) * limit;\n      \n      let queryStr = `\n        SELECT o.*, p.name as product_name, a.username as admin_username\n        FROM orders o\n        JOIN products p ON o.product_id = p.id\n        JOIN admins a ON o.admin_id = a.id\n      `;\n      const params: any[] = [];\n      const conditions: string[] = [];\n      \n      // Normal admins can only see their own orders\n      if (admin.role !== 'super') {\n        conditions.push('o.admin_id = ?');\n        params.push(admin.admin_id);\n      }\n      \n      if (status) {\n        conditions.push('o.status = ?');\n        params.push(status);\n      }\n      \n      if (product_id) {\n        conditions.push('o.product_id = ?');\n        params.push(product_id);\n      }\n      \n      if (start_date) {\n        conditions.push('o.created_at >= ?');\n        params.push(start_date);\n      }\n      \n      if (end_date) {\n        conditions.push('o.created_at <= ?');\n        params.push(end_date);\n      }\n      \n      if (conditions.length > 0) {\n        queryStr += ' WHERE ' + conditions.join(' AND ');\n      }\n      \n      queryStr += ' ORDER BY o.created_at DESC LIMIT ? OFFSET ?';\n      params.push(limit, offset);\n      \n      const orders = await c.env.DB.prepare(queryStr).bind(...params).all();\n      \n      // Get total count\n      let countQuery = 'SELECT COUNT(*) as total FROM orders o';\n      if (conditions.length > 0) {\n        countQuery += ' WHERE ' + conditions.join(' AND ');\n      }\n      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();\n      \n      return response.success(c, '订单列表获取成功', {\n        orders: orders.results,\n        pagination: {\n          page,\n          limit,\n          total: (countResult?.total as number) || 0,\n          totalPages: Math.ceil(((countResult?.total as number) || 0) / limit),\n        },\n      });\n    } catch (error) {\n      console.error('Get orders error:', error);\n      return response.error(c, 'Internal server error', 'GET_ORDERS_ERROR', 500);\n    }\n  });\n\n  // Create order\n  app.post('/api/orders', authMiddleware(), async (c) => {\n    try {\n      const admin = c.get('admin');\n      const body = await c.req.json();\n      const { product_id, license_count, unit_price } = body;\n      \n      if (!product_id || !license_count || !unit_price) {\n        return response.error(c, '产品ID、许可证数量和单价不能为空', 'MISSING_REQUIRED_FIELDS', 400);\n      }\n      \n      // Check if product exists and admin has access\n      const product = await c.env.DB.prepare('SELECT * FROM products WHERE id = ? AND status = \\'active\\'').bind(product_id).first();\n      if (!product) {\n        return response.error(c, 'Product not found or inactive', 'PRODUCT_NOT_FOUND', 400);\n      }\n      \n      // Check admin permissions for normal admins\n      if (admin.role !== 'super') {\n        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);\n        if (adminInfo) {\n          const { product_ids } = JSON.parse(adminInfo);\n          if (!product_ids || !product_ids.includes(product_id.toString())) {\n            return response.error(c, 'Access denied to this product', 'PRODUCT_ACCESS_DENIED', 403);\n          }\n        }\n      }\n      \n      const totalPrice = unit_price * license_count;\n      \n      const result = await c.env.DB.prepare(`\n        INSERT INTO orders (admin_id, product_id, license_count, unit_price, total_price, status, created_at, updated_at)\n        VALUES (?, ?, ?, ?, ?, 'pending', datetime('now'), datetime('now'))\n      `).bind(admin.admin_id, product_id, license_count, unit_price, totalPrice).run();\n      \n      return response.success(c, '订单创建成功', { \n        id: result.meta.last_row_id,\n        total_price: totalPrice \n      }, 201);\n    } catch (error) {\n      console.error('Create order error:', error);\n      return response.error(c, 'Internal server error', 'CREATE_ORDER_ERROR', 500);\n    }\n  });\n\n  // Get order by ID\n  app.get('/api/orders/:id', authMiddleware(), async (c) => {\n    try {\n      const admin = c.get('admin');\n      const id = parseInt(c.req.param('id'));\n      \n      if (!id || id <= 0) {\n        return response.error(c, '无效的订单ID', 'INVALID_ORDER_ID', 400);\n      }\n      \n      let queryStr = `\n        SELECT o.*, p.name as product_name, a.username as admin_username\n        FROM orders o\n        JOIN products p ON o.product_id = p.id\n        JOIN admins a ON o.admin_id = a.id\n        WHERE o.id = ?\n      `;\n      \n      // Normal admins can only see their own orders\n      if (admin.role !== 'super') {\n        queryStr += ' AND o.admin_id = ?';\n      }\n      \n      const params = admin.role === 'super' ? [id] : [id, admin.admin_id];\n      const order = await c.env.DB.prepare(queryStr).bind(...params).first();\n      \n      if (!order) {\n        return response.error(c, 'Order not found or access denied', 'ORDER_NOT_FOUND', 404);\n      }\n      \n      return response.success(c, '订单信息获取成功', { order });\n    } catch (error) {\n      console.error('Get order error:', error);\n      return response.error(c, 'Internal server error', 'GET_ORDER_ERROR', 500);\n    }\n  });\n\n  // Update order status\n  app.put('/api/orders/:id', authMiddleware(), async (c) => {\n    try {\n      const admin = c.get('admin');\n      const id = parseInt(c.req.param('id'));\n      const body = await c.req.json();\n      const { status } = body;\n      \n      if (!id || id <= 0) {\n        return response.error(c, '无效的订单ID', 'INVALID_ORDER_ID', 400);\n      }\n      \n      if (!status) {\n        return response.error(c, '状态不能为空', 'MISSING_STATUS', 400);\n      }\n      \n      // Check if order exists and admin has access\n      let checkQuery = 'SELECT * FROM orders WHERE id = ?';\n      if (admin.role !== 'super') {\n        checkQuery += ' AND admin_id = ?';\n      }\n      \n      const params = admin.role === 'super' ? [id] : [id, admin.admin_id];\n      const order = await c.env.DB.prepare(checkQuery).bind(...params).first();\n      \n      if (!order) {\n        return response.error(c, 'Order not found or access denied', 'ORDER_NOT_FOUND', 404);\n      }\n      \n      await c.env.DB.prepare(`\n        UPDATE orders SET status = ?, updated_at = datetime('now') WHERE id = ?\n      `).bind(status, id).run();\n      \n      return response.success(c, '订单状态更新成功');\n    } catch (error) {\n      console.error('Update order error:', error);\n      return response.error(c, 'Internal server error', 'UPDATE_ORDER_ERROR', 500);\n    }\n  });\n\n  // Delete order\n  app.delete('/api/orders/:id', authMiddleware(), async (c) => {\n    try {\n      const admin = c.get('admin');\n      const id = parseInt(c.req.param('id'));\n      \n      if (!id || id <= 0) {\n        return response.error(c, '无效的订单ID', 'INVALID_ORDER_ID', 400);\n      }\n      \n      // Check if order exists and admin has access\n      let checkQuery = 'SELECT * FROM orders WHERE id = ?';\n      if (admin.role !== 'super') {\n        checkQuery += ' AND admin_id = ?';\n      }\n      \n      const params = admin.role === 'super' ? [id] : [id, admin.admin_id];\n      const order = await c.env.DB.prepare(checkQuery).bind(...params).first();\n      \n      if (!order) {\n        return response.error(c, 'Order not found or access denied', 'ORDER_NOT_FOUND', 404);\n      }\n      \n      // Only allow deletion of pending orders\n      if (order.status !== 'pending') {\n        return response.error(c, 'Cannot delete completed or cancelled order', 'ORDER_NOT_DELETABLE', 409);\n      }\n      \n      await c.env.DB.prepare('DELETE FROM orders WHERE id = ?').bind(id).run();\n      \n      return response.success(c, '订单删除成功');\n    } catch (error) {\n      console.error('Delete order error:', error);\n      return response.error(c, 'Internal server error', 'DELETE_ORDER_ERROR', 500);\n    }\n  });\n}", "import { Hono } from 'hono';\nimport { CloudflareBindings } from '../types/interfaces';\nimport { authMiddleware, superAdminOnly } from '../middleware/auth';\nimport * as response from '../utils/response';\n\nexport function setupProductRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {\n  // Get all products\n  app.get('/api/products', authMiddleware(), async (c) => {\n    try {\n      const query = c.req.query();\n      const page = parseInt(query.page || '1');\n      const limit = parseInt(query.limit || '20');\n      const status = query.status;\n      const search = query.search;\n      const offset = (page - 1) * limit;\n      \n      let queryStr = 'SELECT * FROM products';\n      const params: any[] = [];\n      const conditions: string[] = [];\n      \n      if (status) {\n        conditions.push('status = ?');\n        params.push(status);\n      }\n      \n      if (search) {\n        conditions.push('(name LIKE ? OR description LIKE ?)');\n        params.push(`%${search}%`, `%${search}%`);\n      }\n      \n      if (conditions.length > 0) {\n        queryStr += ' WHERE ' + conditions.join(' AND ');\n      }\n      \n      queryStr += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';\n      params.push(limit, offset);\n      \n      const products = await c.env.DB.prepare(queryStr).bind(...params).all();\n      \n      // Get total count\n      let countQuery = 'SELECT COUNT(*) as total FROM products';\n      if (conditions.length > 0) {\n        countQuery += ' WHERE ' + conditions.join(' AND ');\n      }\n      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();\n      \n      return response.success(c, '产品列表获取成功', {\n        products: products.results,\n        pagination: {\n          page,\n          limit,\n          total: (countResult?.total as number) || 0,\n          totalPages: Math.ceil(((countResult?.total as number) || 0) / limit),\n        },\n      });\n    } catch (error) {\n      console.error('Get products error:', error);\n      return response.error(c, '服务器内部错误', 'GET_PRODUCTS_ERROR', 500);\n    }\n  });\n\n  // Create product\n  app.post('/api/products', authMiddleware(), superAdminOnly(), async (c) => {\n    try {\n      const body = await c.req.json();\n      const { name, description, verification_strategy, max_devices, features } = body;\n      \n      if (!name || !verification_strategy) {\n        return response.error(c, '产品名称和验证策略不能为空', 'MISSING_REQUIRED_FIELDS', 400);\n      }\n      \n      // Check if product name already exists\n      const existing = await c.env.DB.prepare('SELECT id FROM products WHERE name = ?').bind(name).first();\n      if (existing) {\n        return response.error(c, '产品名称已存在', 'DUPLICATE_PRODUCT_NAME', 400);\n      }\n      \n      const featuresJson = features ? JSON.stringify(features) : null;\n      \n      const result = await c.env.DB.prepare(`\n        INSERT INTO products (name, description, verification_strategy, max_devices, features, status, created_at, updated_at)\n        VALUES (?, ?, ?, ?, ?, 'active', datetime('now'), datetime('now'))\n      `).bind(name, description || null, verification_strategy, max_devices || null, featuresJson).run();\n      \n      return response.success(c, '产品创建成功', { id: result.meta.last_row_id });\n    } catch (error) {\n      console.error('Create product error:', error);\n      return response.error(c, '服务器内部错误', 'CREATE_PRODUCT_ERROR', 500);\n    }\n  });\n\n  // Get product by ID\n  app.get('/api/products/:id', authMiddleware(), async (c) => {\n    try {\n      const id = parseInt(c.req.param('id'));\n      \n      if (!id || id <= 0) {\n        return response.error(c, '无效的产品ID', 'INVALID_PRODUCT_ID', 400);\n      }\n      \n      const product = await c.env.DB.prepare('SELECT * FROM products WHERE id = ?').bind(id).first();\n      \n      if (!product) {\n        return response.error(c, '产品不存在', 'PRODUCT_NOT_FOUND', 404);\n      }\n      \n      return response.success(c, '产品信息获取成功', { product });\n    } catch (error) {\n      console.error('Get product error:', error);\n      return response.error(c, '服务器内部错误', 'GET_PRODUCT_ERROR', 500);\n    }\n  });\n\n  // Update product\n  app.put('/api/products/:id', authMiddleware(), superAdminOnly(), async (c) => {\n    try {\n      const id = parseInt(c.req.param('id'));\n      const updateData = await c.req.json();\n      \n      if (!id || id <= 0) {\n        return response.error(c, '无效的产品ID', 'INVALID_PRODUCT_ID', 400);\n      }\n      \n      // Check if product exists\n      const existing = await c.env.DB.prepare('SELECT id FROM products WHERE id = ?').bind(id).first();\n      if (!existing) {\n        return response.error(c, '产品不存在', 'PRODUCT_NOT_FOUND', 404);\n      }\n      \n      // Build update query\n      const updateFields: string[] = [];\n      const params: any[] = [];\n      \n      if (updateData.name !== undefined) {\n        updateFields.push('name = ?');\n        params.push(updateData.name);\n      }\n      if (updateData.description !== undefined) {\n        updateFields.push('description = ?');\n        params.push(updateData.description);\n      }\n      if (updateData.verification_strategy !== undefined) {\n        updateFields.push('verification_strategy = ?');\n        params.push(updateData.verification_strategy);\n      }\n      if (updateData.max_devices !== undefined) {\n        updateFields.push('max_devices = ?');\n        params.push(updateData.max_devices);\n      }\n      if (updateData.features !== undefined) {\n        updateFields.push('features = ?');\n        params.push(updateData.features ? JSON.stringify(updateData.features) : null);\n      }\n      if (updateData.status !== undefined) {\n        updateFields.push('status = ?');\n        params.push(updateData.status);\n      }\n      \n      updateFields.push('updated_at = datetime(\\'now\\')');\n      params.push(id);\n      \n      const query = `UPDATE products SET ${updateFields.join(', ')} WHERE id = ?`;\n      await c.env.DB.prepare(query).bind(...params).run();\n      \n      return response.success(c, '产品更新成功');\n    } catch (error) {\n      console.error('Update product error:', error);\n      return response.error(c, '服务器内部错误', 'UPDATE_PRODUCT_ERROR', 500);\n    }\n  });\n\n  // Delete product\n  app.delete('/api/products/:id', authMiddleware(), superAdminOnly(), async (c) => {\n    try {\n      const id = parseInt(c.req.param('id'));\n      \n      if (!id || id <= 0) {\n        return response.error(c, '无效的产品ID', 'INVALID_PRODUCT_ID', 400);\n      }\n      \n      // Check if product exists\n      const existing = await c.env.DB.prepare('SELECT id FROM products WHERE id = ?').bind(id).first();\n      if (!existing) {\n        return response.error(c, '产品不存在', 'PRODUCT_NOT_FOUND', 404);\n      }\n      \n      // Check if product has active licenses\n      const activeLicenses = await c.env.DB.prepare('SELECT COUNT(*) as count FROM licenses WHERE product_id = ? AND status = \\'active\\'').bind(id).first();\n      if (activeLicenses && (activeLicenses.count as number) > 0) {\n        return response.error(c, '不能删除拥有活跃许可证的产品', 'PRODUCT_HAS_ACTIVE_LICENSES', 409);\n      }\n      \n      await c.env.DB.prepare('DELETE FROM products WHERE id = ?').bind(id).run();\n      \n      return response.success(c, '产品删除成功');\n    } catch (error) {\n      console.error('Delete product error:', error);\n      return response.error(c, '服务器内部错误', 'DELETE_PRODUCT_ERROR', 500);\n    }\n  });\n}", "import { Hono } from 'hono';\nimport { CloudflareBindings } from '../types/interfaces';\nimport { authMiddleware } from '../middleware/auth';\nimport * as response from '../utils/response';\n\nexport function setupStatisticsRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {\n  // Get sales statistics\n  app.get('/api/stats/sales', authMiddleware(), async (c) => {\n    try {\n      const admin = c.get('admin');\n      const query = c.req.query();\n      const period = query.period || 'month';\n      const start_date = query.start_date;\n      const end_date = query.end_date;\n      \n      // Build date filter\n      let dateFilter = '';\n      const params: any[] = [];\n      \n      if (start_date && end_date) {\n        dateFilter = 'AND o.created_at BETWEEN ? AND ?';\n        params.push(start_date, end_date);\n      } else {\n        // Default period-based filtering\n        const now = new Date();\n        let startDate: Date;\n        \n        switch (period) {\n          case 'day':\n            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n            break;\n          case 'week':\n            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n            break;\n          case 'month':\n            startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n            break;\n          case 'year':\n            startDate = new Date(now.getFullYear(), 0, 1);\n            break;\n          default:\n            startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n        }\n        \n        dateFilter = 'AND o.created_at >= ?';\n        params.push(startDate.toISOString());\n      }\n      \n      // Check cache first\n      const cacheKey = `stats:${admin.admin_id}:${period}:${start_date || ''}:${end_date || ''}`;\n      const cached = await c.env.CACHE.get(cacheKey);\n      \n      if (cached) {\n        return response.success(c, '销售统计获取成功(缓存)', JSON.parse(cached));\n      }\n      \n      // Base query conditions\n      let adminFilter = '';\n      if (admin.role !== 'super') {\n        adminFilter = 'WHERE o.admin_id = ?';\n        params.unshift(admin.admin_id);\n      }\n      \n      // Get total sales\n      const totalSalesQuery = `\n        SELECT \n          COUNT(*) as total_orders,\n          SUM(license_count) as total_licenses,\n          SUM(total_price) as total_revenue\n        FROM orders o\n        ${adminFilter}\n        ${adminFilter ? 'AND' : 'WHERE'} o.status = 'completed'\n        ${dateFilter}\n      `;\n      \n      const totalSales = await c.env.DB.prepare(totalSalesQuery).bind(...params).first();\n      \n      // Get sales by product\n      const productSalesQuery = `\n        SELECT \n          p.name as product_name,\n          COUNT(*) as orders,\n          SUM(o.license_count) as licenses,\n          SUM(o.total_price) as revenue\n        FROM orders o\n        JOIN products p ON o.product_id = p.id\n        ${adminFilter}\n        ${adminFilter ? 'AND' : 'WHERE'} o.status = 'completed'\n        ${dateFilter}\n        GROUP BY p.id, p.name\n        ORDER BY revenue DESC\n      `;\n      \n      const productSales = await c.env.DB.prepare(productSalesQuery).bind(...params).all();\n      \n      // Get sales trend (last 30 days)\n      const trendQuery = `\n        SELECT \n          DATE(o.created_at) as date,\n          COUNT(*) as orders,\n          SUM(o.license_count) as licenses,\n          SUM(o.total_price) as revenue\n        FROM orders o\n        ${adminFilter}\n        ${adminFilter ? 'AND' : 'WHERE'} o.status = 'completed'\n        AND o.created_at >= date('now', '-30 days')\n        GROUP BY DATE(o.created_at)\n        ORDER BY date DESC\n      `;\n      \n      const trendParams = admin.role === 'super' ? [] : [admin.admin_id];\n      const salesTrend = await c.env.DB.prepare(trendQuery).bind(...trendParams).all();\n      \n      const stats = {\n        summary: {\n          total_orders: (totalSales?.total_orders as number) || 0,\n          total_licenses: (totalSales?.total_licenses as number) || 0,\n          total_revenue: (totalSales?.total_revenue as number) || 0,\n          period,\n        },\n        by_product: productSales.results,\n        trend: salesTrend.results,\n      };\n      \n      // Cache for 10 minutes\n      await c.env.CACHE.put(cacheKey, JSON.stringify(stats), { expirationTtl: 600 });\n      \n      return response.success(c, '销售统计获取成功', stats);\n    } catch (error) {\n      console.error('Get sales stats error:', error);\n      return response.error(c, 'Internal server error', 'GET_SALES_STATS_ERROR', 500);\n    }\n  });\n\n  // Get verification statistics\n  app.get('/api/stats/verification', authMiddleware(), async (c) => {\n    try {\n      const admin = c.get('admin');\n      const query = c.req.query();\n      const period = query.period || 'month';\n      const start_date = query.start_date;\n      const end_date = query.end_date;\n      \n      // Build date filter\n      let dateFilter = '';\n      const params: any[] = [];\n      \n      if (start_date && end_date) {\n        dateFilter = 'AND vl.created_at BETWEEN ? AND ?';\n        params.push(start_date, end_date);\n      } else {\n        // Default period-based filtering\n        const now = new Date();\n        let startDate: Date;\n        \n        switch (period) {\n          case 'day':\n            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n            break;\n          case 'week':\n            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n            break;\n          case 'month':\n            startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n            break;\n          case 'year':\n            startDate = new Date(now.getFullYear(), 0, 1);\n            break;\n          default:\n            startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n        }\n        \n        dateFilter = 'AND vl.created_at >= ?';\n        params.push(startDate.toISOString());\n      }\n      \n      // Base query - filter by admin's products for normal admins\n      let productFilter = '';\n      if (admin.role !== 'super') {\n        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);\n        if (adminInfo) {\n          const { product_ids } = JSON.parse(adminInfo);\n          if (product_ids && product_ids.length > 0) {\n            productFilter = `AND l.product_id IN (${product_ids.map(() => '?').join(',')})`;\n            params.push(...product_ids);\n          } else {\n            // Admin has no assigned products\n            return response.success(c, '无验证数据可用', {\n              summary: { total_verifications: 0, successful_verifications: 0, failed_verifications: 0, success_rate: 0 },\n              by_result: [],\n              trend: [],\n            });\n          }\n        }\n      }\n      \n      // Get total verifications\n      const totalQuery = `\n        SELECT \n          COUNT(*) as total_verifications,\n          SUM(CASE WHEN vl.result = 'success' THEN 1 ELSE 0 END) as successful_verifications,\n          SUM(CASE WHEN vl.result = 'failed' THEN 1 ELSE 0 END) as failed_verifications\n        FROM verification_logs vl\n        JOIN licenses l ON vl.license_key = l.license_key\n        WHERE 1=1\n        ${productFilter}\n        ${dateFilter}\n      `;\n      \n      const totalStats = await c.env.DB.prepare(totalQuery).bind(...params).first();\n      \n      // Get verification by result\n      const resultQuery = `\n        SELECT \n          vl.result,\n          COUNT(*) as count\n        FROM verification_logs vl\n        JOIN licenses l ON vl.license_key = l.license_key\n        WHERE 1=1\n        ${productFilter}\n        ${dateFilter}\n        GROUP BY vl.result\n      `;\n      \n      const resultStats = await c.env.DB.prepare(resultQuery).bind(...params).all();\n      \n      // Get verification trend (last 30 days)\n      const trendQuery = `\n        SELECT \n          DATE(vl.created_at) as date,\n          COUNT(*) as total_verifications,\n          SUM(CASE WHEN vl.result = 'success' THEN 1 ELSE 0 END) as successful_verifications\n        FROM verification_logs vl\n        JOIN licenses l ON vl.license_key = l.license_key\n        WHERE vl.created_at >= date('now', '-30 days')\n        ${productFilter ? productFilter.replace('AND', 'AND') : ''}\n        GROUP BY DATE(vl.created_at)\n        ORDER BY date DESC\n      `;\n      \n      const trendParams = admin.role === 'super' ? [] : params.slice(1);\n      const verificationTrend = await c.env.DB.prepare(trendQuery).bind(...trendParams).all();\n      \n      const successfulVerifications = (totalStats?.successful_verifications as number) || 0;\n      const totalVerifications = (totalStats?.total_verifications as number) || 0;\n      const successRate = totalVerifications > 0 ? (successfulVerifications / totalVerifications) * 100 : 0;\n      \n      const stats = {\n        summary: {\n          total_verifications: totalVerifications,\n          successful_verifications: successfulVerifications,\n          failed_verifications: (totalStats?.failed_verifications as number) || 0,\n          success_rate: Math.round(successRate * 100) / 100,\n          period,\n        },\n        by_result: resultStats.results,\n        trend: verificationTrend.results,\n      };\n      \n      return response.success(c, '验证统计获取成功', stats);\n    } catch (error) {\n      console.error('Get verification stats error:', error);\n      return response.error(c, 'Internal server error', 'GET_VERIFICATION_STATS_ERROR', 500);\n    }\n  });\n\n  // Get dashboard overview\n  app.get('/api/stats/dashboard', authMiddleware(), async (c) => {\n    try {\n      const admin = c.get('admin');\n      \n      // Base conditions for normal admins\n      let productFilter = '';\n      let adminFilter = '';\n      const params: any[] = [];\n      \n      if (admin.role !== 'super') {\n        adminFilter = 'WHERE admin_id = ?';\n        params.push(admin.admin_id);\n        \n        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);\n        if (adminInfo) {\n          const { product_ids } = JSON.parse(adminInfo);\n          if (product_ids && product_ids.length > 0) {\n            productFilter = `AND product_id IN (${product_ids.map(() => '?').join(',')})`;\n            params.push(...product_ids);\n          }\n        }\n      }\n      \n      // Get license counts\n      const licenseQuery = `\n        SELECT \n          COUNT(*) as total_licenses,\n          SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_licenses,\n          SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired_licenses,\n          SUM(CASE WHEN status = 'revoked' THEN 1 ELSE 0 END) as revoked_licenses\n        FROM licenses\n        ${adminFilter}\n        ${productFilter}\n      `;\n      \n      const licenseStats = await c.env.DB.prepare(licenseQuery).bind(...params).first();\n      \n      // Get order counts (this month)\n      const orderQuery = `\n        SELECT \n          COUNT(*) as total_orders,\n          SUM(total_price) as total_revenue,\n          SUM(license_count) as total_licenses_sold\n        FROM orders\n        ${adminFilter}\n        ${adminFilter ? 'AND' : 'WHERE'} status = 'completed'\n        AND created_at >= date('now', 'start of month')\n      `;\n      \n      const orderStats = await c.env.DB.prepare(orderQuery).bind(...params.slice(0, admin.role === 'super' ? 0 : 1)).first();\n      \n      // Get verification counts (today)\n      const verificationQuery = `\n        SELECT \n          COUNT(*) as total_verifications,\n          SUM(CASE WHEN result = 'success' THEN 1 ELSE 0 END) as successful_verifications\n        FROM verification_logs vl\n        ${admin.role === 'super' ? '' : 'JOIN licenses l ON vl.license_key = l.license_key'}\n        WHERE vl.created_at >= date('now')\n        ${admin.role === 'super' ? '' : productFilter}\n      `;\n      \n      const verificationParams = admin.role === 'super' ? [] : (productFilter ? params.slice(1) : []);\n      const verificationStats = await c.env.DB.prepare(verificationQuery).bind(...verificationParams).first();\n      \n      // Get active devices count\n      const deviceQuery = `\n        SELECT COUNT(DISTINCT device_id) as active_devices\n        FROM devices d\n        JOIN licenses l ON d.license_id = l.id\n        WHERE d.last_verification >= date('now', '-7 days')\n        ${admin.role === 'super' ? '' : `AND l.product_id IN (${params.slice(1).map(() => '?').join(',')})`}\n      `;\n      \n      const deviceParams = admin.role === 'super' ? [] : params.slice(1);\n      const deviceStats = await c.env.DB.prepare(deviceQuery).bind(...deviceParams).first();\n      \n      const dashboardData = {\n        licenses: {\n          total: (licenseStats?.total_licenses as number) || 0,\n          active: (licenseStats?.active_licenses as number) || 0,\n          expired: (licenseStats?.expired_licenses as number) || 0,\n          revoked: (licenseStats?.revoked_licenses as number) || 0,\n        },\n        orders_this_month: {\n          total: (orderStats?.total_orders as number) || 0,\n          revenue: (orderStats?.total_revenue as number) || 0,\n          licenses_sold: (orderStats?.total_licenses_sold as number) || 0,\n        },\n        verifications_today: {\n          total: (verificationStats?.total_verifications as number) || 0,\n          successful: (verificationStats?.successful_verifications as number) || 0,\n        },\n        active_devices_week: (deviceStats?.active_devices as number) || 0,\n      };\n      \n      return response.success(c, '仪表板统计获取成功', dashboardData);\n    } catch (error) {\n      console.error('Get dashboard stats error:', error);\n      return response.error(c, 'Internal server error', 'GET_DASHBOARD_STATS_ERROR', 500);\n    }\n  });\n}", "import { Hono } from 'hono';\nimport { CloudflareBindings } from '../types/interfaces';\nimport { initializeDatabase } from '../utils/init-db';\nimport * as response from '../utils/response';\n\nexport function setupInitRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {\n  // 数据库初始化端点（仅在开发环境使用）\n  app.post('/init-db', async (c) => {\n    try {\n      // 检查是否已有超级管理员，如果有则拒绝重复初始化\n      const existingSuperAdmin = await c.env.DB.prepare(\n        'SELECT id FROM admins WHERE role = ? LIMIT 1'\n      ).bind('super').first();\n\n      if (existingSuperAdmin) {\n        return response.error(c, '数据库已初始化，无需重复操作', 'DATABASE_ALREADY_INITIALIZED', 400);\n      }\n\n      // 执行数据库初始化\n      await initializeDatabase(c.env);\n\n      return response.success(c, '数据库初始化成功', {\n        message: '管理员账户和示例数据已创建',\n        accounts: [\n          { username: 'root', password: 'password', role: 'super' },\n          { username: 'admin', password: 'password', role: 'normal' }\n        ]\n      });\n\n    } catch (error) {\n      console.error('Database initialization error:', error);\n      return response.error(c, '数据库初始化失败', 'INIT_DATABASE_ERROR', 500);\n    }\n  });\n\n  // 重置数据库端点（危险操作，仅开发环境使用）\n  app.post('/reset-db', async (c) => {\n    try {\n      // 删除所有数据\n      await c.env.DB.prepare('DELETE FROM verification_logs').run();\n      await c.env.DB.prepare('DELETE FROM devices').run();\n      await c.env.DB.prepare('DELETE FROM orders').run();\n      await c.env.DB.prepare('DELETE FROM licenses').run();\n      await c.env.DB.prepare('DELETE FROM admins').run();\n      await c.env.DB.prepare('DELETE FROM products').run();\n\n      // 重新初始化\n      await initializeDatabase(c.env);\n\n      return response.success(c, '数据库重置成功', {\n        message: '所有数据已清除并重新初始化'\n      });\n\n    } catch (error) {\n      console.error('Database reset error:', error);\n      return response.error(c, '数据库重置失败', 'RESET_DATABASE_ERROR', 500);\n    }\n  });\n\n  // 数据库状态检查端点\n  app.get('/db-status', async (c) => {\n    try {\n      const adminCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM admins').first();\n      const productCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM products').first();\n      const licenseCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM licenses').first();\n\n      return response.success(c, '数据库状态查询成功', {\n        admins: (adminCount?.count as number) || 0,\n        products: (productCount?.count as number) || 0,\n        licenses: (licenseCount?.count as number) || 0,\n        initialized: ((adminCount?.count as number) || 0) > 0\n      });\n\n    } catch (error) {\n      console.error('Database status check error:', error);\n      return response.error(c, '数据库状态查询失败', 'DB_STATUS_ERROR', 500);\n    }\n  });\n}", "/**\n * 数据库初始化脚本 - 插入管理员账户\n * 运行命令：pnpm run init-db\n */\n\nimport * as bcrypt from 'bcryptjs';\n\ninterface CloudflareBindings {\n  DB: D1Database;\n  CACHE: KVNamespace;\n  JWT_SECRET: string;\n  API_VERSION: string;\n  ALLOWED_ORIGINS: string;\n}\n\n/**\n * 初始化管理员账户\n */\nasync function initAdminAccounts(DB: D1Database) {\n  console.log('正在初始化管理员账户...');\n\n  try {\n    // 检查超级管理员是否已存在\n    const existingSuperAdmin = await DB.prepare(\n      'SELECT id FROM admins WHERE username = ?'\n    ).bind('root').first();\n\n    if (!existingSuperAdmin) {\n      // 创建超级管理员账户\n      const superAdminPasswordHash = await bcrypt.hash('password', 10);\n      \n      await DB.prepare(`\n        INSERT INTO admins (username, password_hash, role, status, product_ids, created_at, updated_at)\n        VALUES (?, ?, 'super', 'active', NULL, datetime('now'), datetime('now'))\n      `).bind('root', superAdminPasswordHash).run();\n      \n      console.log('✅ 超级管理员账户创建成功 (用户名: root, 密码: password)');\n    } else {\n      console.log('ℹ️ 超级管理员账户已存在，跳过创建');\n    }\n\n    // 检查普通管理员是否已存在\n    const existingNormalAdmin = await DB.prepare(\n      'SELECT id FROM admins WHERE username = ?'\n    ).bind('admin').first();\n\n    if (!existingNormalAdmin) {\n      // 创建普通管理员账户\n      const normalAdminPasswordHash = await bcrypt.hash('password', 10);\n      const assignedProducts = JSON.stringify([1, 2]); // 分配产品1和2\n      \n      await DB.prepare(`\n        INSERT INTO admins (username, password_hash, role, status, product_ids, created_at, updated_at)\n        VALUES (?, ?, 'normal', 'active', ?, datetime('now'), datetime('now'))\n      `).bind('admin', normalAdminPasswordHash, assignedProducts).run();\n      \n      console.log('✅ 普通管理员账户创建成功 (用户名: admin, 密码: password, 分配产品: 1,2)');\n    } else {\n      console.log('ℹ️ 普通管理员账户已存在，跳过创建');\n    }\n\n  } catch (error) {\n    console.error('❌ 创建管理员账户失败:', error);\n    throw error;\n  }\n}\n\n/**\n * 插入示例许可证数据\n */\nasync function insertSampleLicenses(DB: D1Database) {\n  console.log('正在插入示例许可证数据...');\n\n  try {\n    // 获取管理员ID\n    const superAdmin = await DB.prepare('SELECT id FROM admins WHERE username = ?').bind('root').first();\n    const normalAdmin = await DB.prepare('SELECT id FROM admins WHERE username = ?').bind('admin').first();\n\n    if (!superAdmin || !normalAdmin) {\n      throw new Error('管理员账户不存在，请先运行管理员初始化');\n    }\n\n    // 生成示例许可证\n    const sampleLicenses = [\n      {\n        product_id: 1,\n        license_key: 'BASIC-DEMO-001-ABCD-EFGH',\n        expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1年后过期\n        admin_id: superAdmin.id\n      },\n      {\n        product_id: 2,\n        license_key: 'PRO-DEMO-002-IJKL-MNOP',\n        expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),\n        admin_id: normalAdmin.id\n      },\n      {\n        product_id: 3,\n        license_key: 'ENT-DEMO-003-QRST-UVWX',\n        expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),\n        admin_id: superAdmin.id\n      }\n    ];\n\n    for (const license of sampleLicenses) {\n      // 检查许可证是否已存在\n      const existing = await DB.prepare('SELECT id FROM licenses WHERE license_key = ?')\n        .bind(license.license_key).first();\n\n      if (!existing) {\n        await DB.prepare(`\n          INSERT INTO licenses (product_id, license_key, status, expires_at, admin_id, created_at, updated_at)\n          VALUES (?, ?, 'active', ?, ?, datetime('now'), datetime('now'))\n        `).bind(\n          license.product_id,\n          license.license_key,\n          license.expires_at,\n          license.admin_id\n        ).run();\n\n        console.log(`✅ 示例许可证创建成功: ${license.license_key}`);\n      } else {\n        console.log(`ℹ️ 许可证已存在，跳过创建: ${license.license_key}`);\n      }\n    }\n\n  } catch (error) {\n    console.error('❌ 插入示例许可证失败:', error);\n    throw error;\n  }\n}\n\n/**\n * 插入示例订单数据\n */\nasync function insertSampleOrders(DB: D1Database) {\n  console.log('正在插入示例订单数据...');\n\n  try {\n    const normalAdmin = await DB.prepare('SELECT id FROM admins WHERE username = ?').bind('admin').first();\n    \n    if (!normalAdmin) {\n      throw new Error('普通管理员账户不存在');\n    }\n\n    const sampleOrders = [\n      {\n        admin_id: normalAdmin.id,\n        product_id: 1,\n        license_count: 10,\n        unit_price: 99.00,\n        total_price: 990.00,\n        status: 'completed'\n      },\n      {\n        admin_id: normalAdmin.id,\n        product_id: 2,\n        license_count: 5,\n        unit_price: 299.00,\n        total_price: 1495.00,\n        status: 'completed'\n      }\n    ];\n\n    for (const order of sampleOrders) {\n      await DB.prepare(`\n        INSERT INTO orders (admin_id, product_id, license_count, unit_price, total_price, status, created_at, updated_at)\n        VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))\n      `).bind(\n        order.admin_id,\n        order.product_id,\n        order.license_count,\n        order.unit_price,\n        order.total_price,\n        order.status\n      ).run();\n    }\n\n    console.log('✅ 示例订单数据插入成功');\n\n  } catch (error) {\n    console.error('❌ 插入示例订单失败:', error);\n    throw error;\n  }\n}\n\n/**\n * 主初始化函数\n */\nexport async function initializeDatabase(env: CloudflareBindings) {\n  console.log('🚀 开始初始化数据库...');\n  \n  try {\n    // 1. 初始化管理员账户\n    await initAdminAccounts(env.DB);\n    \n    // 2. 插入示例许可证\n    await insertSampleLicenses(env.DB);\n    \n    // 3. 插入示例订单\n    await insertSampleOrders(env.DB);\n    \n    console.log('🎉 数据库初始化完成！');\n    console.log('');\n    console.log('账户信息：');\n    console.log('超级管理员 - 用户名: root, 密码: password');\n    console.log('普通管理员 - 用户名: admin, 密码: password');\n    console.log('');\n    \n  } catch (error) {\n    console.error('💥 数据库初始化失败:', error);\n    throw error;\n  }\n}\n\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAGA;AAAA;AAAA;A;;;;;;;;;ACHA;;;ACAA;;;ACAA;;;ACAA;;;ACAA;;;ACAA;;;ACAA;AACA,IAAI,UAAU,wBAAC,YAAY,SAAS,eAAe;AACjD,SAAO,CAAC,SAAS,SAAS;AACxB,QAAI,QAAQ;AACZ,WAAO,SAAS,CAAC;AACjB,mBAAe,SAAS,GAAG;AACzB,UAAI,KAAK,OAAO;AACd,cAAM,IAAI,MAAM,8BAA8B;AAAA,MAChD;AACA,cAAQ;AACR,UAAI;AACJ,UAAI,UAAU;AACd,UAAI;AACJ,UAAI,WAAW,CAAC,GAAG;AACjB,kBAAU,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;AAC5B,gBAAQ,IAAI,aAAa;AAAA,MAC3B,OAAO;AACL,kBAAU,MAAM,WAAW,UAAU,QAAQ;AAAA,MAC/C;AACA,UAAI,SAAS;AACX,YAAI;AACF,gBAAM,MAAM,QAAQ,SAAS,MAAM,SAAS,IAAI,CAAC,CAAC;AAAA,QACpD,SAAS,KAAK;AACZ,cAAI,eAAe,SAAS,SAAS;AACnC,oBAAQ,QAAQ;AAChB,kBAAM,MAAM,QAAQ,KAAK,OAAO;AAChC,sBAAU;AAAA,UACZ,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,QAAQ,cAAc,SAAS,YAAY;AAC7C,gBAAM,MAAM,WAAW,OAAO;AAAA,QAChC;AAAA,MACF;AACA,UAAI,QAAQ,QAAQ,cAAc,SAAS,UAAU;AACnD,gBAAQ,MAAM;AAAA,MAChB;AACA,aAAO;AAAA,IACT;AAnCe;AAAA,EAoCjB;AACF,GAzCc;;;ACDd;;;ACAA;;;ACAA;AACA,IAAI,mBAAmB,OAAO;;;ACD9B;AAEA,IAAI,YAAY,8BAAO,SAAS,UAA0B,uBAAO,OAAO,IAAI,MAAM;AAChF,QAAM,EAAE,MAAM,OAAO,MAAM,MAAM,IAAI;AACrC,QAAM,UAAU,mBAAmB,cAAc,QAAQ,IAAI,UAAU,QAAQ;AAC/E,QAAM,cAAc,QAAQ,IAAI,cAAc;AAC9C,MAAI,aAAa,WAAW,qBAAqB,KAAK,aAAa,WAAW,mCAAmC,GAAG;AAClH,WAAO,cAAc,SAAS,EAAE,KAAK,IAAI,CAAC;AAAA,EAC5C;AACA,SAAO,CAAC;AACV,GARgB;AAShB,eAAe,cAAc,SAAS,SAAS;AAC7C,QAAM,WAAW,MAAM,QAAQ,SAAS;AACxC,MAAI,UAAU;AACZ,WAAO,0BAA0B,UAAU,OAAO;AAAA,EACpD;AACA,SAAO,CAAC;AACV;AANe;AAOf,SAAS,0BAA0B,UAAU,SAAS;AACpD,QAAM,OAAuB,uBAAO,OAAO,IAAI;AAC/C,WAAS,QAAQ,CAAC,OAAO,QAAQ;AAC/B,UAAM,uBAAuB,QAAQ,OAAO,IAAI,SAAS,IAAI;AAC7D,QAAI,CAAC,sBAAsB;AACzB,WAAK,GAAG,IAAI;AAAA,IACd,OAAO;AACL,6BAAuB,MAAM,KAAK,KAAK;AAAA,IACzC;AAAA,EACF,CAAC;AACD,MAAI,QAAQ,KAAK;AACf,WAAO,QAAQ,IAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7C,YAAM,uBAAuB,IAAI,SAAS,GAAG;AAC7C,UAAI,sBAAsB;AACxB,kCAA0B,MAAM,KAAK,KAAK;AAC1C,eAAO,KAAK,GAAG;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AApBS;AAqBT,IAAI,yBAAyB,wBAAC,MAAM,KAAK,UAAU;AACjD,MAAI,KAAK,GAAG,MAAM,QAAQ;AACxB,QAAI,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG;AAC5B;AACA,WAAK,GAAG,EAAE,KAAK,KAAK;AAAA,IACtB,OAAO;AACL,WAAK,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,KAAK;AAAA,IAC/B;AAAA,EACF,OAAO;AACL,QAAI,CAAC,IAAI,SAAS,IAAI,GAAG;AACvB,WAAK,GAAG,IAAI;AAAA,IACd,OAAO;AACL,WAAK,GAAG,IAAI,CAAC,KAAK;AAAA,IACpB;AAAA,EACF;AACF,GAf6B;AAgB7B,IAAI,4BAA4B,wBAAC,MAAM,KAAK,UAAU;AACpD,MAAI,aAAa;AACjB,QAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,OAAK,QAAQ,CAAC,MAAM,UAAU;AAC5B,QAAI,UAAU,KAAK,SAAS,GAAG;AAC7B,iBAAW,IAAI,IAAI;AAAA,IACrB,OAAO;AACL,UAAI,CAAC,WAAW,IAAI,KAAK,OAAO,WAAW,IAAI,MAAM,YAAY,MAAM,QAAQ,WAAW,IAAI,CAAC,KAAK,WAAW,IAAI,aAAa,MAAM;AACpI,mBAAW,IAAI,IAAoB,uBAAO,OAAO,IAAI;AAAA,MACvD;AACA,mBAAa,WAAW,IAAI;AAAA,IAC9B;AAAA,EACF,CAAC;AACH,GAbgC;;;ACvDhC;AACA,IAAI,YAAY,wBAAC,SAAS;AACxB,QAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,MAAI,MAAM,CAAC,MAAM,IAAI;AACnB,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT,GANgB;AAOhB,IAAI,mBAAmB,wBAAC,cAAc;AACpC,QAAM,EAAE,QAAQ,KAAK,IAAI,sBAAsB,SAAS;AACxD,QAAM,QAAQ,UAAU,IAAI;AAC5B,SAAO,kBAAkB,OAAO,MAAM;AACxC,GAJuB;AAKvB,IAAI,wBAAwB,wBAAC,SAAS;AACpC,QAAM,SAAS,CAAC;AAChB,SAAO,KAAK,QAAQ,cAAc,CAAC,OAAO,UAAU;AAClD,UAAM,OAAO,IAAI,KAAK;AACtB,WAAO,KAAK,CAAC,MAAM,KAAK,CAAC;AACzB,WAAO;AAAA,EACT,CAAC;AACD,SAAO,EAAE,QAAQ,KAAK;AACxB,GAR4B;AAS5B,IAAI,oBAAoB,wBAAC,OAAO,WAAW;AACzC,WAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACvB,aAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,UAAI,MAAM,CAAC,EAAE,SAAS,IAAI,GAAG;AAC3B,cAAM,CAAC,IAAI,MAAM,CAAC,EAAE,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC;AAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT,GAXwB;AAYxB,IAAI,eAAe,CAAC;AACpB,IAAI,aAAa,wBAAC,OAAO,SAAS;AAChC,MAAI,UAAU,KAAK;AACjB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,MAAM,MAAM,6BAA6B;AACvD,MAAI,OAAO;AACT,UAAM,WAAW,GAAG,KAAK,IAAI,IAAI;AACjC,QAAI,CAAC,aAAa,QAAQ,GAAG;AAC3B,UAAI,MAAM,CAAC,GAAG;AACZ,qBAAa,QAAQ,IAAI,QAAQ,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,MAAM,CAAC,UAAU,MAAM,CAAC,GAAG,IAAI,OAAO,IAAI,MAAM,CAAC,CAAC,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,IAAI,OAAO,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAAA,MACpL,OAAO;AACL,qBAAa,QAAQ,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,IAAI;AAAA,MACjD;AAAA,IACF;AACA,WAAO,aAAa,QAAQ;AAAA,EAC9B;AACA,SAAO;AACT,GAjBiB;AAkBjB,IAAI,YAAY,wBAAC,KAAK,YAAY;AAChC,MAAI;AACF,WAAO,QAAQ,GAAG;AAAA,EACpB,QAAQ;AACN,WAAO,IAAI,QAAQ,yBAAyB,CAAC,UAAU;AACrD,UAAI;AACF,eAAO,QAAQ,KAAK;AAAA,MACtB,QAAQ;AACN,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AACF,GAZgB;AAahB,IAAI,eAAe,wBAAC,QAAQ,UAAU,KAAK,SAAS,GAAjC;AACnB,IAAI,UAAU,wBAAC,YAAY;AACzB,QAAM,MAAM,QAAQ;AACpB,QAAM,QAAQ,IAAI;AAAA,IAChB;AAAA,IACA,IAAI,WAAW,CAAC,MAAM,KAAK,KAAK;AAAA,EAClC;AACA,MAAI,IAAI;AACR,SAAO,IAAI,IAAI,QAAQ,KAAK;AAC1B,UAAM,WAAW,IAAI,WAAW,CAAC;AACjC,QAAI,aAAa,IAAI;AACnB,YAAM,aAAa,IAAI,QAAQ,KAAK,CAAC;AACrC,YAAM,OAAO,IAAI,MAAM,OAAO,eAAe,KAAK,SAAS,UAAU;AACrE,aAAO,aAAa,KAAK,SAAS,KAAK,IAAI,KAAK,QAAQ,QAAQ,OAAO,IAAI,IAAI;AAAA,IACjF,WAAW,aAAa,IAAI;AAC1B;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,MAAM,OAAO,CAAC;AAC3B,GAlBc;AAuBd,IAAI,kBAAkB,wBAAC,YAAY;AACjC,QAAM,SAAS,QAAQ,OAAO;AAC9B,SAAO,OAAO,SAAS,KAAK,OAAO,GAAG,EAAE,MAAM,MAAM,OAAO,MAAM,GAAG,EAAE,IAAI;AAC5E,GAHsB;AAItB,IAAI,YAAY,wBAAC,MAAM,QAAQ,SAAS;AACtC,MAAI,KAAK,QAAQ;AACf,UAAM,UAAU,KAAK,GAAG,IAAI;AAAA,EAC9B;AACA,SAAO,GAAG,OAAO,CAAC,MAAM,MAAM,KAAK,GAAG,GAAG,IAAI,GAAG,QAAQ,MAAM,KAAK,GAAG,MAAM,GAAG,EAAE,MAAM,MAAM,KAAK,GAAG,GAAG,MAAM,CAAC,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI,GAAG,EAAE;AACjJ,GALgB;AAMhB,IAAI,yBAAyB,wBAAC,SAAS;AACrC,MAAI,KAAK,WAAW,KAAK,SAAS,CAAC,MAAM,MAAM,CAAC,KAAK,SAAS,GAAG,GAAG;AAClE,WAAO;AAAA,EACT;AACA,QAAM,WAAW,KAAK,MAAM,GAAG;AAC/B,QAAM,UAAU,CAAC;AACjB,MAAI,WAAW;AACf,WAAS,QAAQ,CAAC,YAAY;AAC5B,QAAI,YAAY,MAAM,CAAC,KAAK,KAAK,OAAO,GAAG;AACzC,kBAAY,MAAM;AAAA,IACpB,WAAW,KAAK,KAAK,OAAO,GAAG;AAC7B,UAAI,KAAK,KAAK,OAAO,GAAG;AACtB,YAAI,QAAQ,WAAW,KAAK,aAAa,IAAI;AAC3C,kBAAQ,KAAK,GAAG;AAAA,QAClB,OAAO;AACL,kBAAQ,KAAK,QAAQ;AAAA,QACvB;AACA,cAAM,kBAAkB,QAAQ,QAAQ,KAAK,EAAE;AAC/C,oBAAY,MAAM;AAClB,gBAAQ,KAAK,QAAQ;AAAA,MACvB,OAAO;AACL,oBAAY,MAAM;AAAA,MACpB;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,QAAQ,OAAO,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;AACvD,GA1B6B;AA2B7B,IAAI,aAAa,wBAAC,UAAU;AAC1B,MAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,GAAG,MAAM,IAAI;AAC7B,YAAQ,MAAM,QAAQ,OAAO,GAAG;AAAA,EAClC;AACA,SAAO,MAAM,QAAQ,GAAG,MAAM,KAAK,UAAU,OAAO,mBAAmB,IAAI;AAC7E,GARiB;AASjB,IAAI,iBAAiB,wBAAC,KAAK,KAAK,aAAa;AAC3C,MAAI;AACJ,MAAI,CAAC,YAAY,OAAO,CAAC,OAAO,KAAK,GAAG,GAAG;AACzC,QAAI,YAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,CAAC;AACxC,QAAI,cAAc,IAAI;AACpB,kBAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,CAAC;AAAA,IACtC;AACA,WAAO,cAAc,IAAI;AACvB,YAAM,kBAAkB,IAAI,WAAW,YAAY,IAAI,SAAS,CAAC;AACjE,UAAI,oBAAoB,IAAI;AAC1B,cAAM,aAAa,YAAY,IAAI,SAAS;AAC5C,cAAM,WAAW,IAAI,QAAQ,KAAK,UAAU;AAC5C,eAAO,WAAW,IAAI,MAAM,YAAY,aAAa,KAAK,SAAS,QAAQ,CAAC;AAAA,MAC9E,WAAW,mBAAmB,MAAM,MAAM,eAAe,GAAG;AAC1D,eAAO;AAAA,MACT;AACA,kBAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,YAAY,CAAC;AAAA,IAClD;AACA,cAAU,OAAO,KAAK,GAAG;AACzB,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,UAAU,CAAC;AACjB,cAAY,OAAO,KAAK,GAAG;AAC3B,MAAI,WAAW,IAAI,QAAQ,KAAK,CAAC;AACjC,SAAO,aAAa,IAAI;AACtB,UAAM,eAAe,IAAI,QAAQ,KAAK,WAAW,CAAC;AAClD,QAAI,aAAa,IAAI,QAAQ,KAAK,QAAQ;AAC1C,QAAI,aAAa,gBAAgB,iBAAiB,IAAI;AACpD,mBAAa;AAAA,IACf;AACA,QAAI,OAAO,IAAI;AAAA,MACb,WAAW;AAAA,MACX,eAAe,KAAK,iBAAiB,KAAK,SAAS,eAAe;AAAA,IACpE;AACA,QAAI,SAAS;AACX,aAAO,WAAW,IAAI;AAAA,IACxB;AACA,eAAW;AACX,QAAI,SAAS,IAAI;AACf;AAAA,IACF;AACA,QAAI;AACJ,QAAI,eAAe,IAAI;AACrB,cAAQ;AAAA,IACV,OAAO;AACL,cAAQ,IAAI,MAAM,aAAa,GAAG,iBAAiB,KAAK,SAAS,YAAY;AAC7E,UAAI,SAAS;AACX,gBAAQ,WAAW,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,UAAU;AACZ,UAAI,EAAE,QAAQ,IAAI,KAAK,MAAM,QAAQ,QAAQ,IAAI,CAAC,IAAI;AACpD,gBAAQ,IAAI,IAAI,CAAC;AAAA,MACnB;AACA;AACA,cAAQ,IAAI,EAAE,KAAK,KAAK;AAAA,IAC1B,OAAO;AACL,cAAQ,IAAI,MAAM;AAAA,IACpB;AAAA,EACF;AACA,SAAO,MAAM,QAAQ,GAAG,IAAI;AAC9B,GA/DqB;AAgErB,IAAI,gBAAgB;AACpB,IAAI,iBAAiB,wBAAC,KAAK,QAAQ;AACjC,SAAO,eAAe,KAAK,KAAK,IAAI;AACtC,GAFqB;AAGrB,IAAI,sBAAsB;;;AHxM1B,IAAI,wBAAwB,wBAAC,QAAQ,UAAU,KAAK,mBAAmB,GAA3C;AAC5B,IAAI,cAAc,MAAM;AAAA,EALxB,OAKwB;AAAA;AAAA;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb;AAAA,EACA,YAAY,CAAC;AAAA,EACb,YAAY,SAAS,OAAO,KAAK,cAAc,CAAC,CAAC,CAAC,GAAG;AACnD,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,iBAAiB,CAAC;AAAA,EACzB;AAAA,EACA,MAAM,KAAK;AACT,WAAO,MAAM,KAAK,iBAAiB,GAAG,IAAI,KAAK,qBAAqB;AAAA,EACtE;AAAA,EACA,iBAAiB,KAAK;AACpB,UAAM,WAAW,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,EAAE,GAAG;AAC7D,UAAM,QAAQ,KAAK,eAAe,QAAQ;AAC1C,WAAO,QAAQ,KAAK,KAAK,KAAK,IAAI,sBAAsB,KAAK,IAAI,QAAQ;AAAA,EAC3E;AAAA,EACA,uBAAuB;AACrB,UAAM,UAAU,CAAC;AACjB,UAAM,OAAO,OAAO,KAAK,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,CAAC;AACjE,eAAW,OAAO,MAAM;AACtB,YAAM,QAAQ,KAAK,eAAe,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,EAAE,GAAG,CAAC;AAC/E,UAAI,SAAS,OAAO,UAAU,UAAU;AACtC,gBAAQ,GAAG,IAAI,KAAK,KAAK,KAAK,IAAI,sBAAsB,KAAK,IAAI;AAAA,MACnE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe,UAAU;AACvB,WAAO,KAAK,aAAa,CAAC,IAAI,KAAK,aAAa,CAAC,EAAE,QAAQ,IAAI;AAAA,EACjE;AAAA,EACA,MAAM,KAAK;AACT,WAAO,cAAc,KAAK,KAAK,GAAG;AAAA,EACpC;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,eAAe,KAAK,KAAK,GAAG;AAAA,EACrC;AAAA,EACA,OAAO,MAAM;AACX,QAAI,MAAM;AACR,aAAO,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK;AAAA,IACvC;AACA,UAAM,aAAa,CAAC;AACpB,SAAK,IAAI,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACvC,iBAAW,GAAG,IAAI;AAAA,IACpB,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,MAAM,UAAU,SAAS;AACvB,WAAO,KAAK,UAAU,eAAe,MAAM,UAAU,MAAM,OAAO;AAAA,EACpE;AAAA,EACA,cAAc,wBAAC,QAAQ;AACrB,UAAM,EAAE,WAAW,KAAAA,KAAI,IAAI;AAC3B,UAAM,aAAa,UAAU,GAAG;AAChC,QAAI,YAAY;AACd,aAAO;AAAA,IACT;AACA,UAAM,eAAe,OAAO,KAAK,SAAS,EAAE,CAAC;AAC7C,QAAI,cAAc;AAChB,aAAO,UAAU,YAAY,EAAE,KAAK,CAAC,SAAS;AAC5C,YAAI,iBAAiB,QAAQ;AAC3B,iBAAO,KAAK,UAAU,IAAI;AAAA,QAC5B;AACA,eAAO,IAAI,SAAS,IAAI,EAAE,GAAG,EAAE;AAAA,MACjC,CAAC;AAAA,IACH;AACA,WAAO,UAAU,GAAG,IAAIA,KAAI,GAAG,EAAE;AAAA,EACnC,GAhBc;AAAA,EAiBd,OAAO;AACL,WAAO,KAAK,YAAY,MAAM,EAAE,KAAK,CAAC,SAAS,KAAK,MAAM,IAAI,CAAC;AAAA,EACjE;AAAA,EACA,OAAO;AACL,WAAO,KAAK,YAAY,MAAM;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,YAAY,aAAa;AAAA,EACvC;AAAA,EACA,OAAO;AACL,WAAO,KAAK,YAAY,MAAM;AAAA,EAChC;AAAA,EACA,WAAW;AACT,WAAO,KAAK,YAAY,UAAU;AAAA,EACpC;AAAA,EACA,iBAAiB,QAAQ,MAAM;AAC7B,SAAK,eAAe,MAAM,IAAI;AAAA,EAChC;AAAA,EACA,MAAM,QAAQ;AACZ,WAAO,KAAK,eAAe,MAAM;AAAA,EACnC;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,KAAK,gBAAgB,IAAI;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,KAAK;AAAA,EACxD;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,KAAK,EAAE,KAAK,UAAU,EAAE;AAAA,EAC3E;AACF;;;AIhHA;AACA,IAAI,2BAA2B;AAAA,EAC7B,WAAW;AAAA,EACX,cAAc;AAAA,EACd,QAAQ;AACV;AACA,IAAI,MAAM,wBAAC,OAAO,cAAc;AAC9B,QAAM,gBAAgB,IAAI,OAAO,KAAK;AACtC,gBAAc,YAAY;AAC1B,gBAAc,YAAY;AAC1B,SAAO;AACT,GALU;AAgFV,IAAI,kBAAkB,8BAAO,KAAK,OAAO,mBAAmB,SAAS,WAAW;AAC9E,MAAI,OAAO,QAAQ,YAAY,EAAE,eAAe,SAAS;AACvD,QAAI,EAAE,eAAe,UAAU;AAC7B,YAAM,IAAI,SAAS;AAAA,IACrB;AACA,QAAI,eAAe,SAAS;AAC1B,YAAM,MAAM;AAAA,IACd;AAAA,EACF;AACA,QAAM,YAAY,IAAI;AACtB,MAAI,CAAC,WAAW,QAAQ;AACtB,WAAO,QAAQ,QAAQ,GAAG;AAAA,EAC5B;AACA,MAAI,QAAQ;AACV,WAAO,CAAC,KAAK;AAAA,EACf,OAAO;AACL,aAAS,CAAC,GAAG;AAAA,EACf;AACA,QAAM,SAAS,QAAQ,IAAI,UAAU,IAAI,CAAC,MAAM,EAAE,EAAE,OAAO,QAAQ,QAAQ,CAAC,CAAC,CAAC,EAAE;AAAA,IAC9E,CAAC,QAAQ,QAAQ;AAAA,MACf,IAAI,OAAO,OAAO,EAAE,IAAI,CAAC,SAAS,gBAAgB,MAAM,OAAO,OAAO,SAAS,MAAM,CAAC;AAAA,IACxF,EAAE,KAAK,MAAM,OAAO,CAAC,CAAC;AAAA,EACxB;AACA,MAAI,mBAAmB;AACrB,WAAO,IAAI,MAAM,QAAQ,SAAS;AAAA,EACpC,OAAO;AACL,WAAO;AAAA,EACT;AACF,GA5BsB;;;ALnFtB,IAAI,aAAa;AACjB,IAAI,wBAAwB,wBAAC,aAAa,YAAY;AACpD,SAAO;AAAA,IACL,gBAAgB;AAAA,IAChB,GAAG;AAAA,EACL;AACF,GAL4B;AAM5B,IAAI,UAAU,MAAM;AAAA,EAVpB,OAUoB;AAAA;AAAA;AAAA,EAClB;AAAA,EACA;AAAA,EACA,MAAM,CAAC;AAAA,EACP;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,KAAK,SAAS;AACxB,SAAK,cAAc;AACnB,QAAI,SAAS;AACX,WAAK,gBAAgB,QAAQ;AAC7B,WAAK,MAAM,QAAQ;AACnB,WAAK,mBAAmB,QAAQ;AAChC,WAAK,QAAQ,QAAQ;AACrB,WAAK,eAAe,QAAQ;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,IAAI,MAAM;AACR,SAAK,SAAS,IAAI,YAAY,KAAK,aAAa,KAAK,OAAO,KAAK,YAAY;AAC7E,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ;AACV,QAAI,KAAK,iBAAiB,iBAAiB,KAAK,eAAe;AAC7D,aAAO,KAAK;AAAA,IACd,OAAO;AACL,YAAM,MAAM,gCAAgC;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,KAAK,eAAe;AACtB,aAAO,KAAK;AAAA,IACd,OAAO;AACL,YAAM,MAAM,sCAAsC;AAAA,IACpD;AAAA,EACF;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,SAAS,IAAI,SAAS,MAAM;AAAA,MACtC,SAAS,KAAK,qBAAqB,IAAI,QAAQ;AAAA,IACjD,CAAC;AAAA,EACH;AAAA,EACA,IAAI,IAAI,MAAM;AACZ,QAAI,KAAK,QAAQ,MAAM;AACrB,aAAO,IAAI,SAAS,KAAK,MAAM,IAAI;AACnC,iBAAW,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,QAAQ,QAAQ,GAAG;AAChD,YAAI,MAAM,gBAAgB;AACxB;AAAA,QACF;AACA,YAAI,MAAM,cAAc;AACtB,gBAAM,UAAU,KAAK,KAAK,QAAQ,aAAa;AAC/C,eAAK,QAAQ,OAAO,YAAY;AAChC,qBAAW,UAAU,SAAS;AAC5B,iBAAK,QAAQ,OAAO,cAAc,MAAM;AAAA,UAC1C;AAAA,QACF,OAAO;AACL,eAAK,QAAQ,IAAI,GAAG,CAAC;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,SAAK,OAAO;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,SAAS,2BAAI,SAAS;AACpB,SAAK,cAAc,CAAC,YAAY,KAAK,KAAK,OAAO;AACjD,WAAO,KAAK,UAAU,GAAG,IAAI;AAAA,EAC/B,GAHS;AAAA,EAIT,YAAY,wBAAC,WAAW,KAAK,UAAU,QAA3B;AAAA,EACZ,YAAY,6BAAM,KAAK,SAAX;AAAA,EACZ,cAAc,wBAAC,aAAa;AAC1B,SAAK,YAAY;AAAA,EACnB,GAFc;AAAA,EAGd,SAAS,wBAAC,MAAM,OAAO,YAAY;AACjC,QAAI,KAAK,WAAW;AAClB,WAAK,OAAO,IAAI,SAAS,KAAK,KAAK,MAAM,KAAK,IAAI;AAAA,IACpD;AACA,UAAM,UAAU,KAAK,OAAO,KAAK,KAAK,UAAU,KAAK,qBAAqB,IAAI,QAAQ;AACtF,QAAI,UAAU,QAAQ;AACpB,cAAQ,OAAO,IAAI;AAAA,IACrB,WAAW,SAAS,QAAQ;AAC1B,cAAQ,OAAO,MAAM,KAAK;AAAA,IAC5B,OAAO;AACL,cAAQ,IAAI,MAAM,KAAK;AAAA,IACzB;AAAA,EACF,GAZS;AAAA,EAaT,SAAS,wBAAC,WAAW;AACnB,SAAK,UAAU;AAAA,EACjB,GAFS;AAAA,EAGT,MAAM,wBAAC,KAAK,UAAU;AACpB,SAAK,SAAyB,oBAAI,IAAI;AACtC,SAAK,KAAK,IAAI,KAAK,KAAK;AAAA,EAC1B,GAHM;AAAA,EAIN,MAAM,wBAAC,QAAQ;AACb,WAAO,KAAK,OAAO,KAAK,KAAK,IAAI,GAAG,IAAI;AAAA,EAC1C,GAFM;AAAA,EAGN,IAAI,MAAM;AACR,QAAI,CAAC,KAAK,MAAM;AACd,aAAO,CAAC;AAAA,IACV;AACA,WAAO,OAAO,YAAY,KAAK,IAAI;AAAA,EACrC;AAAA,EACA,aAAa,MAAM,KAAK,SAAS;AAC/B,UAAM,kBAAkB,KAAK,OAAO,IAAI,QAAQ,KAAK,KAAK,OAAO,IAAI,KAAK,oBAAoB,IAAI,QAAQ;AAC1G,QAAI,OAAO,QAAQ,YAAY,aAAa,KAAK;AAC/C,YAAM,aAAa,IAAI,mBAAmB,UAAU,IAAI,UAAU,IAAI,QAAQ,IAAI,OAAO;AACzF,iBAAW,CAAC,KAAK,KAAK,KAAK,YAAY;AACrC,YAAI,IAAI,YAAY,MAAM,cAAc;AACtC,0BAAgB,OAAO,KAAK,KAAK;AAAA,QACnC,OAAO;AACL,0BAAgB,IAAI,KAAK,KAAK;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS;AACX,iBAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,OAAO,GAAG;AAC5C,YAAI,OAAO,MAAM,UAAU;AACzB,0BAAgB,IAAI,GAAG,CAAC;AAAA,QAC1B,OAAO;AACL,0BAAgB,OAAO,CAAC;AACxB,qBAAW,MAAM,GAAG;AAClB,4BAAgB,OAAO,GAAG,EAAE;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,OAAO,QAAQ,WAAW,MAAM,KAAK,UAAU,KAAK;AACnE,WAAO,IAAI,SAAS,MAAM,EAAE,QAAQ,SAAS,gBAAgB,CAAC;AAAA,EAChE;AAAA,EACA,cAAc,2BAAI,SAAS,KAAK,aAAa,GAAG,IAAI,GAAtC;AAAA,EACd,OAAO,wBAAC,MAAM,KAAK,YAAY,KAAK,aAAa,MAAM,KAAK,OAAO,GAA5D;AAAA,EACP,OAAO,wBAAC,MAAM,KAAK,YAAY;AAC7B,WAAO,CAAC,KAAK,oBAAoB,CAAC,KAAK,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,YAAY,IAAI,SAAS,IAAI,IAAI,KAAK;AAAA,MAChH;AAAA,MACA;AAAA,MACA,sBAAsB,YAAY,OAAO;AAAA,IAC3C;AAAA,EACF,GANO;AAAA,EAOP,OAAO,wBAAC,QAAQ,KAAK,YAAY;AAC/B,WAAO,KAAK;AAAA,MACV,KAAK,UAAU,MAAM;AAAA,MACrB;AAAA,MACA,sBAAsB,oBAAoB,OAAO;AAAA,IACnD;AAAA,EACF,GANO;AAAA,EAOP,OAAO,wBAAC,MAAM,KAAK,YAAY;AAC7B,UAAM,MAAM,wBAAC,UAAU,KAAK,aAAa,OAAO,KAAK,sBAAsB,4BAA4B,OAAO,CAAC,GAAnG;AACZ,WAAO,OAAO,SAAS,WAAW,gBAAgB,MAAM,yBAAyB,WAAW,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,IAAI,IAAI;AAAA,EAC7H,GAHO;AAAA,EAIP,WAAW,wBAAC,UAAU,WAAW;AAC/B,UAAM,iBAAiB,OAAO,QAAQ;AACtC,SAAK;AAAA,MACH;AAAA,MACA,CAAC,eAAe,KAAK,cAAc,IAAI,iBAAiB,UAAU,cAAc;AAAA,IAClF;AACA,WAAO,KAAK,YAAY,MAAM,UAAU,GAAG;AAAA,EAC7C,GAPW;AAAA,EAQX,WAAW,6BAAM;AACf,SAAK,qBAAqB,MAAM,IAAI,SAAS;AAC7C,WAAO,KAAK,iBAAiB,IAAI;AAAA,EACnC,GAHW;AAIb;;;AMjLA;AACA,IAAI,kBAAkB;AACtB,IAAI,4BAA4B;AAChC,IAAI,UAAU,CAAC,OAAO,QAAQ,OAAO,UAAU,WAAW,OAAO;AACjE,IAAI,mCAAmC;AACvC,IAAI,uBAAuB,cAAc,MAAM;AAAA,EAL/C,OAK+C;AAAA;AAAA;AAC/C;;;ACNA;AACA,IAAI,mBAAmB;;;ATKvB,IAAI,kBAAkB,wBAAC,MAAM;AAC3B,SAAO,EAAE,KAAK,iBAAiB,GAAG;AACpC,GAFsB;AAGtB,IAAI,eAAe,wBAAC,KAAK,MAAM;AAC7B,MAAI,iBAAiB,KAAK;AACxB,UAAM,MAAM,IAAI,YAAY;AAC5B,WAAO,EAAE,YAAY,IAAI,MAAM,GAAG;AAAA,EACpC;AACA,UAAQ,MAAM,GAAG;AACjB,SAAO,EAAE,KAAK,yBAAyB,GAAG;AAC5C,GAPmB;AAQnB,IAAI,OAAO,MAAM;AAAA,EAjBjB,OAiBiB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,SAAS,CAAC;AAAA,EACV,YAAY,UAAU,CAAC,GAAG;AACxB,UAAM,aAAa,CAAC,GAAG,SAAS,yBAAyB;AACzD,eAAW,QAAQ,CAAC,WAAW;AAC7B,WAAK,MAAM,IAAI,CAAC,UAAU,SAAS;AACjC,YAAI,OAAO,UAAU,UAAU;AAC7B,eAAK,QAAQ;AAAA,QACf,OAAO;AACL,eAAK,UAAU,QAAQ,KAAK,OAAO,KAAK;AAAA,QAC1C;AACA,aAAK,QAAQ,CAAC,YAAY;AACxB,eAAK,UAAU,QAAQ,KAAK,OAAO,OAAO;AAAA,QAC5C,CAAC;AACD,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,SAAK,KAAK,CAAC,QAAQ,SAAS,aAAa;AACvC,iBAAW,KAAK,CAAC,IAAI,EAAE,KAAK,GAAG;AAC7B,aAAK,QAAQ;AACb,mBAAW,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG;AAC/B,mBAAS,IAAI,CAAC,YAAY;AACxB,iBAAK,UAAU,EAAE,YAAY,GAAG,KAAK,OAAO,OAAO;AAAA,UACrD,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,SAAK,MAAM,CAAC,SAAS,aAAa;AAChC,UAAI,OAAO,SAAS,UAAU;AAC5B,aAAK,QAAQ;AAAA,MACf,OAAO;AACL,aAAK,QAAQ;AACb,iBAAS,QAAQ,IAAI;AAAA,MACvB;AACA,eAAS,QAAQ,CAAC,YAAY;AAC5B,aAAK,UAAU,iBAAiB,KAAK,OAAO,OAAO;AAAA,MACrD,CAAC;AACD,aAAO;AAAA,IACT;AACA,UAAM,EAAE,QAAQ,GAAG,qBAAqB,IAAI;AAC5C,WAAO,OAAO,MAAM,oBAAoB;AACxC,SAAK,UAAU,UAAU,OAAO,QAAQ,WAAW,UAAU;AAAA,EAC/D;AAAA,EACA,SAAS;AACP,UAAM,QAAQ,IAAI,KAAK;AAAA,MACrB,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,UAAM,eAAe,KAAK;AAC1B,UAAM,mBAAmB,KAAK;AAC9B,UAAM,SAAS,KAAK;AACpB,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,MAAM,MAAMC,MAAK;AACf,UAAM,SAAS,KAAK,SAAS,IAAI;AACjC,IAAAA,KAAI,OAAO,IAAI,CAAC,MAAM;AACpB,UAAI;AACJ,UAAIA,KAAI,iBAAiB,cAAc;AACrC,kBAAU,EAAE;AAAA,MACd,OAAO;AACL,kBAAU,8BAAO,GAAG,UAAU,MAAM,QAAQ,CAAC,GAAGA,KAAI,YAAY,EAAE,GAAG,MAAM,EAAE,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAtF;AACV,gBAAQ,gBAAgB,IAAI,EAAE;AAAA,MAChC;AACA,aAAO,UAAU,EAAE,QAAQ,EAAE,MAAM,OAAO;AAAA,IAC5C,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,SAAS,MAAM;AACb,UAAM,SAAS,KAAK,OAAO;AAC3B,WAAO,YAAY,UAAU,KAAK,WAAW,IAAI;AACjD,WAAO;AAAA,EACT;AAAA,EACA,UAAU,wBAAC,YAAY;AACrB,SAAK,eAAe;AACpB,WAAO;AAAA,EACT,GAHU;AAAA,EAIV,WAAW,wBAAC,YAAY;AACtB,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT,GAHW;AAAA,EAIX,MAAM,MAAM,oBAAoB,SAAS;AACvC,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS;AACX,UAAI,OAAO,YAAY,YAAY;AACjC,wBAAgB;AAAA,MAClB,OAAO;AACL,wBAAgB,QAAQ;AACxB,YAAI,QAAQ,mBAAmB,OAAO;AACpC,2BAAiB,wBAAC,YAAY,SAAb;AAAA,QACnB,OAAO;AACL,2BAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,UAAM,aAAa,gBAAgB,CAAC,MAAM;AACxC,YAAM,WAAW,cAAc,CAAC;AAChC,aAAO,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAAA,IACvD,IAAI,CAAC,MAAM;AACT,UAAI,mBAAmB;AACvB,UAAI;AACF,2BAAmB,EAAE;AAAA,MACvB,QAAQ;AAAA,MACR;AACA,aAAO,CAAC,EAAE,KAAK,gBAAgB;AAAA,IACjC;AACA,wBAAoB,MAAM;AACxB,YAAM,aAAa,UAAU,KAAK,WAAW,IAAI;AACjD,YAAM,mBAAmB,eAAe,MAAM,IAAI,WAAW;AAC7D,aAAO,CAAC,YAAY;AAClB,cAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,YAAI,WAAW,IAAI,SAAS,MAAM,gBAAgB,KAAK;AACvD,eAAO,IAAI,QAAQ,KAAK,OAAO;AAAA,MACjC;AAAA,IACF,GAAG;AACH,UAAM,UAAU,8BAAO,GAAG,SAAS;AACjC,YAAM,MAAM,MAAM,mBAAmB,eAAe,EAAE,IAAI,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC;AAChF,UAAI,KAAK;AACP,eAAO;AAAA,MACT;AACA,YAAM,KAAK;AAAA,IACb,GANgB;AAOhB,SAAK,UAAU,iBAAiB,UAAU,MAAM,GAAG,GAAG,OAAO;AAC7D,WAAO;AAAA,EACT;AAAA,EACA,UAAU,QAAQ,MAAM,SAAS;AAC/B,aAAS,OAAO,YAAY;AAC5B,WAAO,UAAU,KAAK,WAAW,IAAI;AACrC,UAAM,IAAI,EAAE,UAAU,KAAK,WAAW,MAAM,QAAQ,QAAQ;AAC5D,SAAK,OAAO,IAAI,QAAQ,MAAM,CAAC,SAAS,CAAC,CAAC;AAC1C,SAAK,OAAO,KAAK,CAAC;AAAA,EACpB;AAAA,EACA,aAAa,KAAK,GAAG;AACnB,QAAI,eAAe,OAAO;AACxB,aAAO,KAAK,aAAa,KAAK,CAAC;AAAA,IACjC;AACA,UAAM;AAAA,EACR;AAAA,EACA,UAAU,SAAS,cAAc,KAAK,QAAQ;AAC5C,QAAI,WAAW,QAAQ;AACrB,cAAQ,YAAY,IAAI,SAAS,MAAM,MAAM,KAAK,UAAU,SAAS,cAAc,KAAK,KAAK,CAAC,GAAG;AAAA,IACnG;AACA,UAAM,OAAO,KAAK,QAAQ,SAAS,EAAE,IAAI,CAAC;AAC1C,UAAM,cAAc,KAAK,OAAO,MAAM,QAAQ,IAAI;AAClD,UAAM,IAAI,IAAI,QAAQ,SAAS;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,KAAK;AAAA,IACxB,CAAC;AACD,QAAI,YAAY,CAAC,EAAE,WAAW,GAAG;AAC/B,UAAI;AACJ,UAAI;AACF,cAAM,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY;AAC3C,YAAE,MAAM,MAAM,KAAK,iBAAiB,CAAC;AAAA,QACvC,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,eAAO,KAAK,aAAa,KAAK,CAAC;AAAA,MACjC;AACA,aAAO,eAAe,UAAU,IAAI;AAAA,QAClC,CAAC,aAAa,aAAa,EAAE,YAAY,EAAE,MAAM,KAAK,iBAAiB,CAAC;AAAA,MAC1E,EAAE,MAAM,CAAC,QAAQ,KAAK,aAAa,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,iBAAiB,CAAC;AAAA,IAC9E;AACA,UAAM,WAAW,QAAQ,YAAY,CAAC,GAAG,KAAK,cAAc,KAAK,gBAAgB;AACjF,YAAQ,YAAY;AAClB,UAAI;AACF,cAAM,UAAU,MAAM,SAAS,CAAC;AAChC,YAAI,CAAC,QAAQ,WAAW;AACtB,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AACA,eAAO,QAAQ;AAAA,MACjB,SAAS,KAAK;AACZ,eAAO,KAAK,aAAa,KAAK,CAAC;AAAA,MACjC;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,QAAQ,wBAAC,YAAY,SAAS;AAC5B,WAAO,KAAK,UAAU,SAAS,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,MAAM;AAAA,EACjE,GAFQ;AAAA,EAGR,UAAU,wBAAC,OAAO,aAAa,KAAK,iBAAiB;AACnD,QAAI,iBAAiB,SAAS;AAC5B,aAAO,KAAK,MAAM,cAAc,IAAI,QAAQ,OAAO,WAAW,IAAI,OAAO,KAAK,YAAY;AAAA,IAC5F;AACA,YAAQ,MAAM,SAAS;AACvB,WAAO,KAAK;AAAA,MACV,IAAI;AAAA,QACF,eAAe,KAAK,KAAK,IAAI,QAAQ,mBAAmB,UAAU,KAAK,KAAK,CAAC;AAAA,QAC7E;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAbU;AAAA,EAcV,OAAO,6BAAM;AACX,qBAAiB,SAAS,CAAC,UAAU;AACnC,YAAM,YAAY,KAAK,UAAU,MAAM,SAAS,OAAO,QAAQ,MAAM,QAAQ,MAAM,CAAC;AAAA,IACtF,CAAC;AAAA,EACH,GAJO;AAKT;;;AU1OA;;;ACAA;;;ACAA;AACA,IAAI,oBAAoB;AACxB,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,aAAa,OAAO;AACxB,IAAI,kBAAkB,IAAI,IAAI,aAAa;AAC3C,SAAS,WAAW,GAAG,GAAG;AACxB,MAAI,EAAE,WAAW,GAAG;AAClB,WAAO,EAAE,WAAW,IAAI,IAAI,IAAI,KAAK,IAAI;AAAA,EAC3C;AACA,MAAI,EAAE,WAAW,GAAG;AAClB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,6BAA6B,MAAM,2BAA2B;AACtE,WAAO;AAAA,EACT,WAAW,MAAM,6BAA6B,MAAM,2BAA2B;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,MAAM,mBAAmB;AAC3B,WAAO;AAAA,EACT,WAAW,MAAM,mBAAmB;AAClC,WAAO;AAAA,EACT;AACA,SAAO,EAAE,WAAW,EAAE,SAAS,IAAI,IAAI,KAAK,IAAI,EAAE,SAAS,EAAE;AAC/D;AAlBS;AAmBT,IAAI,OAAO,MAAM;AAAA,EAzBjB,OAyBiB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACA,YAA4B,uBAAO,OAAO,IAAI;AAAA,EAC9C,OAAO,QAAQ,OAAO,UAAU,SAAS,oBAAoB;AAC3D,QAAI,OAAO,WAAW,GAAG;AACvB,UAAI,KAAK,WAAW,QAAQ;AAC1B,cAAM;AAAA,MACR;AACA,UAAI,oBAAoB;AACtB;AAAA,MACF;AACA,WAAK,SAAS;AACd;AAAA,IACF;AACA,UAAM,CAAC,OAAO,GAAG,UAAU,IAAI;AAC/B,UAAM,UAAU,UAAU,MAAM,WAAW,WAAW,IAAI,CAAC,IAAI,IAAI,yBAAyB,IAAI,CAAC,IAAI,IAAI,iBAAiB,IAAI,UAAU,OAAO,CAAC,IAAI,IAAI,yBAAyB,IAAI,MAAM,MAAM,6BAA6B;AAC9N,QAAI;AACJ,QAAI,SAAS;AACX,YAAM,OAAO,QAAQ,CAAC;AACtB,UAAI,YAAY,QAAQ,CAAC,KAAK;AAC9B,UAAI,QAAQ,QAAQ,CAAC,GAAG;AACtB,oBAAY,UAAU,QAAQ,0BAA0B,KAAK;AAC7D,YAAI,YAAY,KAAK,SAAS,GAAG;AAC/B,gBAAM;AAAA,QACR;AAAA,MACF;AACA,aAAO,KAAK,UAAU,SAAS;AAC/B,UAAI,CAAC,MAAM;AACT,YAAI,OAAO,KAAK,KAAK,SAAS,EAAE;AAAA,UAC9B,CAAC,MAAM,MAAM,6BAA6B,MAAM;AAAA,QAClD,GAAG;AACD,gBAAM;AAAA,QACR;AACA,YAAI,oBAAoB;AACtB;AAAA,QACF;AACA,eAAO,KAAK,UAAU,SAAS,IAAI,IAAI,KAAK;AAC5C,YAAI,SAAS,IAAI;AACf,eAAK,YAAY,QAAQ;AAAA,QAC3B;AAAA,MACF;AACA,UAAI,CAAC,sBAAsB,SAAS,IAAI;AACtC,iBAAS,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC;AAAA,MACtC;AAAA,IACF,OAAO;AACL,aAAO,KAAK,UAAU,KAAK;AAC3B,UAAI,CAAC,MAAM;AACT,YAAI,OAAO,KAAK,KAAK,SAAS,EAAE;AAAA,UAC9B,CAAC,MAAM,EAAE,SAAS,KAAK,MAAM,6BAA6B,MAAM;AAAA,QAClE,GAAG;AACD,gBAAM;AAAA,QACR;AACA,YAAI,oBAAoB;AACtB;AAAA,QACF;AACA,eAAO,KAAK,UAAU,KAAK,IAAI,IAAI,KAAK;AAAA,MAC1C;AAAA,IACF;AACA,SAAK,OAAO,YAAY,OAAO,UAAU,SAAS,kBAAkB;AAAA,EACtE;AAAA,EACA,iBAAiB;AACf,UAAM,YAAY,OAAO,KAAK,KAAK,SAAS,EAAE,KAAK,UAAU;AAC7D,UAAM,UAAU,UAAU,IAAI,CAAC,MAAM;AACnC,YAAM,IAAI,KAAK,UAAU,CAAC;AAC1B,cAAQ,OAAO,EAAE,cAAc,WAAW,IAAI,CAAC,KAAK,EAAE,SAAS,KAAK,gBAAgB,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,EAAE,eAAe;AAAA,IAChI,CAAC;AACD,QAAI,OAAO,KAAK,WAAW,UAAU;AACnC,cAAQ,QAAQ,IAAI,KAAK,MAAM,EAAE;AAAA,IACnC;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,aAAO,QAAQ,CAAC;AAAA,IAClB;AACA,WAAO,QAAQ,QAAQ,KAAK,GAAG,IAAI;AAAA,EACrC;AACF;;;ACvGA;AAEA,IAAI,OAAO,MAAM;AAAA,EAFjB,OAEiB;AAAA;AAAA;AAAA,EACf,WAAW,EAAE,UAAU,EAAE;AAAA,EACzB,QAAQ,IAAI,KAAK;AAAA,EACjB,OAAO,MAAM,OAAO,oBAAoB;AACtC,UAAM,aAAa,CAAC;AACpB,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,OAAO;AAClB,UAAI,WAAW;AACf,aAAO,KAAK,QAAQ,cAAc,CAAC,MAAM;AACvC,cAAM,OAAO,MAAM,CAAC;AACpB,eAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AACpB;AACA,mBAAW;AACX,eAAO;AAAA,MACT,CAAC;AACD,UAAI,CAAC,UAAU;AACb;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,KAAK,MAAM,0BAA0B,KAAK,CAAC;AAC1D,aAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACvB,eAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAI,OAAO,CAAC,EAAE,QAAQ,IAAI,MAAM,IAAI;AAClC,iBAAO,CAAC,IAAI,OAAO,CAAC,EAAE,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC;AAChD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,MAAM,OAAO,QAAQ,OAAO,YAAY,KAAK,UAAU,kBAAkB;AAC9E,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,QAAI,SAAS,KAAK,MAAM,eAAe;AACvC,QAAI,WAAW,IAAI;AACjB,aAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAAA,IACtB;AACA,QAAI,eAAe;AACnB,UAAM,sBAAsB,CAAC;AAC7B,UAAM,sBAAsB,CAAC;AAC7B,aAAS,OAAO,QAAQ,yBAAyB,CAAC,GAAG,cAAc,eAAe;AAChF,UAAI,iBAAiB,QAAQ;AAC3B,4BAAoB,EAAE,YAAY,IAAI,OAAO,YAAY;AACzD,eAAO;AAAA,MACT;AACA,UAAI,eAAe,QAAQ;AACzB,4BAAoB,OAAO,UAAU,CAAC,IAAI,EAAE;AAC5C,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AACD,WAAO,CAAC,IAAI,OAAO,IAAI,MAAM,EAAE,GAAG,qBAAqB,mBAAmB;AAAA,EAC5E;AACF;;;AF9CA,IAAI,aAAa,CAAC;AAClB,IAAI,cAAc,CAAC,MAAM,CAAC,GAAmB,uBAAO,OAAO,IAAI,CAAC;AAChE,IAAI,sBAAsC,uBAAO,OAAO,IAAI;AAC5D,SAAS,oBAAoB,MAAM;AACjC,SAAO,oBAAoB,IAAI,MAAM,IAAI;AAAA,IACvC,SAAS,MAAM,KAAK,IAAI,KAAK;AAAA,MAC3B;AAAA,MACA,CAAC,GAAG,aAAa,WAAW,KAAK,QAAQ,KAAK;AAAA,IAChD,CAAC;AAAA,EACH;AACF;AAPS;AAQT,SAAS,2BAA2B;AAClC,wBAAsC,uBAAO,OAAO,IAAI;AAC1D;AAFS;AAGT,SAAS,mCAAmC,QAAQ;AAClD,QAAM,OAAO,IAAI,KAAK;AACtB,QAAM,cAAc,CAAC;AACrB,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO;AAAA,EACT;AACA,QAAM,2BAA2B,OAAO;AAAA,IACtC,CAAC,UAAU,CAAC,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,GAAG,GAAG,KAAK;AAAA,EAChD,EAAE;AAAA,IACA,CAAC,CAAC,WAAW,KAAK,GAAG,CAAC,WAAW,KAAK,MAAM,YAAY,IAAI,YAAY,KAAK,MAAM,SAAS,MAAM;AAAA,EACpG;AACA,QAAM,YAA4B,uBAAO,OAAO,IAAI;AACpD,WAAS,IAAI,GAAG,IAAI,IAAI,MAAM,yBAAyB,QAAQ,IAAI,KAAK,KAAK;AAC3E,UAAM,CAAC,oBAAoB,MAAM,QAAQ,IAAI,yBAAyB,CAAC;AACvE,QAAI,oBAAoB;AACtB,gBAAU,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAmB,uBAAO,OAAO,IAAI,CAAC,CAAC,GAAG,UAAU;AAAA,IAChG,OAAO;AACL;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AACF,mBAAa,KAAK,OAAO,MAAM,GAAG,kBAAkB;AAAA,IACtD,SAAS,GAAG;AACV,YAAM,MAAM,aAAa,IAAI,qBAAqB,IAAI,IAAI;AAAA,IAC5D;AACA,QAAI,oBAAoB;AACtB;AAAA,IACF;AACA,gBAAY,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC,GAAG,UAAU,MAAM;AACjD,YAAM,gBAAgC,uBAAO,OAAO,IAAI;AACxD,oBAAc;AACd,aAAO,cAAc,GAAG,cAAc;AACpC,cAAM,CAAC,KAAK,KAAK,IAAI,WAAW,UAAU;AAC1C,sBAAc,GAAG,IAAI;AAAA,MACvB;AACA,aAAO,CAAC,GAAG,aAAa;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,QAAM,CAAC,QAAQ,qBAAqB,mBAAmB,IAAI,KAAK,YAAY;AAC5E,WAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACtD,aAAS,IAAI,GAAG,OAAO,YAAY,CAAC,EAAE,QAAQ,IAAI,MAAM,KAAK;AAC3D,YAAM,MAAM,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC;AACjC,UAAI,CAAC,KAAK;AACR;AAAA,MACF;AACA,YAAM,OAAO,OAAO,KAAK,GAAG;AAC5B,eAAS,IAAI,GAAG,OAAO,KAAK,QAAQ,IAAI,MAAM,KAAK;AACjD,YAAI,KAAK,CAAC,CAAC,IAAI,oBAAoB,IAAI,KAAK,CAAC,CAAC,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,CAAC;AACpB,aAAW,KAAK,qBAAqB;AACnC,eAAW,CAAC,IAAI,YAAY,oBAAoB,CAAC,CAAC;AAAA,EACpD;AACA,SAAO,CAAC,QAAQ,YAAY,SAAS;AACvC;AAxDS;AAyDT,SAAS,eAAe,YAAY,MAAM;AACxC,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AACA,aAAW,KAAK,OAAO,KAAK,UAAU,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG;AAC3E,QAAI,oBAAoB,CAAC,EAAE,KAAK,IAAI,GAAG;AACrC,aAAO,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AAVS;AAWT,IAAI,eAAe,MAAM;AAAA,EA3FzB,OA2FyB;AAAA;AAAA;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,EAAE,CAAC,eAAe,GAAmB,uBAAO,OAAO,IAAI,EAAE;AAC5E,SAAK,UAAU,EAAE,CAAC,eAAe,GAAmB,uBAAO,OAAO,IAAI,EAAE;AAAA,EAC1E;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,UAAM,aAAa,KAAK;AACxB,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,cAAc,CAAC,QAAQ;AAC1B,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AACA,QAAI,CAAC,WAAW,MAAM,GAAG;AACvB;AACA,OAAC,YAAY,MAAM,EAAE,QAAQ,CAAC,eAAe;AAC3C,mBAAW,MAAM,IAAoB,uBAAO,OAAO,IAAI;AACvD,eAAO,KAAK,WAAW,eAAe,CAAC,EAAE,QAAQ,CAAC,MAAM;AACtD,qBAAW,MAAM,EAAE,CAAC,IAAI,CAAC,GAAG,WAAW,eAAe,EAAE,CAAC,CAAC;AAAA,QAC5D,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,SAAS,MAAM;AACjB,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK,MAAM,MAAM,KAAK,CAAC,GAAG;AAC9C,QAAI,MAAM,KAAK,IAAI,GAAG;AACpB,YAAM,KAAK,oBAAoB,IAAI;AACnC,UAAI,WAAW,iBAAiB;AAC9B,eAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,qBAAW,CAAC,EAAE,IAAI,MAAM,eAAe,WAAW,CAAC,GAAG,IAAI,KAAK,eAAe,WAAW,eAAe,GAAG,IAAI,KAAK,CAAC;AAAA,QACvH,CAAC;AAAA,MACH,OAAO;AACL,mBAAW,MAAM,EAAE,IAAI,MAAM,eAAe,WAAW,MAAM,GAAG,IAAI,KAAK,eAAe,WAAW,eAAe,GAAG,IAAI,KAAK,CAAC;AAAA,MACjI;AACA,aAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,KAAK,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM;AACxC,eAAG,KAAK,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,UAAU,CAAC;AAAA,UAC3D,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,MAAM;AACjC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,KAAK,OAAO,CAAC,CAAC,EAAE;AAAA,YACrB,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,UAAU,CAAC;AAAA,UAC9D;AAAA,QACF;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,UAAM,QAAQ,uBAAuB,IAAI,KAAK,CAAC,IAAI;AACnD,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,QAAQ,MAAM,CAAC;AACrB,aAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,MAAM;AACjC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,CAAC,EAAE,KAAK,MAAM;AAAA,YACnB,GAAG,eAAe,WAAW,CAAC,GAAG,KAAK,KAAK,eAAe,WAAW,eAAe,GAAG,KAAK,KAAK,CAAC;AAAA,UACpG;AACA,iBAAO,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,aAAa,MAAM,IAAI,CAAC,CAAC;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,6BAAyB;AACzB,UAAM,WAAW,KAAK,kBAAkB;AACxC,SAAK,QAAQ,CAAC,SAAS,UAAU;AAC/B,YAAM,UAAU,SAAS,OAAO,KAAK,SAAS,eAAe;AAC7D,YAAM,cAAc,QAAQ,CAAC,EAAE,KAAK;AACpC,UAAI,aAAa;AACf,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,MAAM,MAAM,QAAQ,CAAC,CAAC;AACpC,UAAI,CAAC,OAAO;AACV,eAAO,CAAC,CAAC,GAAG,UAAU;AAAA,MACxB;AACA,YAAM,QAAQ,MAAM,QAAQ,IAAI,CAAC;AACjC,aAAO,CAAC,QAAQ,CAAC,EAAE,KAAK,GAAG,KAAK;AAAA,IAClC;AACA,WAAO,KAAK,MAAM,QAAQ,IAAI;AAAA,EAChC;AAAA,EACA,oBAAoB;AAClB,UAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,WAAO,KAAK,KAAK,OAAO,EAAE,OAAO,OAAO,KAAK,KAAK,WAAW,CAAC,EAAE,QAAQ,CAAC,WAAW;AAClF,eAAS,MAAM,MAAM,KAAK,cAAc,MAAM;AAAA,IAChD,CAAC;AACD,SAAK,cAAc,KAAK,UAAU;AAClC,WAAO;AAAA,EACT;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,SAAS,CAAC;AAChB,QAAI,cAAc,WAAW;AAC7B,KAAC,KAAK,aAAa,KAAK,OAAO,EAAE,QAAQ,CAAC,MAAM;AAC9C,YAAM,WAAW,EAAE,MAAM,IAAI,OAAO,KAAK,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC;AAC9F,UAAI,SAAS,WAAW,GAAG;AACzB,wBAAgB;AAChB,eAAO,KAAK,GAAG,QAAQ;AAAA,MACzB,WAAW,WAAW,iBAAiB;AACrC,eAAO;AAAA,UACL,GAAG,OAAO,KAAK,EAAE,eAAe,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;AAAA,QACnF;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,mCAAmC,MAAM;AAAA,IAClD;AAAA,EACF;AACF;;;AG1MA;;;ACAA;AAEA,IAAI,cAAc,MAAM;AAAA,EAFxB,OAEwB;AAAA;AAAA;AAAA,EACtB,OAAO;AAAA,EACP,WAAW,CAAC;AAAA,EACZ,UAAU,CAAC;AAAA,EACX,YAAY,MAAM;AAChB,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,QAAI,CAAC,KAAK,SAAS;AACjB,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AACA,SAAK,QAAQ,KAAK,CAAC,QAAQ,MAAM,OAAO,CAAC;AAAA,EAC3C;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,KAAK,SAAS;AACjB,YAAM,IAAI,MAAM,aAAa;AAAA,IAC/B;AACA,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,KAAK;AACpB,UAAM,MAAM,QAAQ;AACpB,QAAI,IAAI;AACR,QAAI;AACJ,WAAO,IAAI,KAAK,KAAK;AACnB,YAAM,SAAS,QAAQ,CAAC;AACxB,UAAI;AACF,iBAAS,KAAK,GAAG,OAAO,OAAO,QAAQ,KAAK,MAAM,MAAM;AACtD,iBAAO,IAAI,GAAG,OAAO,EAAE,CAAC;AAAA,QAC1B;AACA,cAAM,OAAO,MAAM,QAAQ,IAAI;AAAA,MACjC,SAAS,GAAG;AACV,YAAI,aAAa,sBAAsB;AACrC;AAAA,QACF;AACA,cAAM;AAAA,MACR;AACA,WAAK,QAAQ,OAAO,MAAM,KAAK,MAAM;AACrC,WAAK,WAAW,CAAC,MAAM;AACvB,WAAK,UAAU;AACf;AAAA,IACF;AACA,QAAI,MAAM,KAAK;AACb,YAAM,IAAI,MAAM,aAAa;AAAA,IAC/B;AACA,SAAK,OAAO,iBAAiB,KAAK,aAAa,IAAI;AACnD,WAAO;AAAA,EACT;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,KAAK,WAAW,KAAK,SAAS,WAAW,GAAG;AAC9C,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC7D;AACA,WAAO,KAAK,SAAS,CAAC;AAAA,EACxB;AACF;;;ACtDA;;;ACAA;;;ACAA;AAGA,IAAI,cAA8B,uBAAO,OAAO,IAAI;AACpD,IAAIC,QAAO,MAAM;AAAA,EAJjB,OAIiB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY,QAAQ,SAAS,UAAU;AACrC,SAAK,YAAY,YAA4B,uBAAO,OAAO,IAAI;AAC/D,SAAK,WAAW,CAAC;AACjB,QAAI,UAAU,SAAS;AACrB,YAAM,IAAoB,uBAAO,OAAO,IAAI;AAC5C,QAAE,MAAM,IAAI,EAAE,SAAS,cAAc,CAAC,GAAG,OAAO,EAAE;AAClD,WAAK,WAAW,CAAC,CAAC;AAAA,IACpB;AACA,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA,EACA,OAAO,QAAQ,MAAM,SAAS;AAC5B,SAAK,SAAS,EAAE,KAAK;AACrB,QAAI,UAAU;AACd,UAAM,QAAQ,iBAAiB,IAAI;AACnC,UAAM,eAAe,CAAC;AACtB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,IAAI,MAAM,CAAC;AACjB,YAAM,QAAQ,MAAM,IAAI,CAAC;AACzB,YAAM,UAAU,WAAW,GAAG,KAAK;AACnC,YAAM,MAAM,MAAM,QAAQ,OAAO,IAAI,QAAQ,CAAC,IAAI;AAClD,UAAI,OAAO,QAAQ,WAAW;AAC5B,kBAAU,QAAQ,UAAU,GAAG;AAC/B,YAAI,SAAS;AACX,uBAAa,KAAK,QAAQ,CAAC,CAAC;AAAA,QAC9B;AACA;AAAA,MACF;AACA,cAAQ,UAAU,GAAG,IAAI,IAAIA,MAAK;AAClC,UAAI,SAAS;AACX,gBAAQ,UAAU,KAAK,OAAO;AAC9B,qBAAa,KAAK,QAAQ,CAAC,CAAC;AAAA,MAC9B;AACA,gBAAU,QAAQ,UAAU,GAAG;AAAA,IACjC;AACA,YAAQ,SAAS,KAAK;AAAA,MACpB,CAAC,MAAM,GAAG;AAAA,QACR;AAAA,QACA,cAAc,aAAa,OAAO,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;AAAA,QACjE,OAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,MAAM,QAAQ,YAAY,QAAQ;AAChD,UAAM,cAAc,CAAC;AACrB,aAAS,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK;AACxD,YAAM,IAAI,KAAK,SAAS,CAAC;AACzB,YAAM,aAAa,EAAE,MAAM,KAAK,EAAE,eAAe;AACjD,YAAM,eAAe,CAAC;AACtB,UAAI,eAAe,QAAQ;AACzB,mBAAW,SAAyB,uBAAO,OAAO,IAAI;AACtD,oBAAY,KAAK,UAAU;AAC3B,YAAI,eAAe,eAAe,UAAU,WAAW,aAAa;AAClE,mBAAS,KAAK,GAAG,OAAO,WAAW,aAAa,QAAQ,KAAK,MAAM,MAAM;AACvE,kBAAM,MAAM,WAAW,aAAa,EAAE;AACtC,kBAAM,YAAY,aAAa,WAAW,KAAK;AAC/C,uBAAW,OAAO,GAAG,IAAI,SAAS,GAAG,KAAK,CAAC,YAAY,OAAO,GAAG,IAAI,WAAW,GAAG,KAAK,SAAS,GAAG;AACpG,yBAAa,WAAW,KAAK,IAAI;AAAA,UACnC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,QAAQ,MAAM;AACnB,UAAM,cAAc,CAAC;AACrB,SAAK,UAAU;AACf,UAAM,UAAU;AAChB,QAAI,WAAW,CAAC,OAAO;AACvB,UAAM,QAAQ,UAAU,IAAI;AAC5B,UAAM,gBAAgB,CAAC;AACvB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,OAAO,MAAM,CAAC;AACpB,YAAM,SAAS,MAAM,MAAM;AAC3B,YAAM,YAAY,CAAC;AACnB,eAAS,IAAI,GAAG,OAAO,SAAS,QAAQ,IAAI,MAAM,KAAK;AACrD,cAAM,OAAO,SAAS,CAAC;AACvB,cAAM,WAAW,KAAK,UAAU,IAAI;AACpC,YAAI,UAAU;AACZ,mBAAS,UAAU,KAAK;AACxB,cAAI,QAAQ;AACV,gBAAI,SAAS,UAAU,GAAG,GAAG;AAC3B,0BAAY;AAAA,gBACV,GAAG,KAAK,gBAAgB,SAAS,UAAU,GAAG,GAAG,QAAQ,KAAK,OAAO;AAAA,cACvE;AAAA,YACF;AACA,wBAAY,KAAK,GAAG,KAAK,gBAAgB,UAAU,QAAQ,KAAK,OAAO,CAAC;AAAA,UAC1E,OAAO;AACL,sBAAU,KAAK,QAAQ;AAAA,UACzB;AAAA,QACF;AACA,iBAAS,IAAI,GAAG,OAAO,KAAK,UAAU,QAAQ,IAAI,MAAM,KAAK;AAC3D,gBAAM,UAAU,KAAK,UAAU,CAAC;AAChC,gBAAM,SAAS,KAAK,YAAY,cAAc,CAAC,IAAI,EAAE,GAAG,KAAK,QAAQ;AACrE,cAAI,YAAY,KAAK;AACnB,kBAAM,UAAU,KAAK,UAAU,GAAG;AAClC,gBAAI,SAAS;AACX,0BAAY,KAAK,GAAG,KAAK,gBAAgB,SAAS,QAAQ,KAAK,OAAO,CAAC;AACvE,sBAAQ,UAAU;AAClB,wBAAU,KAAK,OAAO;AAAA,YACxB;AACA;AAAA,UACF;AACA,cAAI,CAAC,MAAM;AACT;AAAA,UACF;AACA,gBAAM,CAAC,KAAK,MAAM,OAAO,IAAI;AAC7B,gBAAM,QAAQ,KAAK,UAAU,GAAG;AAChC,gBAAM,iBAAiB,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAC9C,cAAI,mBAAmB,QAAQ;AAC7B,kBAAM,IAAI,QAAQ,KAAK,cAAc;AACrC,gBAAI,GAAG;AACL,qBAAO,IAAI,IAAI,EAAE,CAAC;AAClB,0BAAY,KAAK,GAAG,KAAK,gBAAgB,OAAO,QAAQ,KAAK,SAAS,MAAM,CAAC;AAC7E,kBAAI,OAAO,KAAK,MAAM,SAAS,EAAE,QAAQ;AACvC,sBAAM,UAAU;AAChB,sBAAM,iBAAiB,EAAE,CAAC,EAAE,MAAM,IAAI,GAAG,UAAU;AACnD,sBAAM,iBAAiB,cAAc,cAAc,MAAM,CAAC;AAC1D,+BAAe,KAAK,KAAK;AAAA,cAC3B;AACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,YAAY,QAAQ,QAAQ,KAAK,IAAI,GAAG;AAC1C,mBAAO,IAAI,IAAI;AACf,gBAAI,QAAQ;AACV,0BAAY,KAAK,GAAG,KAAK,gBAAgB,OAAO,QAAQ,QAAQ,KAAK,OAAO,CAAC;AAC7E,kBAAI,MAAM,UAAU,GAAG,GAAG;AACxB,4BAAY;AAAA,kBACV,GAAG,KAAK,gBAAgB,MAAM,UAAU,GAAG,GAAG,QAAQ,QAAQ,KAAK,OAAO;AAAA,gBAC5E;AAAA,cACF;AAAA,YACF,OAAO;AACL,oBAAM,UAAU;AAChB,wBAAU,KAAK,KAAK;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,iBAAW,UAAU,OAAO,cAAc,MAAM,KAAK,CAAC,CAAC;AAAA,IACzD;AACA,QAAI,YAAY,SAAS,GAAG;AAC1B,kBAAY,KAAK,CAAC,GAAG,MAAM;AACzB,eAAO,EAAE,QAAQ,EAAE;AAAA,MACrB,CAAC;AAAA,IACH;AACA,WAAO,CAAC,YAAY,IAAI,CAAC,EAAE,SAAS,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC,CAAC;AAAA,EACrE;AACF;;;AD3JA,IAAI,aAAa,MAAM;AAAA,EAHvB,OAGuB;AAAA;AAAA;AAAA,EACrB,OAAO;AAAA,EACP;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ,IAAIC,MAAK;AAAA,EACxB;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,UAAM,UAAU,uBAAuB,IAAI;AAC3C,QAAI,SAAS;AACX,eAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,aAAK,MAAM,OAAO,QAAQ,QAAQ,CAAC,GAAG,OAAO;AAAA,MAC/C;AACA;AAAA,IACF;AACA,SAAK,MAAM,OAAO,QAAQ,MAAM,OAAO;AAAA,EACzC;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,WAAO,KAAK,MAAM,OAAO,QAAQ,IAAI;AAAA,EACvC;AACF;;;AlBjBA,IAAIC,QAAO,cAAc,KAAS;AAAA,EALlC,OAKkC;AAAA;AAAA;AAAA,EAChC,YAAY,UAAU,CAAC,GAAG;AACxB,UAAM,OAAO;AACb,SAAK,SAAS,QAAQ,UAAU,IAAI,YAAY;AAAA,MAC9C,SAAS,CAAC,IAAI,aAAa,GAAG,IAAI,WAAW,CAAC;AAAA,IAChD,CAAC;AAAA,EACH;AACF;;;AoBZA;;;ACAA;AACA,IAAI,OAAO,wBAAC,YAAY;AACtB,QAAM,WAAW;AAAA,IACf,QAAQ;AAAA,IACR,cAAc,CAAC,OAAO,QAAQ,OAAO,QAAQ,UAAU,OAAO;AAAA,IAC9D,cAAc,CAAC;AAAA,IACf,eAAe,CAAC;AAAA,EAClB;AACA,QAAM,OAAO;AAAA,IACX,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,mBAAmB,CAAC,eAAe;AACvC,QAAI,OAAO,eAAe,UAAU;AAClC,UAAI,eAAe,KAAK;AACtB,eAAO,MAAM;AAAA,MACf,OAAO;AACL,eAAO,CAAC,WAAW,eAAe,SAAS,SAAS;AAAA,MACtD;AAAA,IACF,WAAW,OAAO,eAAe,YAAY;AAC3C,aAAO;AAAA,IACT,OAAO;AACL,aAAO,CAAC,WAAW,WAAW,SAAS,MAAM,IAAI,SAAS;AAAA,IAC5D;AAAA,EACF,GAAG,KAAK,MAAM;AACd,QAAM,oBAAoB,CAAC,qBAAqB;AAC9C,QAAI,OAAO,qBAAqB,YAAY;AAC1C,aAAO;AAAA,IACT,WAAW,MAAM,QAAQ,gBAAgB,GAAG;AAC1C,aAAO,MAAM;AAAA,IACf,OAAO;AACL,aAAO,MAAM,CAAC;AAAA,IAChB;AAAA,EACF,GAAG,KAAK,YAAY;AACpB,SAAO,sCAAe,MAAM,GAAG,MAAM;AACnC,aAAS,IAAI,KAAK,OAAO;AACvB,QAAE,IAAI,QAAQ,IAAI,KAAK,KAAK;AAAA,IAC9B;AAFS;AAGT,UAAM,cAAc,gBAAgB,EAAE,IAAI,OAAO,QAAQ,KAAK,IAAI,CAAC;AACnE,QAAI,aAAa;AACf,UAAI,+BAA+B,WAAW;AAAA,IAChD;AACA,QAAI,KAAK,WAAW,KAAK;AACvB,YAAM,eAAe,EAAE,IAAI,OAAO,MAAM;AACxC,UAAI,cAAc;AAChB,YAAI,QAAQ,YAAY;AAAA,MAC1B,OAAO;AACL,YAAI,QAAQ,QAAQ;AAAA,MACtB;AAAA,IACF;AACA,QAAI,KAAK,aAAa;AACpB,UAAI,oCAAoC,MAAM;AAAA,IAChD;AACA,QAAI,KAAK,eAAe,QAAQ;AAC9B,UAAI,iCAAiC,KAAK,cAAc,KAAK,GAAG,CAAC;AAAA,IACnE;AACA,QAAI,EAAE,IAAI,WAAW,WAAW;AAC9B,UAAI,KAAK,UAAU,MAAM;AACvB,YAAI,0BAA0B,KAAK,OAAO,SAAS,CAAC;AAAA,MACtD;AACA,YAAM,eAAe,iBAAiB,EAAE,IAAI,OAAO,QAAQ,KAAK,IAAI,CAAC;AACrE,UAAI,aAAa,QAAQ;AACvB,YAAI,gCAAgC,aAAa,KAAK,GAAG,CAAC;AAAA,MAC5D;AACA,UAAI,UAAU,KAAK;AACnB,UAAI,CAAC,SAAS,QAAQ;AACpB,cAAM,iBAAiB,EAAE,IAAI,OAAO,gCAAgC;AACpE,YAAI,gBAAgB;AAClB,oBAAU,eAAe,MAAM,SAAS;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,SAAS,QAAQ;AACnB,YAAI,gCAAgC,QAAQ,KAAK,GAAG,CAAC;AACrD,UAAE,IAAI,QAAQ,OAAO,QAAQ,gCAAgC;AAAA,MAC/D;AACA,QAAE,IAAI,QAAQ,OAAO,gBAAgB;AACrC,QAAE,IAAI,QAAQ,OAAO,cAAc;AACnC,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,SAAS,EAAE,IAAI;AAAA,QACf,QAAQ;AAAA,QACR,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AACA,UAAM,KAAK;AAAA,EACb,GAlDO;AAmDT,GApFW;;;ACDX;AAGO,SAAS,QAAW,GAAY,SAAkB,MAAoB;AAC3E,QAAM,WAAW;AAAA,IACf,SAAS;AAAA,IACT,KAAK;AAAA,IACL,MAAM,QAAQ;AAAA,EAChB;AACA,SAAO,EAAE,KAAK,QAAQ;AACxB;AAPgB;AAST,SAAS,MAAM,GAAY,SAAiB,MAAe,SAAiB,KAAe;AAChG,QAAM,WAAW;AAAA,IACf,SAAS;AAAA,IACT,KAAK;AAAA,IACL,MAAM;AAAA,IACN;AAAA,EACF;AACA,SAAO,EAAE,KAAK,UAAU,MAAa;AACvC;AARgB;;;AFPT,SAAS,iBAAiB;AAC/B,SAAO,KAAK;AAAA,IACV,QAAQ;AAAA,IACR,cAAc,CAAC,OAAO,QAAQ,OAAO,UAAU,SAAS;AAAA,IACxD,cAAc,CAAC,gBAAgB,eAAe;AAAA,IAC9C,aAAa;AAAA,EACf,CAAC;AACH;AAPgB;AAST,SAAS,qBAAqB;AACnC,SAAO,OAAO,GAA8C,SAAe;AACzE,QAAI,CAAC,EAAE,IAAI,IAAI;AACb,aAAgB,MAAM,GAAG,wCAAU,wBAAwB,GAAG;AAAA,IAChE;AACA,QAAI,CAAC,EAAE,IAAI,OAAO;AAChB,aAAgB,MAAM,GAAG,kCAAS,qBAAqB,GAAG;AAAA,IAC5D;AACA,UAAM,KAAK;AAAA,EACb;AACF;AAVgB;;;AGdhB;;;ACAA;;;ACAA;;;ACAA;;;ACAA;;;ACAA;;;ACAA;;;ACAA;;;ACAA;;;ACAA;AACA,IAAI,kBAAkB,wBAAC,QAAQ;AAC7B,SAAO,aAAa,IAAI,QAAQ,QAAQ,CAAC,OAAO,EAAE,GAAG,KAAK,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;AAChF,GAFsB;AAGtB,IAAI,kBAAkB,wBAAC,QAAQ,aAAa,GAAG,EAAE,QAAQ,UAAU,CAAC,OAAO,EAAE,KAAK,KAAK,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,GAApF;AACtB,IAAI,eAAe,wBAAC,QAAQ;AAC1B,MAAI,SAAS;AACb,QAAM,QAAQ,IAAI,WAAW,GAAG;AAChC,WAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,cAAU,OAAO,aAAa,MAAM,CAAC,CAAC;AAAA,EACxC;AACA,SAAO,KAAK,MAAM;AACpB,GAPmB;AAQnB,IAAI,eAAe,wBAAC,QAAQ;AAC1B,QAAM,SAAS,KAAK,GAAG;AACvB,QAAM,QAAQ,IAAI,WAAW,IAAI,YAAY,OAAO,MAAM,CAAC;AAC3D,QAAM,OAAO,OAAO,SAAS;AAC7B,WAAS,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,KAAK,MAAM,KAAK,KAAK;AAC1D,UAAM,CAAC,IAAI,OAAO,WAAW,CAAC;AAC9B,UAAM,CAAC,IAAI,OAAO,WAAW,CAAC;AAAA,EAChC;AACA,SAAO;AACT,GATmB;;;ACbnB;AACA,IAAI,iBAAkC,kBAAC,oBAAoB;AACzD,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,SAAO;AACT,GAAG,kBAAkB,CAAC,CAAC;;;AChBvB;;;ACAA;AAkBA,IAAI,kBAAkB;AAAA,EACpB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,SAAS;AAAA,EACT,MAAM;AACR;AACA,IAAI,gBAAgB,6BAAM;AACxB,QAAM,SAAS;AACf,QAAM,qBAAqB,OAAO,cAAc,eAAe;AAC/D,MAAI,oBAAoB;AACtB,eAAW,CAAC,YAAY,SAAS,KAAK,OAAO,QAAQ,eAAe,GAAG;AACrE,UAAI,qBAAqB,SAAS,GAAG;AACnC,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,MAAI,OAAO,QAAQ,gBAAgB,UAAU;AAC3C,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,WAAW,QAAQ;AAC7B,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,SAAS,SAAS,SAAS,QAAQ;AAC7C,WAAO;AAAA,EACT;AACA,SAAO;AACT,GApBoB;AAqBpB,IAAI,uBAAuB,wBAAC,aAAa;AACvC,QAAM,YAAY;AAClB,SAAO,UAAU,WAAW,QAAQ;AACtC,GAH2B;;;AC7C3B;AACA,IAAI,6BAA6B,cAAc,MAAM;AAAA,EADrD,OACqD;AAAA;AAAA;AAAA,EACnD,YAAY,KAAK;AACf,UAAM,GAAG,GAAG,kCAAkC;AAC9C,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,kBAAkB,cAAc,MAAM;AAAA,EAP1C,OAO0C;AAAA;AAAA;AAAA,EACxC,YAAY,OAAO;AACjB,UAAM,sBAAsB,KAAK,EAAE;AACnC,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,oBAAoB,cAAc,MAAM;AAAA,EAb5C,OAa4C;AAAA;AAAA;AAAA,EAC1C,YAAY,OAAO;AACjB,UAAM,UAAU,KAAK,mCAAmC;AACxD,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,kBAAkB,cAAc,MAAM;AAAA,EAnB1C,OAmB0C;AAAA;AAAA;AAAA,EACxC,YAAY,OAAO;AACjB,UAAM,UAAU,KAAK,WAAW;AAChC,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,mBAAmB,cAAc,MAAM;AAAA,EAzB3C,OAyB2C;AAAA;AAAA;AAAA,EACzC,YAAY,kBAAkB,KAAK;AACjC;AAAA,MACE,2DAA2D,gBAAgB,YAAY,GAAG;AAAA,IAC5F;AACA,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,mBAAmB,cAAc,MAAM;AAAA,EAjC3C,OAiC2C;AAAA;AAAA;AAAA,EACzC,YAAY,QAAQ;AAClB,UAAM,0BAA0B,KAAK,UAAU,MAAM,CAAC,EAAE;AACxD,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,uBAAuB,cAAc,MAAM;AAAA,EAvC/C,OAuC+C;AAAA;AAAA;AAAA,EAC7C,YAAY,QAAQ;AAClB,UAAM,iCAAiC,KAAK,UAAU,MAAM,CAAC,EAAE;AAC/D,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,8BAA8B,cAAc,MAAM;AAAA,EA7CtD,OA6CsD;AAAA;AAAA;AAAA,EACpD,YAAY,OAAO;AACjB,UAAM,SAAS,KAAK,wBAAwB;AAC5C,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,iBAAkC,kBAAC,oBAAoB;AACzD,kBAAgB,SAAS,IAAI;AAC7B,kBAAgB,SAAS,IAAI;AAC7B,kBAAgB,MAAM,IAAI;AAC1B,kBAAgB,QAAQ,IAAI;AAC5B,kBAAgB,WAAW,IAAI;AAC/B,kBAAgB,YAAY,IAAI;AAChC,kBAAgB,SAAS,IAAI;AAC7B,kBAAgB,WAAW,IAAI;AAC/B,SAAO;AACT,GAAG,kBAAkB,CAAC,CAAC;;;AC7DvB;AACA,IAAI,cAAc,IAAI,YAAY;AAClC,IAAI,cAAc,IAAI,YAAY;;;AHGlC,eAAe,QAAQ,YAAY,KAAK,MAAM;AAC5C,QAAM,YAAY,gBAAgB,GAAG;AACrC,QAAM,YAAY,MAAM,iBAAiB,YAAY,SAAS;AAC9D,SAAO,MAAM,OAAO,OAAO,KAAK,WAAW,WAAW,IAAI;AAC5D;AAJe;AAKf,eAAe,UAAU,WAAW,KAAK,WAAW,MAAM;AACxD,QAAM,YAAY,gBAAgB,GAAG;AACrC,QAAM,YAAY,MAAM,gBAAgB,WAAW,SAAS;AAC5D,SAAO,MAAM,OAAO,OAAO,OAAO,WAAW,WAAW,WAAW,IAAI;AACzE;AAJe;AAKf,SAAS,YAAY,KAAK;AACxB,SAAO,aAAa,IAAI,QAAQ,oBAAoB,EAAE,EAAE,QAAQ,OAAO,EAAE,CAAC;AAC5E;AAFS;AAGT,eAAe,iBAAiB,KAAK,KAAK;AACxC,MAAI,CAAC,OAAO,UAAU,CAAC,OAAO,OAAO,WAAW;AAC9C,UAAM,IAAI,MAAM,0EAA0E;AAAA,EAC5F;AACA,MAAI,YAAY,GAAG,GAAG;AACpB,QAAI,IAAI,SAAS,aAAa,IAAI,SAAS,UAAU;AACnD,YAAM,IAAI;AAAA,QACR,0CAA0C,IAAI,IAAI;AAAA,MACpD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAC,eAAe,IAAI;AACnC,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO,MAAM,OAAO,OAAO,UAAU,OAAO,KAAK,KAAK,OAAO,MAAM;AAAA,EACrE;AACA,MAAI,IAAI,SAAS,SAAS,GAAG;AAC3B,WAAO,MAAM,OAAO,OAAO,UAAU,SAAS,YAAY,GAAG,GAAG,KAAK,OAAO,MAAM;AAAA,EACpF;AACA,SAAO,MAAM,OAAO,OAAO,UAAU,OAAO,YAAY,OAAO,GAAG,GAAG,KAAK,OAAO,MAAM;AACzF;AApBe;AAqBf,eAAe,gBAAgB,KAAK,KAAK;AACvC,MAAI,CAAC,OAAO,UAAU,CAAC,OAAO,OAAO,WAAW;AAC9C,UAAM,IAAI,MAAM,0EAA0E;AAAA,EAC5F;AACA,MAAI,YAAY,GAAG,GAAG;AACpB,QAAI,IAAI,SAAS,YAAY,IAAI,SAAS,UAAU;AAClD,aAAO;AAAA,IACT;AACA,UAAM,MAAM,oBAAoB,GAAG;AAAA,EACrC;AACA,MAAI,OAAO,QAAQ,YAAY,IAAI,SAAS,SAAS,GAAG;AACtD,UAAM,aAAa,MAAM,OAAO,OAAO,UAAU,SAAS,YAAY,GAAG,GAAG,KAAK,MAAM;AAAA,MACrF,eAAe;AAAA,IACjB,CAAC;AACD,UAAM,MAAM,oBAAoB,UAAU;AAAA,EAC5C;AACA,QAAM,SAAS,CAAC,eAAe,MAAM;AACrC,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO,MAAM,OAAO,OAAO,UAAU,OAAO,KAAK,KAAK,OAAO,MAAM;AAAA,EACrE;AACA,MAAI,IAAI,SAAS,QAAQ,GAAG;AAC1B,WAAO,MAAM,OAAO,OAAO,UAAU,QAAQ,YAAY,GAAG,GAAG,KAAK,OAAO,MAAM;AAAA,EACnF;AACA,SAAO,MAAM,OAAO,OAAO,UAAU,OAAO,YAAY,OAAO,GAAG,GAAG,KAAK,OAAO,MAAM;AACzF;AAxBe;AAyBf,eAAe,oBAAoB,YAAY;AAC7C,MAAI,WAAW,SAAS,WAAW;AACjC,UAAM,IAAI,MAAM,wBAAwB,WAAW,IAAI,EAAE;AAAA,EAC3D;AACA,MAAI,CAAC,WAAW,aAAa;AAC3B,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC3D;AACA,QAAM,MAAM,MAAM,OAAO,OAAO,UAAU,OAAO,UAAU;AAC3D,QAAM,EAAE,IAAI,IAAI;AAChB,QAAM,EAAE,KAAK,GAAG,EAAE,IAAI;AACtB,QAAM,EAAE,KAAK,GAAG,EAAE,IAAI;AACtB,SAAO,EAAE,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,SAAS,CAAC,eAAe,MAAM,EAAE;AACvE;AAZe;AAaf,SAAS,gBAAgB,MAAM;AAC7B,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,YAAY;AAAA,MACd;AAAA,IACF;AACE,YAAM,IAAI,2BAA2B,IAAI;AAAA,EAC7C;AACF;AApGS;AAqGT,SAAS,YAAY,KAAK;AACxB,QAAM,UAAU,cAAc;AAC9B,MAAI,YAAY,UAAU,CAAC,CAAC,OAAO,WAAW;AAC5C,WAAO,eAAe,OAAO,UAAU;AAAA,EACzC;AACA,SAAO,eAAe;AACxB;AANS;;;AHpKT,IAAI,gBAAgB,wBAAC,SAAS,gBAAgB,YAAY,OAAO,KAAK,UAAU,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,MAAM,EAAE,GAA3F;AACpB,IAAI,sBAAsB,wBAAC,QAAQ,gBAAgB,GAAG,EAAE,QAAQ,MAAM,EAAE,GAA9C;AAC1B,IAAI,gBAAgB,wBAAC,SAAS,KAAK,MAAM,YAAY,OAAO,gBAAgB,IAAI,CAAC,CAAC,GAA9D;AACpB,SAAS,cAAc,KAAK;AAC1B,MAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AAC3C,UAAM,aAAa;AACnB,WAAO,SAAS,cAAc,OAAO,OAAO,cAAc,EAAE,SAAS,WAAW,GAAG,MAAM,EAAE,SAAS,eAAe,WAAW,QAAQ;AAAA,EACxI;AACA,SAAO;AACT;AANS;AAOT,IAAI,OAAO,8BAAO,SAAS,YAAY,MAAM,YAAY;AACvD,QAAM,iBAAiB,cAAc,OAAO;AAC5C,MAAI;AACJ,MAAI,OAAO,eAAe,YAAY,SAAS,YAAY;AACzD,UAAM,WAAW;AACjB,oBAAgB,cAAc,EAAE,KAAK,KAAK,OAAO,KAAK,WAAW,IAAI,CAAC;AAAA,EACxE,OAAO;AACL,oBAAgB,cAAc,EAAE,KAAK,KAAK,MAAM,CAAC;AAAA,EACnD;AACA,QAAM,eAAe,GAAG,aAAa,IAAI,cAAc;AACvD,QAAM,gBAAgB,MAAM,QAAQ,YAAY,KAAK,YAAY,OAAO,YAAY,CAAC;AACrF,QAAM,YAAY,oBAAoB,aAAa;AACnD,SAAO,GAAG,YAAY,IAAI,SAAS;AACrC,GAbW;AAcX,IAAI,SAAS,8BAAO,OAAO,WAAW,MAAM,YAAY;AACtD,QAAM,aAAa,MAAM,MAAM,GAAG;AAClC,MAAI,WAAW,WAAW,GAAG;AAC3B,UAAM,IAAI,gBAAgB,KAAK;AAAA,EACjC;AACA,QAAM,EAAE,QAAQ,QAAQ,IAAI,OAAO,KAAK;AACxC,MAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,UAAM,IAAI,iBAAiB,MAAM;AAAA,EACnC;AACA,QAAM,MAAM,KAAK,IAAI,IAAI,MAAM;AAC/B,MAAI,QAAQ,OAAO,QAAQ,MAAM,KAAK;AACpC,UAAM,IAAI,kBAAkB,KAAK;AAAA,EACnC;AACA,MAAI,QAAQ,OAAO,QAAQ,OAAO,KAAK;AACrC,UAAM,IAAI,gBAAgB,KAAK;AAAA,EACjC;AACA,MAAI,QAAQ,OAAO,MAAM,QAAQ,KAAK;AACpC,UAAM,IAAI,iBAAiB,KAAK,QAAQ,GAAG;AAAA,EAC7C;AACA,QAAM,gBAAgB,MAAM,UAAU,GAAG,MAAM,YAAY,GAAG,CAAC;AAC/D,QAAM,WAAW,MAAM;AAAA,IACrB;AAAA,IACA;AAAA,IACA,gBAAgB,WAAW,CAAC,CAAC;AAAA,IAC7B,YAAY,OAAO,aAAa;AAAA,EAClC;AACA,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,4BAA4B,KAAK;AAAA,EAC7C;AACA,SAAO;AACT,GA9Ba;AA+Bb,IAAI,iBAAiB,8BAAO,OAAO,SAAS,SAAS;AACnD,QAAM,SAAS,aAAa,KAAK;AACjC,MAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,UAAM,IAAI,iBAAiB,MAAM;AAAA,EACnC;AACA,MAAI,CAAC,OAAO,KAAK;AACf,UAAM,IAAI,qBAAqB,MAAM;AAAA,EACvC;AACA,MAAI,QAAQ,UAAU;AACpB,UAAM,WAAW,MAAM,MAAM,QAAQ,UAAU,IAAI;AACnD,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI,MAAM,6BAA6B,QAAQ,QAAQ,EAAE;AAAA,IACjE;AACA,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,QAAI,CAAC,KAAK,MAAM;AACd,YAAM,IAAI,MAAM,gDAAgD;AAAA,IAClE;AACA,QAAI,CAAC,MAAM,QAAQ,KAAK,IAAI,GAAG;AAC7B,YAAM,IAAI,MAAM,qDAAqD;AAAA,IACvE;AACA,QAAI,QAAQ,MAAM;AAChB,cAAQ,KAAK,KAAK,GAAG,KAAK,IAAI;AAAA,IAChC,OAAO;AACL,cAAQ,OAAO,KAAK;AAAA,IACtB;AAAA,EACF,WAAW,CAAC,QAAQ,MAAM;AACxB,UAAM,IAAI,MAAM,yEAAyE;AAAA,EAC3F;AACA,QAAM,cAAc,QAAQ,KAAK,KAAK,CAAC,QAAQ,IAAI,QAAQ,OAAO,GAAG;AACrE,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,gBAAgB,KAAK;AAAA,EACjC;AACA,SAAO,MAAM,OAAO,OAAO,aAAa,YAAY,OAAO,OAAO,GAAG;AACvE,GAjCqB;AAkCrB,IAAI,SAAS,wBAAC,UAAU;AACtB,MAAI;AACF,UAAM,CAAC,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG;AAC9B,UAAM,SAAS,cAAc,CAAC;AAC9B,UAAM,UAAU,cAAc,CAAC;AAC/B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF,QAAQ;AACN,UAAM,IAAI,gBAAgB,KAAK;AAAA,EACjC;AACF,GAZa;AAab,IAAI,eAAe,wBAAC,UAAU;AAC5B,MAAI;AACF,UAAM,CAAC,CAAC,IAAI,MAAM,MAAM,GAAG;AAC3B,WAAO,cAAc,CAAC;AAAA,EACxB,QAAQ;AACN,UAAM,IAAI,gBAAgB,KAAK;AAAA,EACjC;AACF,GAPmB;;;ADlHnB,IAAI,MAAM,EAAE,MAAM,QAAQ,QAAQ,eAAe;;;AJ8FjD,IAAIC,UAAS,IAAI;AACjB,IAAIC,UAAS,IAAI;AACjB,IAAIC,QAAO,IAAI;;;AF/FR,IAAM,aAAN,MAAiB;AAAA,EAHxB,OAGwB;AAAA;AAAA;AAAA,EACd;AAAA,EAER,YAAY,QAAgB;AAC1B,SAAK,SAAS;AAAA,EAChB;AAAA,EAEA,MAAM,cAAc,SAA2D;AAC7E,UAAM,MAAM,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AACxC,UAAM,aAAa;AAAA,MACjB,GAAG;AAAA,MACH,KAAK;AAAA,MACL,KAAK,MAAM,KAAK,KAAK;AAAA;AAAA,IACvB;AACA,WAAO,MAAMC,MAAK,YAAY,KAAK,MAAM;AAAA,EAC3C;AAAA,EAEA,MAAM,qBAAqB,SAA2D;AACpF,UAAM,MAAM,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AACxC,UAAM,aAAa;AAAA,MACjB,GAAG;AAAA,MACH,KAAK;AAAA,MACL,KAAK,MAAM,IAAI,KAAK,KAAK;AAAA;AAAA,IAC3B;AACA,WAAO,MAAMA,MAAK,YAAY,KAAK,MAAM;AAAA,EAC3C;AAAA,EAEA,MAAM,YAAY,OAAoC;AACpD,WAAO,MAAMC,QAAO,OAAO,KAAK,MAAM;AAAA,EACxC;AACF;AAEO,SAAS,kBAAkB,SAAiB,IAAY;AAC7D,QAAM,QAAQ;AACd,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,EACjE;AACA,SAAO;AACT;AAPgB;;;AcnChB;AA+BA,oBAAuB;AAOvB,IAAI,iBAAiB;AAUrB,SAAS,YAAY,KAAK;AAExB,MAAI;AACF,WAAO,OAAO,gBAAgB,IAAI,WAAW,GAAG,CAAC;AAAA,EACnD,QAAQ;AAAA,EAAC;AAET,MAAI;AACF,WAAO,cAAAC,QAAW,YAAY,GAAG;AAAA,EACnC,QAAQ;AAAA,EAAC;AAET,MAAI,CAAC,gBAAgB;AACnB,UAAM;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACA,SAAO,eAAe,GAAG;AAC3B;AAhBS;AAsCF,SAAS,YAAY,QAAQ,aAAa;AAC/C,WAAS,UAAU;AACnB,MAAI,OAAO,WAAW;AACpB,UAAM;AAAA,MACJ,wBAAwB,OAAO,SAAS,OAAO,OAAO;AAAA,IACxD;AACF,MAAI,SAAS,EAAG,UAAS;AAAA,WAChB,SAAS,GAAI,UAAS;AAC/B,MAAI,OAAO,CAAC;AACZ,OAAK,KAAK,MAAM;AAChB,MAAI,SAAS,GAAI,MAAK,KAAK,GAAG;AAC9B,OAAK,KAAK,OAAO,SAAS,CAAC;AAC3B,OAAK,KAAK,GAAG;AACb,OAAK,KAAK,cAAc,YAAY,eAAe,GAAG,eAAe,CAAC;AACtE,SAAO,KAAK,KAAK,EAAE;AACrB;AAfgB;AAyBT,SAAS,QAAQ,QAAQ,aAAa,UAAU;AACrD,MAAI,OAAO,gBAAgB;AACzB,IAAC,WAAW,aAAe,cAAc;AAC3C,MAAI,OAAO,WAAW,WAAY,CAAC,WAAW,QAAU,SAAS;AACjE,MAAI,OAAO,WAAW,YAAa,UAAS;AAAA,WACnC,OAAO,WAAW;AACzB,UAAM,MAAM,wBAAwB,OAAO,MAAM;AAEnD,WAAS,OAAOC,WAAU;AACxB,aAAS,WAAY;AAEnB,UAAI;AACF,QAAAA,UAAS,MAAM,YAAY,MAAM,CAAC;AAAA,MACpC,SAAS,KAAK;AACZ,QAAAA,UAAS,GAAG;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AATS;AAWT,MAAI,UAAU;AACZ,QAAI,OAAO,aAAa;AACtB,YAAM,MAAM,uBAAuB,OAAO,QAAQ;AACpD,WAAO,QAAQ;AAAA,EACjB;AACE,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,aAAO,SAAU,KAAK,KAAK;AACzB,YAAI,KAAK;AACP,iBAAO,GAAG;AACV;AAAA,QACF;AACA,gBAAQ,GAAG;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AACL;AAjCgB;AA2DT,SAAS,KAAK,UAAU,MAAM,UAAU,kBAAkB;AAC/D,WAAS,OAAOC,WAAU;AACxB,QAAI,OAAO,aAAa,YAAY,OAAO,SAAS;AAClD,cAAQ,MAAM,SAAU,KAAKC,OAAM;AACjC,cAAM,UAAUA,OAAMD,WAAU,gBAAgB;AAAA,MAClD,CAAC;AAAA,aACM,OAAO,aAAa,YAAY,OAAO,SAAS;AACvD,YAAM,UAAU,MAAMA,WAAU,gBAAgB;AAAA;AAEhD;AAAA,QACEA,UAAS;AAAA,UACP;AAAA,UACA,MAAM,wBAAwB,OAAO,WAAW,OAAO,OAAO,IAAI;AAAA,QACpE;AAAA,MACF;AAAA,EACJ;AAdS;AAgBT,MAAI,UAAU;AACZ,QAAI,OAAO,aAAa;AACtB,YAAM,MAAM,uBAAuB,OAAO,QAAQ;AACpD,WAAO,QAAQ;AAAA,EACjB;AACE,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,aAAO,SAAU,KAAK,KAAK;AACzB,YAAI,KAAK;AACP,iBAAO,GAAG;AACV;AAAA,QACF;AACA,gBAAQ,GAAG;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AACL;AA/BgB;AAwChB,SAAS,kBAAkB,OAAO,SAAS;AACzC,MAAI,OAAO,MAAM,SAAS,QAAQ;AAClC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,YAAQ,MAAM,WAAW,CAAC,IAAI,QAAQ,WAAW,CAAC;AAAA,EACpD;AACA,SAAO,SAAS;AAClB;AANS;AAmCF,SAAS,QAAQ,UAAU,WAAW,UAAU,kBAAkB;AACvE,WAAS,OAAOE,WAAU;AACxB,QAAI,OAAO,aAAa,YAAY,OAAO,cAAc,UAAU;AACjE;AAAA,QACEA,UAAS;AAAA,UACP;AAAA,UACA;AAAA,YACE,wBAAwB,OAAO,WAAW,OAAO,OAAO;AAAA,UAC1D;AAAA,QACF;AAAA,MACF;AACA;AAAA,IACF;AACA,QAAI,UAAU,WAAW,IAAI;AAC3B,eAASA,UAAS,KAAK,MAAM,MAAM,KAAK,CAAC;AACzC;AAAA,IACF;AACA;AAAA,MACE;AAAA,MACA,UAAU,UAAU,GAAG,EAAE;AAAA,MACzB,SAAU,KAAK,MAAM;AACnB,YAAI,IAAK,CAAAA,UAAS,GAAG;AAAA,YAChB,CAAAA,UAAS,MAAM,kBAAkB,MAAM,SAAS,CAAC;AAAA,MACxD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAzBS;AA2BT,MAAI,UAAU;AACZ,QAAI,OAAO,aAAa;AACtB,YAAM,MAAM,uBAAuB,OAAO,QAAQ;AACpD,WAAO,QAAQ;AAAA,EACjB;AACE,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,aAAO,SAAU,KAAK,KAAK;AACzB,YAAI,KAAK;AACP,iBAAO,GAAG;AACV;AAAA,QACF;AACA,gBAAQ,GAAG;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AACL;AA1CgB;AAwFhB,IAAI,WACF,OAAO,YAAY,eACnB,WACA,OAAO,QAAQ,aAAa,aACxB,OAAO,iBAAiB,aACtB,eACA,QAAQ,WACV;AAGN,SAAS,WAAW,QAAQ;AAC1B,MAAI,MAAM,GACR,IAAI;AACN,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,QAAI,OAAO,WAAW,CAAC;AACvB,QAAI,IAAI,IAAK,QAAO;AAAA,aACX,IAAI,KAAM,QAAO;AAAA,cAEvB,IAAI,WAAY,UAChB,OAAO,WAAW,IAAI,CAAC,IAAI,WAAY,OACxC;AACA,QAAE;AACF,aAAO;AAAA,IACT,MAAO,QAAO;AAAA,EAChB;AACA,SAAO;AACT;AAhBS;AAmBT,SAAS,UAAU,QAAQ;AACzB,MAAI,SAAS,GACX,IACA;AACF,MAAI,SAAS,IAAI,MAAM,WAAW,MAAM,CAAC;AACzC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC7C,SAAK,OAAO,WAAW,CAAC;AACxB,QAAI,KAAK,KAAK;AACZ,aAAO,QAAQ,IAAI;AAAA,IACrB,WAAW,KAAK,MAAM;AACpB,aAAO,QAAQ,IAAK,MAAM,IAAK;AAC/B,aAAO,QAAQ,IAAK,KAAK,KAAM;AAAA,IACjC,YACG,KAAK,WAAY,WAChB,KAAK,OAAO,WAAW,IAAI,CAAC,KAAK,WAAY,OAC/C;AACA,WAAK,UAAY,KAAK,SAAW,OAAO,KAAK;AAC7C,QAAE;AACF,aAAO,QAAQ,IAAK,MAAM,KAAM;AAChC,aAAO,QAAQ,IAAM,MAAM,KAAM,KAAM;AACvC,aAAO,QAAQ,IAAM,MAAM,IAAK,KAAM;AACtC,aAAO,QAAQ,IAAK,KAAK,KAAM;AAAA,IACjC,OAAO;AACL,aAAO,QAAQ,IAAK,MAAM,KAAM;AAChC,aAAO,QAAQ,IAAM,MAAM,IAAK,KAAM;AACtC,aAAO,QAAQ,IAAK,KAAK,KAAM;AAAA,IACjC;AAAA,EACF;AACA,SAAO;AACT;AA7BS;AAuCT,IAAI,cACF,mEAAmE,MAAM,EAAE;AAO7E,IAAI,eAAe;AAAA,EACjB;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EACxE;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EACxE;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAG;AAAA,EAAG;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAC1E;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EACxE;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EACxE;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EACxE;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAC1C;AASA,SAAS,cAAc,GAAG,KAAK;AAC7B,MAAI,MAAM,GACR,KAAK,CAAC,GACN,IACA;AACF,MAAI,OAAO,KAAK,MAAM,EAAE,OAAQ,OAAM,MAAM,kBAAkB,GAAG;AACjE,SAAO,MAAM,KAAK;AAChB,SAAK,EAAE,KAAK,IAAI;AAChB,OAAG,KAAK,YAAa,MAAM,IAAK,EAAI,CAAC;AACrC,UAAM,KAAK,MAAS;AACpB,QAAI,OAAO,KAAK;AACd,SAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAC9B;AAAA,IACF;AACA,SAAK,EAAE,KAAK,IAAI;AAChB,UAAO,MAAM,IAAK;AAClB,OAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAC9B,UAAM,KAAK,OAAS;AACpB,QAAI,OAAO,KAAK;AACd,SAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAC9B;AAAA,IACF;AACA,SAAK,EAAE,KAAK,IAAI;AAChB,UAAO,MAAM,IAAK;AAClB,OAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAC9B,OAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAAA,EAChC;AACA,SAAO,GAAG,KAAK,EAAE;AACnB;AA5BS;AAqCT,SAAS,cAAc,GAAG,KAAK;AAC7B,MAAI,MAAM,GACR,OAAO,EAAE,QACT,OAAO,GACP,KAAK,CAAC,GACN,IACA,IACA,IACA,IACA,GACA;AACF,MAAI,OAAO,EAAG,OAAM,MAAM,kBAAkB,GAAG;AAC/C,SAAO,MAAM,OAAO,KAAK,OAAO,KAAK;AACnC,WAAO,EAAE,WAAW,KAAK;AACzB,SAAK,OAAO,aAAa,SAAS,aAAa,IAAI,IAAI;AACvD,WAAO,EAAE,WAAW,KAAK;AACzB,SAAK,OAAO,aAAa,SAAS,aAAa,IAAI,IAAI;AACvD,QAAI,MAAM,MAAM,MAAM,GAAI;AAC1B,QAAK,MAAM,MAAO;AAClB,UAAM,KAAK,OAAS;AACpB,OAAG,KAAK,OAAO,aAAa,CAAC,CAAC;AAC9B,QAAI,EAAE,QAAQ,OAAO,OAAO,KAAM;AAClC,WAAO,EAAE,WAAW,KAAK;AACzB,SAAK,OAAO,aAAa,SAAS,aAAa,IAAI,IAAI;AACvD,QAAI,MAAM,GAAI;AACd,SAAM,KAAK,OAAS,MAAO;AAC3B,UAAM,KAAK,OAAS;AACpB,OAAG,KAAK,OAAO,aAAa,CAAC,CAAC;AAC9B,QAAI,EAAE,QAAQ,OAAO,OAAO,KAAM;AAClC,WAAO,EAAE,WAAW,KAAK;AACzB,SAAK,OAAO,aAAa,SAAS,aAAa,IAAI,IAAI;AACvD,SAAM,KAAK,MAAS,MAAO;AAC3B,SAAK;AACL,OAAG,KAAK,OAAO,aAAa,CAAC,CAAC;AAC9B,MAAE;AAAA,EACJ;AACA,MAAI,MAAM,CAAC;AACX,OAAK,MAAM,GAAG,MAAM,MAAM,MAAO,KAAI,KAAK,GAAG,GAAG,EAAE,WAAW,CAAC,CAAC;AAC/D,SAAO;AACT;AAvCS;AA8CT,IAAI,kBAAkB;AAOtB,IAAI,8BAA8B;AAOlC,IAAI,sBAAsB;AAO1B,IAAI,qBAAqB;AAOzB,IAAI,SAAS;AAAA,EACX;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAC9D;AAOA,IAAI,SAAS;AAAA,EACX;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AACtC;AAOA,IAAI,SAAS;AAAA,EACX;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAC9D;AAUA,SAAS,UAAU,IAAI,KAAK,GAAG,GAAG;AAEhC,MAAI,GACF,IAAI,GAAG,GAAG,GACV,IAAI,GAAG,MAAM,CAAC;AAEhB,OAAK,EAAE,CAAC;AAoBR,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,CAAC;AACZ,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,CAAC;AAEZ,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,CAAC;AACZ,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,CAAC;AAEZ,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,CAAC;AACZ,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,CAAC;AAEZ,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,CAAC;AACZ,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,CAAC;AAEZ,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,CAAC;AACZ,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,EAAE;AAEb,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,EAAE;AACb,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,EAAE;AAEb,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,EAAE;AACb,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,EAAE;AAEb,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,EAAE;AACb,MAAI,EAAE,MAAM,EAAE;AACd,OAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,OAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,OAAK,EAAE,MAAS,IAAI,GAAK;AACzB,OAAK,IAAI,EAAE,EAAE;AAEb,KAAG,GAAG,IAAI,IAAI,EAAE,sBAAsB,CAAC;AACvC,KAAG,MAAM,CAAC,IAAI;AACd,SAAO;AACT;AArHS;AA6HT,SAAS,cAAc,MAAM,MAAM;AACjC,WAAS,IAAI,GAAG,OAAO,GAAG,IAAI,GAAG,EAAE;AACjC,IAAC,OAAQ,QAAQ,IAAM,KAAK,IAAI,IAAI,KACjC,QAAQ,OAAO,KAAK,KAAK;AAC9B,SAAO,EAAE,KAAK,MAAM,KAAW;AACjC;AALS;AAaT,SAAS,KAAK,KAAK,GAAG,GAAG;AACvB,MAAI,SAAS,GACX,KAAK,CAAC,GAAG,CAAC,GACV,OAAO,EAAE,QACT,OAAO,EAAE,QACT;AACF,WAAS,IAAI,GAAG,IAAI,MAAM;AACxB,IAAC,KAAK,cAAc,KAAK,MAAM,GAC5B,SAAS,GAAG,MACZ,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AACtB,OAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACzB,IAAC,KAAK,UAAU,IAAI,GAAG,GAAG,CAAC,GAAK,EAAE,CAAC,IAAI,GAAG,CAAC,GAAK,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;AACjE,OAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACzB,IAAC,KAAK,UAAU,IAAI,GAAG,GAAG,CAAC,GAAK,EAAE,CAAC,IAAI,GAAG,CAAC,GAAK,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;AACnE;AAdS;AAwBT,SAAS,QAAQ,MAAM,KAAK,GAAG,GAAG;AAChC,MAAI,OAAO,GACT,KAAK,CAAC,GAAG,CAAC,GACV,OAAO,EAAE,QACT,OAAO,EAAE,QACT;AACF,WAAS,IAAI,GAAG,IAAI,MAAM;AACxB,IAAC,KAAK,cAAc,KAAK,IAAI,GAAK,OAAO,GAAG,MAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AACvE,SAAO;AACP,OAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACzB,IAAC,KAAK,cAAc,MAAM,IAAI,GAC3B,OAAO,GAAG,MACV,GAAG,CAAC,KAAK,GAAG,KACZ,KAAK,cAAc,MAAM,IAAI,GAC7B,OAAO,GAAG,MACV,GAAG,CAAC,KAAK,GAAG,KACZ,KAAK,UAAU,IAAI,GAAG,GAAG,CAAC,GAC1B,EAAE,CAAC,IAAI,GAAG,CAAC,GACX,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;AACpB,OAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACzB,IAAC,KAAK,cAAc,MAAM,IAAI,GAC3B,OAAO,GAAG,MACV,GAAG,CAAC,KAAK,GAAG,KACZ,KAAK,cAAc,MAAM,IAAI,GAC7B,OAAO,GAAG,MACV,GAAG,CAAC,KAAK,GAAG,KACZ,KAAK,UAAU,IAAI,GAAG,GAAG,CAAC,GAC1B,EAAE,CAAC,IAAI,GAAG,CAAC,GACX,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;AACtB;AA7BS;AA0CT,SAAS,OAAO,GAAG,MAAM,QAAQ,UAAU,kBAAkB;AAC3D,MAAI,QAAQ,OAAO,MAAM,GACvB,OAAO,MAAM,QACb;AAGF,MAAI,SAAS,KAAK,SAAS,IAAI;AAC7B,UAAM,MAAM,sCAAsC,MAAM;AACxD,QAAI,UAAU;AACZ,eAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,IACF,MAAO,OAAM;AAAA,EACf;AACA,MAAI,KAAK,WAAW,iBAAiB;AACnC,UAAM;AAAA,MACJ,0BAA0B,KAAK,SAAS,SAAS;AAAA,IACnD;AACA,QAAI,UAAU;AACZ,eAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,IACF,MAAO,OAAM;AAAA,EACf;AACA,WAAU,KAAK,WAAY;AAE3B,MAAI,GACF,GACA,IAAI,GACJ;AAGF,MAAI,OAAO,eAAe,YAAY;AACpC,QAAI,IAAI,WAAW,MAAM;AACzB,QAAI,IAAI,WAAW,MAAM;AAAA,EAC3B,OAAO;AACL,QAAI,OAAO,MAAM;AACjB,QAAI,OAAO,MAAM;AAAA,EACnB;AAEA,UAAQ,MAAM,GAAG,GAAG,CAAC;AAOrB,WAAS,OAAO;AACd,QAAI,iBAAkB,kBAAiB,IAAI,MAAM;AACjD,QAAI,IAAI,QAAQ;AACd,UAAI,QAAQ,KAAK,IAAI;AACrB,aAAO,IAAI,UAAU;AACnB,YAAI,IAAI;AACR,aAAK,GAAG,GAAG,CAAC;AACZ,aAAK,MAAM,GAAG,CAAC;AACf,YAAI,KAAK,IAAI,IAAI,QAAQ,mBAAoB;AAAA,MAC/C;AAAA,IACF,OAAO;AACL,WAAK,IAAI,GAAG,IAAI,IAAI;AAClB,aAAK,IAAI,GAAG,IAAI,QAAQ,GAAG,IAAK,WAAU,OAAO,KAAK,GAAG,GAAG,CAAC;AAC/D,UAAI,MAAM,CAAC;AACX,WAAK,IAAI,GAAG,IAAI,MAAM;AACpB,YAAI,MAAO,MAAM,CAAC,KAAK,KAAM,SAAU,CAAC,GACtC,IAAI,MAAO,MAAM,CAAC,KAAK,KAAM,SAAU,CAAC,GACxC,IAAI,MAAO,MAAM,CAAC,KAAK,IAAK,SAAU,CAAC,GACvC,IAAI,MAAM,MAAM,CAAC,IAAI,SAAU,CAAC;AACpC,UAAI,UAAU;AACZ,iBAAS,MAAM,GAAG;AAClB;AAAA,MACF,MAAO,QAAO;AAAA,IAChB;AACA,QAAI,SAAU,UAAS,IAAI;AAAA,EAC7B;AAzBS;AA4BT,MAAI,OAAO,aAAa,aAAa;AACnC,SAAK;AAAA,EAGP,OAAO;AACL,QAAI;AACJ,WAAO,KAAM,KAAI,QAAQ,MAAM,KAAK,OAAO,YAAa,QAAO,OAAO,CAAC;AAAA,EACzE;AACF;AAjFS;AA6FT,SAAS,MAAM,UAAU,MAAM,UAAU,kBAAkB;AACzD,MAAI;AACJ,MAAI,OAAO,aAAa,YAAY,OAAO,SAAS,UAAU;AAC5D,UAAM,MAAM,qCAAqC;AACjD,QAAI,UAAU;AACZ,eAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,IACF,MAAO,OAAM;AAAA,EACf;AAGA,MAAI,OAAO;AACX,MAAI,KAAK,OAAO,CAAC,MAAM,OAAO,KAAK,OAAO,CAAC,MAAM,KAAK;AACpD,UAAM,MAAM,2BAA2B,KAAK,UAAU,GAAG,CAAC,CAAC;AAC3D,QAAI,UAAU;AACZ,eAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,IACF,MAAO,OAAM;AAAA,EACf;AACA,MAAI,KAAK,OAAO,CAAC,MAAM,IAAK,CAAC,QAAQ,OAAO,aAAa,CAAC,GAAK,SAAS;AAAA,OACnE;AACH,YAAQ,KAAK,OAAO,CAAC;AACrB,QACG,UAAU,OAAO,UAAU,OAAO,UAAU,OAC7C,KAAK,OAAO,CAAC,MAAM,KACnB;AACA,YAAM,MAAM,4BAA4B,KAAK,UAAU,GAAG,CAAC,CAAC;AAC5D,UAAI,UAAU;AACZ,iBAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,MACF,MAAO,OAAM;AAAA,IACf;AACA,aAAS;AAAA,EACX;AAGA,MAAI,KAAK,OAAO,SAAS,CAAC,IAAI,KAAK;AACjC,UAAM,MAAM,qBAAqB;AACjC,QAAI,UAAU;AACZ,eAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,IACF,MAAO,OAAM;AAAA,EACf;AACA,MAAI,KAAK,SAAS,KAAK,UAAU,QAAQ,SAAS,CAAC,GAAG,EAAE,IAAI,IAC1D,KAAK,SAAS,KAAK,UAAU,SAAS,GAAG,SAAS,CAAC,GAAG,EAAE,GACxD,SAAS,KAAK,IACd,YAAY,KAAK,UAAU,SAAS,GAAG,SAAS,EAAE;AACpD,cAAY,SAAS,MAAM,OAAS;AAEpC,MAAI,YAAY,UAAU,QAAQ,GAChC,QAAQ,cAAc,WAAW,eAAe;AAQlD,WAAS,OAAO,OAAO;AACrB,QAAI,MAAM,CAAC;AACX,QAAI,KAAK,IAAI;AACb,QAAI,SAAS,IAAK,KAAI,KAAK,KAAK;AAChC,QAAI,KAAK,GAAG;AACZ,QAAI,SAAS,GAAI,KAAI,KAAK,GAAG;AAC7B,QAAI,KAAK,OAAO,SAAS,CAAC;AAC1B,QAAI,KAAK,GAAG;AACZ,QAAI,KAAK,cAAc,OAAO,MAAM,MAAM,CAAC;AAC3C,QAAI,KAAK,cAAc,OAAO,OAAO,SAAS,IAAI,CAAC,CAAC;AACpD,WAAO,IAAI,KAAK,EAAE;AAAA,EACpB;AAXS;AAcT,MAAI,OAAO,YAAY;AACrB,WAAO,OAAO,OAAO,WAAW,OAAO,MAAM,CAAC;AAAA,OAE3C;AACH;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAUC,MAAK,OAAO;AACpB,YAAIA,KAAK,UAASA,MAAK,IAAI;AAAA,YACtB,UAAS,MAAM,OAAO,KAAK,CAAC;AAAA,MACnC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAvFS;;;AftgCF,SAAS,gBAAgBC,MAA6C;AAE3E,EAAAA,KAAI,KAAK,mBAAmB,OAAO,MAAM;AACvC,QAAI;AACF,YAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,YAAM,EAAE,UAAU,SAAS,IAAI;AAE/B,UAAI,CAAC,YAAY,CAAC,UAAU;AAC1B,eAAgB,MAAM,GAAG,gEAAc,uBAAuB,GAAG;AAAA,MACnE;AAGA,YAAM,QAAQ,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA,OAIpC,EAAE,KAAK,QAAQ,EAAE,MAAM;AAExB,UAAI,CAAC,OAAO;AACV,eAAgB,MAAM,GAAG,oDAAY,uBAAuB,GAAG;AAAA,MACjE;AAGA,YAAM,kBAAkB,MAAa,QAAQ,UAAU,MAAM,aAAuB;AAEpF,UAAI,CAAC,iBAAiB;AACpB,eAAgB,MAAM,GAAG,oDAAY,uBAAuB,GAAG;AAAA,MACjE;AAEA,YAAM,aAAa,IAAI,WAAW,EAAE,IAAI,UAAU;AAGlD,YAAM,eAAe;AAAA,QACnB,UAAU,MAAM;AAAA,QAChB,UAAU,MAAM;AAAA,QAChB,MAAM,MAAM;AAAA,MACd;AAEA,YAAM,QAAQ,MAAM,WAAW,cAAc,YAAY;AACzD,YAAM,eAAe,MAAM,WAAW,qBAAqB,YAAY;AAGvE,UAAI,aAAuB,CAAC;AAC5B,UAAI,MAAM,aAAa;AACrB,YAAI;AACF,uBAAa,KAAK,MAAM,MAAM,WAAqB;AAAA,QACrD,QAAQ;AACN,uBAAc,MAAM,YAAuB,MAAM,GAAG,EAAE,IAAI,CAAC,OAAe,GAAG,KAAK,CAAC;AAAA,QACrF;AAAA,MACF;AAGA,YAAM,WAAW,cAAc,MAAM,EAAE;AACvC,YAAM,EAAE,IAAI,MAAM,IAAI,UAAU,KAAK,UAAU;AAAA,QAC7C,IAAI,MAAM;AAAA,QACV,UAAU,MAAM;AAAA,QAChB,MAAM,MAAM;AAAA,QACZ,aAAa;AAAA,MACf,CAAC,GAAG,EAAE,eAAe,KAAK,CAAC;AAE3B,aAAgB,QAAQ,GAAG,4BAAQ;AAAA,QACjC;AAAA,QACA,UAAU,MAAM;AAAA,QAChB,UAAU,MAAM;AAAA,QAChB,MAAM,MAAM;AAAA,QACZ,qBAAqB;AAAA,QACrB,YAAY;AAAA,MACd,CAAC;AAAA,IAEH,SAASC,QAAO;AACd,cAAQ,MAAM,sBAAsBA,MAAK;AACzC,aAAgB,MAAM,GAAG,8CAAW,eAAe,GAAG;AAAA,IACxD;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,KAAK,qBAAqB,OAAO,MAAM;AACzC,QAAI;AACF,YAAM,aAAa,EAAE,IAAI,OAAO,eAAe;AAE/C,UAAI,CAAC,cAAc,CAAC,WAAW,WAAW,SAAS,GAAG;AACpD,eAAgB,MAAM,GAAG,mDAAgB,iBAAiB,GAAG;AAAA,MAC/D;AAEA,YAAM,gBAAgB,WAAW,UAAU,CAAC;AAE5C,YAAM,aAAa,IAAI,WAAW,EAAE,IAAI,UAAU;AAGlD,UAAI;AACJ,UAAI;AACF,kBAAU,MAAM,WAAW,YAAY,aAAa;AAAA,MACtD,QAAQ;AACN,eAAgB,MAAM,GAAG,yDAAiB,iBAAiB,GAAG;AAAA,MAChE;AAGA,YAAM,QAAQ,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA,OAIpC,EAAE,KAAK,QAAQ,QAAQ,EAAE,MAAM;AAEhC,UAAI,CAAC,OAAO;AACV,eAAgB,MAAM,GAAG,4EAAgB,mBAAmB,GAAG;AAAA,MACjE;AAGA,YAAM,eAAe;AAAA,QACnB,UAAU,MAAM;AAAA,QAChB,UAAU,MAAM;AAAA,QAChB,MAAM,MAAM;AAAA,MACd;AAEA,YAAM,QAAQ,MAAM,WAAW,cAAc,YAAY;AACzD,YAAM,kBAAkB,MAAM,WAAW,qBAAqB,YAAY;AAG1E,UAAI,aAAuB,CAAC;AAC5B,UAAI,MAAM,aAAa;AACrB,YAAI;AACF,uBAAa,KAAK,MAAM,MAAM,WAAqB;AAAA,QACrD,QAAQ;AACN,uBAAc,MAAM,YAAuB,MAAM,GAAG,EAAE,IAAI,CAAC,OAAe,GAAG,KAAK,CAAC;AAAA,QACrF;AAAA,MACF;AAGA,YAAM,WAAW,cAAc,MAAM,EAAE;AACvC,YAAM,EAAE,IAAI,MAAM,IAAI,UAAU,KAAK,UAAU;AAAA,QAC7C,IAAI,MAAM;AAAA,QACV,UAAU,MAAM;AAAA,QAChB,MAAM,MAAM;AAAA,QACZ,aAAa;AAAA,MACf,CAAC,GAAG,EAAE,eAAe,KAAK,CAAC;AAE3B,aAAgB,QAAQ,GAAG,iCAAa;AAAA,QACtC;AAAA,QACA,YAAY;AAAA,MACd,CAAC;AAAA,IAEH,SAASC,QAAO;AACd,cAAQ,MAAM,wBAAwBA,MAAK;AAC3C,aAAgB,MAAM,GAAG,8CAAW,iBAAiB,GAAG;AAAA,IAC1D;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,KAAK,oBAAoB,OAAO,MAAM;AACxC,QAAI;AACF,YAAM,aAAa,EAAE,IAAI,OAAO,eAAe;AAC/C,UAAI,CAAC,cAAc,CAAC,WAAW,WAAW,SAAS,GAAG;AACpD,eAAgB,MAAM,GAAG,kCAAS,iBAAiB,GAAG;AAAA,MACxD;AAEA,YAAM,QAAQ,WAAW,UAAU,CAAC;AACpC,YAAM,aAAa,IAAI,WAAW,EAAE,IAAI,UAAU;AAElD,UAAI;AACF,cAAM,UAAU,MAAM,WAAW,YAAY,KAAK;AAGlD,cAAM,WAAW,cAAc,QAAQ,QAAQ;AAC/C,cAAM,EAAE,IAAI,MAAM,OAAO,QAAQ;AAAA,MAEnC,QAAQ;AAAA,MAER;AAEA,aAAgB,QAAQ,GAAG,0BAAM;AAAA,IAEnC,SAASC,QAAO;AACd,cAAQ,MAAM,iBAAiBA,MAAK;AACpC,aAAgB,MAAM,GAAG,8CAAW,gBAAgB,GAAG;AAAA,IACzD;AAAA,EACF,CAAC;AACH;AAhLgB;;;AgBNhB;;;ACAA;AAWO,SAAS,iBAAiB;AAC/B,SAAO,OAAO,GAA8C,SAAe;AACzE,UAAM,aAAa,EAAE,IAAI,OAAO,eAAe;AAE/C,QAAI,CAAC,cAAc,CAAC,WAAW,WAAW,SAAS,GAAG;AACpD,aAAgB,MAAM,GAAG,iFAAqB,iBAAiB,GAAG;AAAA,IACpE;AAEA,UAAM,QAAQ,WAAW,UAAU,CAAC;AACpC,UAAM,aAAa,IAAI,WAAW,EAAE,IAAI,UAAU;AAElD,QAAI;AACF,YAAM,UAAU,MAAM,WAAW,YAAY,KAAK;AAGlD,UAAI,QAAQ,MAAM,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI,GAAG;AAC/C,eAAgB,MAAM,GAAG,+DAAkB,iBAAiB,GAAG;AAAA,MACjE;AAEA,QAAE,IAAI,SAAS,OAAO;AACtB,YAAM,KAAK;AAAA,IACb,SAAS,KAAK;AACZ,aAAgB,MAAM,GAAG,+DAAkB,iBAAiB,GAAG;AAAA,IACjE;AAAA,EACF;AACF;AAzBgB;AA2BT,SAAS,iBAAiB;AAC/B,SAAO,OAAO,GAA8C,SAAe;AACzE,UAAM,QAAQ,EAAE,IAAI,OAAO;AAE3B,QAAI,CAAC,SAAS,MAAM,SAAS,SAAS;AACpC,aAAgB,MAAM,GAAG,wFAAkB,4BAA4B,GAAG;AAAA,IAC5E;AAEA,UAAM,KAAK;AAAA,EACb;AACF;AAVgB;;;ADhCT,SAAS,iBAAiBC,MAA6C;AAE5E,EAAAA,KAAI,IAAI,eAAe,eAAe,GAAG,eAAe,CAAC;AACzD,EAAAA,KAAI,IAAI,iBAAiB,eAAe,GAAG,eAAe,CAAC;AAG3D,EAAAA,KAAI,IAAI,eAAe,OAAO,MAAM;AAClC,QAAI;AACF,YAAM,QAAQ,EAAE,IAAI,MAAM;AAC1B,YAAM,OAAO,SAAS,MAAM,QAAQ,GAAG;AACvC,YAAM,QAAQ,SAAS,MAAM,SAAS,IAAI;AAC1C,YAAM,OAAO,MAAM;AACnB,YAAM,SAAS,MAAM;AACrB,YAAM,SAAS,MAAM;AACrB,YAAM,UAAU,OAAO,KAAK;AAE5B,UAAI,WAAW;AACf,YAAM,SAAgB,CAAC;AACvB,YAAM,aAAuB,CAAC;AAE9B,UAAI,MAAM;AACR,mBAAW,KAAK,UAAU;AAC1B,eAAO,KAAK,IAAI;AAAA,MAClB;AAEA,UAAI,QAAQ;AACV,mBAAW,KAAK,YAAY;AAC5B,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,UAAI,QAAQ;AACV,mBAAW,KAAK,iBAAiB;AACjC,eAAO,KAAK,IAAI,MAAM,GAAG;AAAA,MAC3B;AAEA,UAAI,WAAW,SAAS,GAAG;AACzB,oBAAY,YAAY,WAAW,KAAK,OAAO;AAAA,MACjD;AAEA,kBAAY;AACZ,aAAO,KAAK,OAAO,MAAM;AAEzB,YAAM,SAAS,MAAM,EAAE,IAAI,GAAG,QAAQ,QAAQ,EAAE,KAAK,GAAG,MAAM,EAAE,IAAI;AAGpE,UAAI,aAAa;AACjB,UAAI,WAAW,SAAS,GAAG;AACzB,sBAAc,YAAY,WAAW,KAAK,OAAO;AAAA,MACnD;AACA,YAAM,cAAc,MAAM,EAAE,IAAI,GAAG,QAAQ,UAAU,EAAE,KAAK,GAAG,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM;AAE1F,aAAgB,QAAQ,GAAG,0DAAa;AAAA,QACtC,QAAQ,OAAO;AAAA,QACf,OAAQ,aAAa,SAAoB;AAAA,QACzC;AAAA,QACA;AAAA,QACA,YAAY,KAAK,MAAO,aAAa,SAAoB,KAAK,KAAK;AAAA,MACrE,CAAC;AAAA,IACH,SAASC,QAAO;AACd,cAAQ,MAAM,qBAAqBA,MAAK;AACxC,aAAgB,MAAM,GAAG,8CAAW,oBAAoB,GAAG;AAAA,IAC7D;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,KAAK,eAAe,OAAO,MAAM;AACnC,QAAI;AACF,YAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,YAAM,EAAE,UAAU,UAAU,MAAM,YAAY,IAAI;AAElD,UAAI,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM;AACnC,eAAgB,MAAM,GAAG,kFAAiB,2BAA2B,GAAG;AAAA,MAC1E;AAGA,YAAM,WAAW,MAAM,EAAE,IAAI,GAAG,QAAQ,0CAA0C,EAAE,KAAK,QAAQ,EAAE,MAAM;AACzG,UAAI,UAAU;AACZ,eAAgB,MAAM,GAAG,wCAAU,sBAAsB,GAAG;AAAA,MAC9D;AAGA,UAAI,eAAe,YAAY,SAAS,GAAG;AACzC,cAAM,eAAe,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA,gEACY,YAAY,IAAI,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,SAC3F,EAAE,KAAK,GAAG,WAAW,EAAE,MAAM;AAE9B,YAAK,cAAc,UAAqB,YAAY,QAAQ;AAC1D,iBAAgB,MAAM,GAAG,4DAAe,uBAAuB,GAAG;AAAA,QACpE;AAAA,MACF;AAGA,YAAM,eAAe,MAAa,KAAK,UAAU,EAAE;AACnD,YAAM,iBAAiB,cAAc,KAAK,UAAU,WAAW,IAAI;AAEnE,YAAM,SAAS,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA,OAGrC,EAAE,KAAK,UAAU,cAAc,MAAM,cAAc,EAAE,IAAI;AAE1D,aAAgB,QAAQ,GAAG,8CAAW,EAAE,IAAI,OAAO,KAAK,YAAY,CAAC;AAAA,IACvE,SAASC,QAAO;AACd,cAAQ,MAAM,uBAAuBA,MAAK;AAC1C,aAAgB,MAAM,GAAG,8CAAW,sBAAsB,GAAG;AAAA,IAC/D;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,IAAI,mBAAmB,OAAO,MAAM;AACtC,QAAI;AACF,YAAM,KAAK,SAAS,EAAE,IAAI,MAAM,IAAI,CAAC;AAErC,UAAI,CAAC,MAAM,MAAM,GAAG;AAClB,eAAgB,MAAM,GAAG,0CAAY,oBAAoB,GAAG;AAAA,MAC9D;AAEA,YAAM,QAAQ,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA,OAIpC,EAAE,KAAK,EAAE,EAAE,MAAM;AAElB,UAAI,CAAC,OAAO;AACV,eAAgB,MAAM,GAAG,wCAAU,mBAAmB,GAAG;AAAA,MAC3D;AAGA,UAAI,mBAAmB,CAAC;AACxB,UAAI,MAAM,aAAa;AACrB,YAAI;AACF,gBAAM,aAAa,KAAK,MAAM,MAAM,WAAqB;AACzD,gBAAM,WAAW,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA,yDACO,WAAW,IAAI,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,WACjF,EAAE,KAAK,GAAG,UAAU,EAAE,IAAI;AAC3B,6BAAmB,SAAS;AAAA,QAC9B,SAAS,GAAG;AAEV,gBAAM,aAAc,MAAM,YAAuB,MAAM,GAAG,EAAE,IAAI,CAAAE,QAAM,SAASA,IAAG,KAAK,CAAC,CAAC;AACzF,gBAAM,WAAW,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA,yDACO,WAAW,IAAI,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,WACjF,EAAE,KAAK,GAAG,UAAU,EAAE,IAAI;AAC3B,6BAAmB,SAAS;AAAA,QAC9B;AAAA,MACF;AAEA,aAAgB,QAAQ,GAAG,0DAAa;AAAA,QACtC,OAAO;AAAA,UACL,GAAG;AAAA,UACH,mBAAmB;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH,SAASD,QAAO;AACd,cAAQ,MAAM,oBAAoBA,MAAK;AACvC,aAAgB,MAAM,GAAG,8CAAW,mBAAmB,GAAG;AAAA,IAC5D;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,IAAI,mBAAmB,OAAO,MAAM;AACtC,QAAI;AACF,YAAM,KAAK,SAAS,EAAE,IAAI,MAAM,IAAI,CAAC;AACrC,YAAM,aAAa,MAAM,EAAE,IAAI,KAAK;AAEpC,UAAI,CAAC,MAAM,MAAM,GAAG;AAClB,eAAgB,MAAM,GAAG,0CAAY,oBAAoB,GAAG;AAAA,MAC9D;AAGA,YAAM,WAAW,MAAM,EAAE,IAAI,GAAG,QAAQ,oCAAoC,EAAE,KAAK,EAAE,EAAE,MAAM;AAC7F,UAAI,CAAC,UAAU;AACb,eAAgB,MAAM,GAAG,wCAAU,mBAAmB,GAAG;AAAA,MAC3D;AAGA,UAAI,WAAW,eAAe,WAAW,YAAY,SAAS,GAAG;AAC/D,cAAM,eAAe,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA,gEACY,WAAW,YAAY,IAAI,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,SACtG,EAAE,KAAK,GAAG,WAAW,WAAW,EAAE,MAAM;AAEzC,YAAK,cAAc,UAAqB,WAAW,YAAY,QAAQ;AACrE,iBAAgB,MAAM,GAAG,4DAAe,uBAAuB,GAAG;AAAA,QACpE;AAAA,MACF;AAGA,YAAM,eAAyB,CAAC;AAChC,YAAM,SAAgB,CAAC;AAEvB,UAAI,WAAW,aAAa,QAAW;AACrC,cAAM,eAAe,MAAa,KAAK,WAAW,UAAU,EAAE;AAC9D,qBAAa,KAAK,mBAAmB;AACrC,eAAO,KAAK,YAAY;AAAA,MAC1B;AACA,UAAI,WAAW,SAAS,QAAW;AACjC,qBAAa,KAAK,UAAU;AAC5B,eAAO,KAAK,WAAW,IAAI;AAAA,MAC7B;AACA,UAAI,WAAW,gBAAgB,QAAW;AACxC,qBAAa,KAAK,iBAAiB;AACnC,eAAO,KAAK,WAAW,cAAc,KAAK,UAAU,WAAW,WAAW,IAAI,IAAI;AAAA,MACpF;AACA,UAAI,WAAW,WAAW,QAAW;AACnC,qBAAa,KAAK,YAAY;AAC9B,eAAO,KAAK,WAAW,MAAM;AAAA,MAC/B;AAEA,mBAAa,KAAK,8BAAgC;AAClD,aAAO,KAAK,EAAE;AAEd,YAAM,QAAQ,qBAAqB,aAAa,KAAK,IAAI,CAAC;AAC1D,YAAM,EAAE,IAAI,GAAG,QAAQ,KAAK,EAAE,KAAK,GAAG,MAAM,EAAE,IAAI;AAGlD,YAAM,WAAW,cAAc,EAAE;AACjC,YAAM,EAAE,IAAI,MAAM,OAAO,QAAQ;AAEjC,aAAgB,QAAQ,GAAG,4CAAS;AAAA,IACtC,SAASC,QAAO;AACd,cAAQ,MAAM,uBAAuBA,MAAK;AAC1C,aAAgB,MAAM,GAAG,8CAAW,sBAAsB,GAAG;AAAA,IAC/D;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,OAAO,mBAAmB,OAAO,MAAM;AACzC,QAAI;AACF,YAAM,eAAe,EAAE,IAAI,OAAO;AAClC,YAAM,KAAK,SAAS,EAAE,IAAI,MAAM,IAAI,CAAC;AAErC,UAAI,CAAC,MAAM,MAAM,GAAG;AAClB,eAAgB,MAAM,GAAG,0CAAY,oBAAoB,GAAG;AAAA,MAC9D;AAGA,UAAI,aAAa,aAAa,IAAI;AAChC,eAAgB,MAAM,GAAG,0DAAa,sBAAsB,GAAG;AAAA,MACjE;AAGA,YAAM,WAAW,MAAM,EAAE,IAAI,GAAG,QAAQ,oCAAoC,EAAE,KAAK,EAAE,EAAE,MAAM;AAC7F,UAAI,CAAC,UAAU;AACb,eAAgB,MAAM,GAAG,wCAAU,mBAAmB,GAAG;AAAA,MAC3D;AAGA,YAAM,iBAAiB,MAAM,EAAE,IAAI,GAAG,QAAQ,iFAAmF,EAAE,KAAK,EAAE,EAAE,MAAM;AAClJ,UAAI,kBAAmB,eAAe,QAAmB,GAAG;AAC1D,eAAgB,MAAM,GAAG,8FAAmB,6BAA6B,GAAG;AAAA,MAC9E;AAEA,YAAM,EAAE,IAAI,GAAG,QAAQ,iCAAiC,EAAE,KAAK,EAAE,EAAE,IAAI;AAGvE,YAAM,WAAW,cAAc,EAAE;AACjC,YAAM,EAAE,IAAI,MAAM,OAAO,QAAQ;AAEjC,aAAgB,QAAQ,GAAG,4CAAS;AAAA,IACtC,SAASC,QAAO;AACd,cAAQ,MAAM,uBAAuBA,MAAK;AAC1C,aAAgB,MAAM,GAAG,8CAAW,sBAAsB,GAAG;AAAA,IAC/D;AAAA,EACF,CAAC;AACH;AAtQgB;;;AENhB;AAMO,SAAS,mBAAmBE,MAA6C;AAE9E,EAAAA,KAAI,IAAI,oBAAoB,eAAe,GAAG,OAAO,MAAM;AACzD,QAAI;AACF,YAAM,QAAQ,EAAE,IAAI,OAAO;AAC3B,YAAM,QAAQ,EAAE,IAAI,MAAM;AAC1B,YAAM,OAAO,SAAS,MAAM,QAAQ,GAAG;AACvC,YAAM,QAAQ,SAAS,MAAM,SAAS,IAAI;AAC1C,YAAM,aAAa,MAAM,aAAa,SAAS,MAAM,UAAU,IAAI;AACnE,YAAM,SAAS,MAAM;AACrB,YAAM,SAAS,MAAM;AACrB,YAAM,UAAU,OAAO,KAAK;AAE5B,UAAI,WAAW;AAAA;AAAA;AAAA;AAAA;AAKf,YAAM,SAAgB,CAAC;AACvB,YAAM,aAAuB,CAAC;AAG9B,UAAI,MAAM,SAAS,SAAS;AAC1B,cAAM,YAAY,MAAM,EAAE,IAAI,MAAM,IAAI,cAAc,MAAM,QAAQ,EAAE;AACtE,YAAI,WAAW;AACb,gBAAM,EAAE,YAAY,IAAI,KAAK,MAAM,SAAS;AAC5C,cAAI,eAAe,YAAY,SAAS,GAAG;AACzC,uBAAW,KAAK,oBAAoB,YAAY,IAAI,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC,GAAG;AAC3E,mBAAO,KAAK,GAAG,WAAW;AAAA,UAC5B,OAAO;AAEL,mBAAgB,QAAQ,GAAG,wCAAU;AAAA,cACnC,UAAU,CAAC;AAAA,cACX,YAAY,EAAE,MAAM,OAAO,OAAO,GAAG,YAAY,EAAE;AAAA,YACrD,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAEA,UAAI,YAAY;AACd,mBAAW,KAAK,kBAAkB;AAClC,eAAO,KAAK,UAAU;AAAA,MACxB;AAEA,UAAI,QAAQ;AACV,mBAAW,KAAK,cAAc;AAC9B,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,UAAI,QAAQ;AACV,mBAAW,KAAK,sBAAsB;AACtC,eAAO,KAAK,IAAI,MAAM,GAAG;AAAA,MAC3B;AAEA,UAAI,WAAW,SAAS,GAAG;AACzB,oBAAY,YAAY,WAAW,KAAK,OAAO;AAAA,MACjD;AAEA,kBAAY;AACZ,aAAO,KAAK,OAAO,MAAM;AAEzB,YAAM,WAAW,MAAM,EAAE,IAAI,GAAG,QAAQ,QAAQ,EAAE,KAAK,GAAG,MAAM,EAAE,IAAI;AAGtE,UAAI,aAAa;AACjB,UAAI,WAAW,SAAS,GAAG;AACzB,sBAAc,YAAY,WAAW,KAAK,OAAO;AAAA,MACnD;AACA,YAAM,cAAc,MAAM,EAAE,IAAI,GAAG,QAAQ,UAAU,EAAE,KAAK,GAAG,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM;AAE1F,aAAgB,QAAQ,GAAG,0DAAa;AAAA,QACtC,UAAU,SAAS;AAAA,QACnB,YAAY;AAAA,UACV;AAAA,UACA;AAAA,UACA,OAAQ,aAAa,SAAoB;AAAA,UACzC,YAAY,KAAK,MAAO,aAAa,SAAoB,KAAK,KAAK;AAAA,QACrE;AAAA,MACF,CAAC;AAAA,IACH,SAASC,QAAO;AACd,cAAQ,MAAM,uBAAuBA,MAAK;AAC1C,aAAgB,MAAM,GAAG,yBAAyB,sBAAsB,GAAG;AAAA,IAC7E;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,KAAK,oBAAoB,eAAe,GAAG,OAAO,MAAM;AAC1D,QAAI;AACF,YAAM,QAAQ,EAAE,IAAI,OAAO;AAC3B,YAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,YAAM,EAAE,YAAY,OAAO,YAAY,YAAY,IAAI;AAEvD,UAAI,CAAC,cAAc,CAAC,OAAO;AACzB,eAAgB,MAAM,GAAG,4DAAe,2BAA2B,GAAG;AAAA,MACxE;AAEA,UAAI,SAAS,KAAK,QAAQ,KAAM;AAC9B,eAAgB,MAAM,GAAG,yDAAiB,iBAAiB,GAAG;AAAA,MAChE;AAGA,YAAM,UAAU,MAAM,EAAE,IAAI,GAAG,QAAQ,2DAA6D,EAAE,KAAK,UAAU,EAAE,MAAM;AAC7H,UAAI,CAAC,SAAS;AACZ,eAAgB,MAAM,GAAG,iCAAiC,qBAAqB,GAAG;AAAA,MACpF;AAGA,UAAI,MAAM,SAAS,SAAS;AAC1B,cAAM,YAAY,MAAM,EAAE,IAAI,MAAM,IAAI,cAAc,MAAM,QAAQ,EAAE;AACtE,YAAI,WAAW;AACb,gBAAM,EAAE,YAAY,IAAI,KAAK,MAAM,SAAS;AAC5C,cAAI,CAAC,eAAe,CAAC,YAAY,SAAS,WAAW,SAAS,CAAC,GAAG;AAChE,mBAAgB,MAAM,GAAG,iCAAiC,yBAAyB,GAAG;AAAA,UACxF;AAAA,QACF;AAAA,MACF;AAGA,YAAM,WAAqB,CAAC;AAC5B,YAAM,iBAAiC,CAAC;AAExC,eAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,cAAM,aAAa,kBAAkB,EAAE;AACvC,iBAAS,KAAK,UAAU;AAExB,uBAAe;AAAA,UACb,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA,WAGhB,EAAE,KAAK,YAAY,YAAY,cAAc,MAAM,eAAe,MAAM,MAAM,QAAQ,EAAE,IAAI;AAAA,QAC/F;AAAA,MACF;AAEA,YAAM,QAAQ,IAAI,cAAc;AAGhC,YAAM,YAAY;AAClB,YAAM,aAAa,YAAY;AAE/B,YAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA,OAGtB,EAAE,KAAK,MAAM,UAAU,YAAY,OAAO,WAAW,UAAU,EAAE,IAAI;AAEtE,aAAgB,QAAQ,GAAG,2BAAO,KAAK,4BAAQ,EAAE,SAAS,GAAG,GAAG;AAAA,IAClE,SAASC,QAAO;AACd,cAAQ,MAAM,0BAA0BA,MAAK;AAC7C,aAAgB,MAAM,GAAG,yBAAyB,yBAAyB,GAAG;AAAA,IAChF;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,IAAI,wBAAwB,eAAe,GAAG,OAAO,MAAM;AAC7D,QAAI;AACF,YAAM,QAAQ,EAAE,IAAI,OAAO;AAC3B,YAAM,KAAK,SAAS,EAAE,IAAI,MAAM,IAAI,CAAC;AAErC,UAAI,CAAC,MAAM,MAAM,GAAG;AAClB,eAAgB,MAAM,GAAG,0CAAY,sBAAsB,GAAG;AAAA,MAChE;AAEA,YAAM,UAAU,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,OAKtC,EAAE,KAAK,EAAE,EAAE,MAAM;AAElB,UAAI,CAAC,SAAS;AACZ,eAAgB,MAAM,GAAG,qBAAqB,qBAAqB,GAAG;AAAA,MACxE;AAGA,UAAI,MAAM,SAAS,SAAS;AAC1B,cAAM,YAAY,MAAM,EAAE,IAAI,MAAM,IAAI,cAAc,MAAM,QAAQ,EAAE;AACtE,YAAI,WAAW;AACb,gBAAM,EAAE,YAAY,IAAI,KAAK,MAAM,SAAS;AAC5C,cAAI,CAAC,eAAe,CAAC,YAAY,SAAU,QAAQ,WAAsB,SAAS,CAAC,GAAG;AACpF,mBAAgB,MAAM,GAAG,iCAAiC,yBAAyB,GAAG;AAAA,UACxF;AAAA,QACF;AAAA,MACF;AAGA,YAAM,UAAU,MAAM,EAAE,IAAI,GAAG,QAAQ,4CAA4C,EAAE,KAAK,EAAE,EAAE,IAAI;AAGlG,YAAM,OAAO,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,OAKnC,EAAE,KAAK,QAAQ,WAAW,EAAE,IAAI;AAEjC,aAAgB,QAAQ,GAAG,0DAAa;AAAA,QACtC;AAAA,QACA,SAAS,QAAQ;AAAA,QACjB,mBAAmB,KAAK;AAAA,MAC1B,CAAC;AAAA,IACH,SAASC,QAAO;AACd,cAAQ,MAAM,sBAAsBA,MAAK;AACzC,aAAgB,MAAM,GAAG,yBAAyB,qBAAqB,GAAG;AAAA,IAC5E;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,IAAI,wBAAwB,eAAe,GAAG,OAAO,MAAM;AAC7D,QAAI;AACF,YAAM,QAAQ,EAAE,IAAI,OAAO;AAC3B,YAAM,KAAK,SAAS,EAAE,IAAI,MAAM,IAAI,CAAC;AACrC,YAAM,aAAa,MAAM,EAAE,IAAI,KAAK;AAEpC,UAAI,CAAC,MAAM,MAAM,GAAG;AAClB,eAAgB,MAAM,GAAG,0CAAY,sBAAsB,GAAG;AAAA,MAChE;AAGA,YAAM,UAAU,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,OAKtC,EAAE,KAAK,EAAE,EAAE,MAAM;AAElB,UAAI,CAAC,SAAS;AACZ,eAAgB,MAAM,GAAG,qBAAqB,qBAAqB,GAAG;AAAA,MACxE;AAGA,UAAI,MAAM,SAAS,SAAS;AAC1B,cAAM,YAAY,MAAM,EAAE,IAAI,MAAM,IAAI,cAAc,MAAM,QAAQ,EAAE;AACtE,YAAI,WAAW;AACb,gBAAM,EAAE,YAAY,IAAI,KAAK,MAAM,SAAS;AAC5C,cAAI,CAAC,eAAe,CAAC,YAAY,SAAU,QAAQ,WAAsB,SAAS,CAAC,GAAG;AACpF,mBAAgB,MAAM,GAAG,iCAAiC,yBAAyB,GAAG;AAAA,UACxF;AAAA,QACF;AAAA,MACF;AAGA,YAAM,eAAyB,CAAC;AAChC,YAAM,SAAgB,CAAC;AAEvB,UAAI,WAAW,WAAW,QAAW;AACnC,qBAAa,KAAK,YAAY;AAC9B,eAAO,KAAK,WAAW,MAAM;AAAA,MAC/B;AACA,UAAI,WAAW,eAAe,QAAW;AACvC,qBAAa,KAAK,gBAAgB;AAClC,eAAO,KAAK,WAAW,UAAU;AAAA,MACnC;AACA,UAAI,WAAW,gBAAgB,QAAW;AACxC,qBAAa,KAAK,iBAAiB;AACnC,eAAO,KAAK,WAAW,WAAW;AAAA,MACpC;AAEA,mBAAa,KAAK,8BAAgC;AAClD,aAAO,KAAK,EAAE;AAEd,YAAM,QAAQ,uBAAuB,aAAa,KAAK,IAAI,CAAC;AAC5D,YAAM,EAAE,IAAI,GAAG,QAAQ,KAAK,EAAE,KAAK,GAAG,MAAM,EAAE,IAAI;AAGlD,YAAM,WAAW,kBAAkB,QAAQ,WAAW;AACtD,YAAM,EAAE,IAAI,MAAM,OAAO,QAAQ;AAEjC,aAAgB,QAAQ,GAAG,4CAAS;AAAA,IACtC,SAASC,QAAO;AACd,cAAQ,MAAM,yBAAyBA,MAAK;AAC5C,aAAgB,MAAM,GAAG,yBAAyB,wBAAwB,GAAG;AAAA,IAC/E;AAAA,EACF,CAAC;AACH;AAhRgB;;;ACNhB;AAIO,SAAS,kBAAkBC,MAA6C;AAE7E,EAAAA,KAAI,KAAK,WAAW,OAAO,MAAM;AAC/B,QAAI;AACF,YAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,YAAM,EAAE,aAAa,WAAW,iBAAiB,IAAI;AAErD,UAAI,CAAC,aAAa;AAChB,eAAgB,MAAM,GAAG,0DAAa,uBAAuB,GAAG;AAAA,MAClE;AAGA,YAAM,WAAW,kBAAkB,WAAW;AAC9C,YAAM,SAAS,MAAM,EAAE,IAAI,MAAM,IAAI,QAAQ;AAE7C,UAAI,QAAQ;AACV,cAAM,aAAa,KAAK,MAAM,MAAM;AACpC,eAAgB,QAAQ,GAAG,4DAAe;AAAA,UACxC,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AAGA,YAAM,UAAU,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,OAKtC,EAAE,KAAK,WAAW,EAAE,MAAM;AAE3B,UAAI,CAAC,SAAS;AAEZ,cAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA,SAGtB,EAAE;AAAA,UACD;AAAA,UACA,aAAa;AAAA,UACb,EAAE,IAAI,OAAO,kBAAkB,KAAK,EAAE,IAAI,OAAO,iBAAiB,KAAK;AAAA,UACvE,EAAE,IAAI,OAAO,YAAY,KAAK;AAAA,QAChC,EAAE,IAAI;AAEN,eAAgB,MAAM,GAAG,4EAAgB,mBAAmB,GAAG;AAAA,MACjE;AAGA,UAAI,QAAQ,cAAc,IAAI,KAAK,QAAQ,UAAoB,IAAI,oBAAI,KAAK,GAAG;AAC7E,eAAgB,MAAM,GAAG,wCAAU,mBAAmB,GAAG;AAAA,MAC3D;AAGA,UAAI,iBAAiB;AACrB,UAAI,QAAQ,0BAA0B,kBAAkB,WAAW;AACjE,cAAM,cAAc,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA,SAE1C,EAAE,KAAK,QAAQ,EAAE,EAAE,MAAM;AAE1B,yBAAkB,aAAa,SAAoB;AACnD,cAAM,aAAc,QAAQ,eAA2B,QAAQ,uBAAkC;AAGjG,cAAM,iBAAiB,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA,SAE7C,EAAE,KAAK,QAAQ,IAAI,SAAS,EAAE,MAAM;AAErC,YAAI,CAAC,kBAAkB,kBAAkB,YAAY;AACnD,iBAAgB,MAAM,GAAG,qEAAc,UAAU,sBAAO,yBAAyB,GAAG;AAAA,QACtF;AAGA,YAAI,CAAC,gBAAgB;AACnB,gBAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA,WAGtB,EAAE,KAAK,QAAQ,IAAc,WAAW,KAAK,UAAU,EAAE,YAAY,EAAE,IAAI,OAAO,YAAY,EAAE,CAAC,CAAC,EAAE,IAAI;AACzG;AAAA,QACF,OAAO;AACL,gBAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA,WAGtB,EAAE,KAAK,QAAQ,IAAc,SAAS,EAAE,IAAI;AAAA,QAC/C;AAAA,MACF;AAGA,UAAI,WAAqB,CAAC;AAC1B,UAAI,QAAQ,UAAU;AACpB,YAAI;AACF,qBAAW,KAAK,MAAM,QAAQ,QAAkB;AAAA,QAClD,QAAQ;AACN,qBAAY,QAAQ,SAAoB,MAAM,GAAG,EAAE,IAAI,CAAC,MAAc,EAAE,KAAK,CAAC;AAAA,QAChF;AAAA,MACF;AAEA,YAAM,cAAc;AAAA,QAClB,cAAc,QAAQ;AAAA,QACtB,YAAY,QAAQ;AAAA,QACpB,aAAa,QAAQ,eAAe,QAAQ;AAAA,QAC5C,iBAAiB;AAAA,QACjB;AAAA,MACF;AAGA,YAAM,EAAE,IAAI,MAAM,IAAI,UAAU,KAAK,UAAU,WAAW,GAAG,EAAE,eAAe,IAAI,CAAC;AAGnF,YAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA,OAGtB,EAAE;AAAA,QACD;AAAA,QACA,aAAa;AAAA,QACb,EAAE,IAAI,OAAO,kBAAkB,KAAK,EAAE,IAAI,OAAO,iBAAiB,KAAK;AAAA,QACvE,EAAE,IAAI,OAAO,YAAY,KAAK;AAAA,MAChC,EAAE,IAAI;AAEN,aAAgB,QAAQ,GAAG,8CAAW;AAAA,QACpC,cAAc;AAAA,MAChB,CAAC;AAAA,IAEH,SAASC,QAAO;AACd,cAAQ,MAAM,+BAA+BA,MAAK;AAClD,aAAgB,MAAM,GAAG,8CAAW,sBAAsB,GAAG;AAAA,IAC/D;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,KAAK,kBAAkB,OAAO,MAAM;AACtC,QAAI;AACF,YAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,YAAM,EAAE,aAAa,UAAU,IAAI;AAEnC,UAAI,CAAC,eAAe,CAAC,WAAW;AAC9B,eAAgB,MAAM,GAAG,8EAAkB,sBAAsB,GAAG;AAAA,MACtE;AAGA,YAAM,UAAU,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA,OAEtC,EAAE,KAAK,WAAW,EAAE,MAAM;AAE3B,UAAI,CAAC,SAAS;AACZ,eAAgB,MAAM,GAAG,4EAAgB,mBAAmB,GAAG;AAAA,MACjE;AAGA,YAAM,SAAS,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA,OAErC,EAAE,KAAK,QAAQ,IAAc,SAAS,EAAE,IAAI;AAE7C,UAAI,OAAO,KAAK,YAAY,GAAG;AAC7B,eAAgB,MAAM,GAAG,8CAAW,oBAAoB,GAAG;AAAA,MAC7D;AAGA,YAAM,WAAW,kBAAkB,WAAW;AAC9C,YAAM,EAAE,IAAI,MAAM,OAAO,QAAQ;AAEjC,aAAgB,QAAQ,GAAG,sCAAQ;AAAA,IAErC,SAASC,QAAO;AACd,cAAQ,MAAM,wBAAwBA,MAAK;AAC3C,aAAgB,MAAM,GAAG,8CAAW,gBAAgB,GAAG;AAAA,IACzD;AAAA,EACF,CAAC;AACH;AArKgB;;;ACJhB;AAKO,SAAS,iBAAiBC,MAA6C;AAE5E,EAAAA,KAAI,IAAI,eAAe,eAAe,GAAG,OAAO,MAAM;AACpD,QAAI;AACF,YAAM,QAAQ,EAAE,IAAI,OAAO;AAC3B,YAAM,QAAQ,EAAE,IAAI,MAAM;AAC1B,YAAM,OAAO,SAAS,MAAM,QAAQ,GAAG;AACvC,YAAM,QAAQ,SAAS,MAAM,SAAS,IAAI;AAC1C,YAAM,SAAS,MAAM;AACrB,YAAM,aAAa,MAAM,aAAa,SAAS,MAAM,UAAU,IAAI;AACnE,YAAM,aAAa,MAAM;AACzB,YAAM,WAAW,MAAM;AACvB,YAAM,UAAU,OAAO,KAAK;AAE5B,UAAI,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAMf,YAAM,SAAgB,CAAC;AACvB,YAAM,aAAuB,CAAC;AAG9B,UAAI,MAAM,SAAS,SAAS;AAC1B,mBAAW,KAAK,gBAAgB;AAChC,eAAO,KAAK,MAAM,QAAQ;AAAA,MAC5B;AAEA,UAAI,QAAQ;AACV,mBAAW,KAAK,cAAc;AAC9B,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,UAAI,YAAY;AACd,mBAAW,KAAK,kBAAkB;AAClC,eAAO,KAAK,UAAU;AAAA,MACxB;AAEA,UAAI,YAAY;AACd,mBAAW,KAAK,mBAAmB;AACnC,eAAO,KAAK,UAAU;AAAA,MACxB;AAEA,UAAI,UAAU;AACZ,mBAAW,KAAK,mBAAmB;AACnC,eAAO,KAAK,QAAQ;AAAA,MACtB;AAEA,UAAI,WAAW,SAAS,GAAG;AACzB,oBAAY,YAAY,WAAW,KAAK,OAAO;AAAA,MACjD;AAEA,kBAAY;AACZ,aAAO,KAAK,OAAO,MAAM;AAEzB,YAAM,SAAS,MAAM,EAAE,IAAI,GAAG,QAAQ,QAAQ,EAAE,KAAK,GAAG,MAAM,EAAE,IAAI;AAGpE,UAAI,aAAa;AACjB,UAAI,WAAW,SAAS,GAAG;AACzB,sBAAc,YAAY,WAAW,KAAK,OAAO;AAAA,MACnD;AACA,YAAM,cAAc,MAAM,EAAE,IAAI,GAAG,QAAQ,UAAU,EAAE,KAAK,GAAG,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM;AAE1F,aAAgB,QAAQ,GAAG,oDAAY;AAAA,QACrC,QAAQ,OAAO;AAAA,QACf,YAAY;AAAA,UACV;AAAA,UACA;AAAA,UACA,OAAQ,aAAa,SAAoB;AAAA,UACzC,YAAY,KAAK,MAAO,aAAa,SAAoB,KAAK,KAAK;AAAA,QACrE;AAAA,MACF,CAAC;AAAA,IACH,SAASC,QAAO;AACd,cAAQ,MAAM,qBAAqBA,MAAK;AACxC,aAAgB,MAAM,GAAG,yBAAyB,oBAAoB,GAAG;AAAA,IAC3E;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,KAAK,eAAe,eAAe,GAAG,OAAO,MAAM;AACrD,QAAI;AACF,YAAM,QAAQ,EAAE,IAAI,OAAO;AAC3B,YAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,YAAM,EAAE,YAAY,eAAe,WAAW,IAAI;AAElD,UAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,YAAY;AAChD,eAAgB,MAAM,GAAG,gGAAqB,2BAA2B,GAAG;AAAA,MAC9E;AAGA,YAAM,UAAU,MAAM,EAAE,IAAI,GAAG,QAAQ,2DAA6D,EAAE,KAAK,UAAU,EAAE,MAAM;AAC7H,UAAI,CAAC,SAAS;AACZ,eAAgB,MAAM,GAAG,iCAAiC,qBAAqB,GAAG;AAAA,MACpF;AAGA,UAAI,MAAM,SAAS,SAAS;AAC1B,cAAM,YAAY,MAAM,EAAE,IAAI,MAAM,IAAI,cAAc,MAAM,QAAQ,EAAE;AACtE,YAAI,WAAW;AACb,gBAAM,EAAE,YAAY,IAAI,KAAK,MAAM,SAAS;AAC5C,cAAI,CAAC,eAAe,CAAC,YAAY,SAAS,WAAW,SAAS,CAAC,GAAG;AAChE,mBAAgB,MAAM,GAAG,iCAAiC,yBAAyB,GAAG;AAAA,UACxF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,aAAa,aAAa;AAEhC,YAAM,SAAS,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA,OAGrC,EAAE,KAAK,MAAM,UAAU,YAAY,eAAe,YAAY,UAAU,EAAE,IAAI;AAE/E,aAAgB,QAAQ,GAAG,wCAAU;AAAA,QACnC,IAAI,OAAO,KAAK;AAAA,QAChB,aAAa;AAAA,MACf,GAAG,GAAG;AAAA,IACR,SAASC,QAAO;AACd,cAAQ,MAAM,uBAAuBA,MAAK;AAC1C,aAAgB,MAAM,GAAG,yBAAyB,sBAAsB,GAAG;AAAA,IAC7E;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,IAAI,mBAAmB,eAAe,GAAG,OAAO,MAAM;AACxD,QAAI;AACF,YAAM,QAAQ,EAAE,IAAI,OAAO;AAC3B,YAAM,KAAK,SAAS,EAAE,IAAI,MAAM,IAAI,CAAC;AAErC,UAAI,CAAC,MAAM,MAAM,GAAG;AAClB,eAAgB,MAAM,GAAG,oCAAW,oBAAoB,GAAG;AAAA,MAC7D;AAEA,UAAI,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASf,UAAI,MAAM,SAAS,SAAS;AAC1B,oBAAY;AAAA,MACd;AAEA,YAAM,SAAS,MAAM,SAAS,UAAU,CAAC,EAAE,IAAI,CAAC,IAAI,MAAM,QAAQ;AAClE,YAAM,QAAQ,MAAM,EAAE,IAAI,GAAG,QAAQ,QAAQ,EAAE,KAAK,GAAG,MAAM,EAAE,MAAM;AAErE,UAAI,CAAC,OAAO;AACV,eAAgB,MAAM,GAAG,oCAAoC,mBAAmB,GAAG;AAAA,MACrF;AAEA,aAAgB,QAAQ,GAAG,oDAAY,EAAE,MAAM,CAAC;AAAA,IAClD,SAASC,QAAO;AACd,cAAQ,MAAM,oBAAoBA,MAAK;AACvC,aAAgB,MAAM,GAAG,yBAAyB,mBAAmB,GAAG;AAAA,IAC1E;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,IAAI,mBAAmB,eAAe,GAAG,OAAO,MAAM;AACxD,QAAI;AACF,YAAM,QAAQ,EAAE,IAAI,OAAO;AAC3B,YAAM,KAAK,SAAS,EAAE,IAAI,MAAM,IAAI,CAAC;AACrC,YAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,YAAM,EAAE,OAAO,IAAI;AAEnB,UAAI,CAAC,MAAM,MAAM,GAAG;AAClB,eAAgB,MAAM,GAAG,oCAAW,oBAAoB,GAAG;AAAA,MAC7D;AAEA,UAAI,CAAC,QAAQ;AACX,eAAgB,MAAM,GAAG,wCAAU,kBAAkB,GAAG;AAAA,MAC1D;AAGA,UAAI,aAAa;AACjB,UAAI,MAAM,SAAS,SAAS;AAC1B,sBAAc;AAAA,MAChB;AAEA,YAAM,SAAS,MAAM,SAAS,UAAU,CAAC,EAAE,IAAI,CAAC,IAAI,MAAM,QAAQ;AAClE,YAAM,QAAQ,MAAM,EAAE,IAAI,GAAG,QAAQ,UAAU,EAAE,KAAK,GAAG,MAAM,EAAE,MAAM;AAEvE,UAAI,CAAC,OAAO;AACV,eAAgB,MAAM,GAAG,oCAAoC,mBAAmB,GAAG;AAAA,MACrF;AAEA,YAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA,OAEtB,EAAE,KAAK,QAAQ,EAAE,EAAE,IAAI;AAExB,aAAgB,QAAQ,GAAG,kDAAU;AAAA,IACvC,SAASC,QAAO;AACd,cAAQ,MAAM,uBAAuBA,MAAK;AAC1C,aAAgB,MAAM,GAAG,yBAAyB,sBAAsB,GAAG;AAAA,IAC7E;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,OAAO,mBAAmB,eAAe,GAAG,OAAO,MAAM;AAC3D,QAAI;AACF,YAAM,QAAQ,EAAE,IAAI,OAAO;AAC3B,YAAM,KAAK,SAAS,EAAE,IAAI,MAAM,IAAI,CAAC;AAErC,UAAI,CAAC,MAAM,MAAM,GAAG;AAClB,eAAgB,MAAM,GAAG,oCAAW,oBAAoB,GAAG;AAAA,MAC7D;AAGA,UAAI,aAAa;AACjB,UAAI,MAAM,SAAS,SAAS;AAC1B,sBAAc;AAAA,MAChB;AAEA,YAAM,SAAS,MAAM,SAAS,UAAU,CAAC,EAAE,IAAI,CAAC,IAAI,MAAM,QAAQ;AAClE,YAAM,QAAQ,MAAM,EAAE,IAAI,GAAG,QAAQ,UAAU,EAAE,KAAK,GAAG,MAAM,EAAE,MAAM;AAEvE,UAAI,CAAC,OAAO;AACV,eAAgB,MAAM,GAAG,oCAAoC,mBAAmB,GAAG;AAAA,MACrF;AAGA,UAAI,MAAM,WAAW,WAAW;AAC9B,eAAgB,MAAM,GAAG,8CAA8C,uBAAuB,GAAG;AAAA,MACnG;AAEA,YAAM,EAAE,IAAI,GAAG,QAAQ,iCAAiC,EAAE,KAAK,EAAE,EAAE,IAAI;AAEvE,aAAgB,QAAQ,GAAG,sCAAQ;AAAA,IACrC,SAASC,QAAO;AACd,cAAQ,MAAM,uBAAuBA,MAAK;AAC1C,aAAgB,MAAM,GAAG,yBAAyB,sBAAsB,GAAG;AAAA,IAC7E;AAAA,EACF,CAAC;AACH;AA9OgB;;;ACLhB;AAKO,SAAS,mBAAmBC,MAA6C;AAE9E,EAAAA,KAAI,IAAI,iBAAiB,eAAe,GAAG,OAAO,MAAM;AACtD,QAAI;AACF,YAAM,QAAQ,EAAE,IAAI,MAAM;AAC1B,YAAM,OAAO,SAAS,MAAM,QAAQ,GAAG;AACvC,YAAM,QAAQ,SAAS,MAAM,SAAS,IAAI;AAC1C,YAAM,SAAS,MAAM;AACrB,YAAM,SAAS,MAAM;AACrB,YAAM,UAAU,OAAO,KAAK;AAE5B,UAAI,WAAW;AACf,YAAM,SAAgB,CAAC;AACvB,YAAM,aAAuB,CAAC;AAE9B,UAAI,QAAQ;AACV,mBAAW,KAAK,YAAY;AAC5B,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,UAAI,QAAQ;AACV,mBAAW,KAAK,qCAAqC;AACrD,eAAO,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,GAAG;AAAA,MAC1C;AAEA,UAAI,WAAW,SAAS,GAAG;AACzB,oBAAY,YAAY,WAAW,KAAK,OAAO;AAAA,MACjD;AAEA,kBAAY;AACZ,aAAO,KAAK,OAAO,MAAM;AAEzB,YAAM,WAAW,MAAM,EAAE,IAAI,GAAG,QAAQ,QAAQ,EAAE,KAAK,GAAG,MAAM,EAAE,IAAI;AAGtE,UAAI,aAAa;AACjB,UAAI,WAAW,SAAS,GAAG;AACzB,sBAAc,YAAY,WAAW,KAAK,OAAO;AAAA,MACnD;AACA,YAAM,cAAc,MAAM,EAAE,IAAI,GAAG,QAAQ,UAAU,EAAE,KAAK,GAAG,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM;AAE1F,aAAgB,QAAQ,GAAG,oDAAY;AAAA,QACrC,UAAU,SAAS;AAAA,QACnB,YAAY;AAAA,UACV;AAAA,UACA;AAAA,UACA,OAAQ,aAAa,SAAoB;AAAA,UACzC,YAAY,KAAK,MAAO,aAAa,SAAoB,KAAK,KAAK;AAAA,QACrE;AAAA,MACF,CAAC;AAAA,IACH,SAASC,QAAO;AACd,cAAQ,MAAM,uBAAuBA,MAAK;AAC1C,aAAgB,MAAM,GAAG,8CAAW,sBAAsB,GAAG;AAAA,IAC/D;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,KAAK,iBAAiB,eAAe,GAAG,eAAe,GAAG,OAAO,MAAM;AACzE,QAAI;AACF,YAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,YAAM,EAAE,MAAM,aAAa,uBAAuB,aAAa,SAAS,IAAI;AAE5E,UAAI,CAAC,QAAQ,CAAC,uBAAuB;AACnC,eAAgB,MAAM,GAAG,kFAAiB,2BAA2B,GAAG;AAAA,MAC1E;AAGA,YAAM,WAAW,MAAM,EAAE,IAAI,GAAG,QAAQ,wCAAwC,EAAE,KAAK,IAAI,EAAE,MAAM;AACnG,UAAI,UAAU;AACZ,eAAgB,MAAM,GAAG,8CAAW,0BAA0B,GAAG;AAAA,MACnE;AAEA,YAAM,eAAe,WAAW,KAAK,UAAU,QAAQ,IAAI;AAE3D,YAAM,SAAS,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA,OAGrC,EAAE,KAAK,MAAM,eAAe,MAAM,uBAAuB,eAAe,MAAM,YAAY,EAAE,IAAI;AAEjG,aAAgB,QAAQ,GAAG,wCAAU,EAAE,IAAI,OAAO,KAAK,YAAY,CAAC;AAAA,IACtE,SAASC,QAAO;AACd,cAAQ,MAAM,yBAAyBA,MAAK;AAC5C,aAAgB,MAAM,GAAG,8CAAW,wBAAwB,GAAG;AAAA,IACjE;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,IAAI,qBAAqB,eAAe,GAAG,OAAO,MAAM;AAC1D,QAAI;AACF,YAAM,KAAK,SAAS,EAAE,IAAI,MAAM,IAAI,CAAC;AAErC,UAAI,CAAC,MAAM,MAAM,GAAG;AAClB,eAAgB,MAAM,GAAG,oCAAW,sBAAsB,GAAG;AAAA,MAC/D;AAEA,YAAM,UAAU,MAAM,EAAE,IAAI,GAAG,QAAQ,qCAAqC,EAAE,KAAK,EAAE,EAAE,MAAM;AAE7F,UAAI,CAAC,SAAS;AACZ,eAAgB,MAAM,GAAG,kCAAS,qBAAqB,GAAG;AAAA,MAC5D;AAEA,aAAgB,QAAQ,GAAG,oDAAY,EAAE,QAAQ,CAAC;AAAA,IACpD,SAASC,QAAO;AACd,cAAQ,MAAM,sBAAsBA,MAAK;AACzC,aAAgB,MAAM,GAAG,8CAAW,qBAAqB,GAAG;AAAA,IAC9D;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,IAAI,qBAAqB,eAAe,GAAG,eAAe,GAAG,OAAO,MAAM;AAC5E,QAAI;AACF,YAAM,KAAK,SAAS,EAAE,IAAI,MAAM,IAAI,CAAC;AACrC,YAAM,aAAa,MAAM,EAAE,IAAI,KAAK;AAEpC,UAAI,CAAC,MAAM,MAAM,GAAG;AAClB,eAAgB,MAAM,GAAG,oCAAW,sBAAsB,GAAG;AAAA,MAC/D;AAGA,YAAM,WAAW,MAAM,EAAE,IAAI,GAAG,QAAQ,sCAAsC,EAAE,KAAK,EAAE,EAAE,MAAM;AAC/F,UAAI,CAAC,UAAU;AACb,eAAgB,MAAM,GAAG,kCAAS,qBAAqB,GAAG;AAAA,MAC5D;AAGA,YAAM,eAAyB,CAAC;AAChC,YAAM,SAAgB,CAAC;AAEvB,UAAI,WAAW,SAAS,QAAW;AACjC,qBAAa,KAAK,UAAU;AAC5B,eAAO,KAAK,WAAW,IAAI;AAAA,MAC7B;AACA,UAAI,WAAW,gBAAgB,QAAW;AACxC,qBAAa,KAAK,iBAAiB;AACnC,eAAO,KAAK,WAAW,WAAW;AAAA,MACpC;AACA,UAAI,WAAW,0BAA0B,QAAW;AAClD,qBAAa,KAAK,2BAA2B;AAC7C,eAAO,KAAK,WAAW,qBAAqB;AAAA,MAC9C;AACA,UAAI,WAAW,gBAAgB,QAAW;AACxC,qBAAa,KAAK,iBAAiB;AACnC,eAAO,KAAK,WAAW,WAAW;AAAA,MACpC;AACA,UAAI,WAAW,aAAa,QAAW;AACrC,qBAAa,KAAK,cAAc;AAChC,eAAO,KAAK,WAAW,WAAW,KAAK,UAAU,WAAW,QAAQ,IAAI,IAAI;AAAA,MAC9E;AACA,UAAI,WAAW,WAAW,QAAW;AACnC,qBAAa,KAAK,YAAY;AAC9B,eAAO,KAAK,WAAW,MAAM;AAAA,MAC/B;AAEA,mBAAa,KAAK,8BAAgC;AAClD,aAAO,KAAK,EAAE;AAEd,YAAM,QAAQ,uBAAuB,aAAa,KAAK,IAAI,CAAC;AAC5D,YAAM,EAAE,IAAI,GAAG,QAAQ,KAAK,EAAE,KAAK,GAAG,MAAM,EAAE,IAAI;AAElD,aAAgB,QAAQ,GAAG,sCAAQ;AAAA,IACrC,SAASC,QAAO;AACd,cAAQ,MAAM,yBAAyBA,MAAK;AAC5C,aAAgB,MAAM,GAAG,8CAAW,wBAAwB,GAAG;AAAA,IACjE;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,OAAO,qBAAqB,eAAe,GAAG,eAAe,GAAG,OAAO,MAAM;AAC/E,QAAI;AACF,YAAM,KAAK,SAAS,EAAE,IAAI,MAAM,IAAI,CAAC;AAErC,UAAI,CAAC,MAAM,MAAM,GAAG;AAClB,eAAgB,MAAM,GAAG,oCAAW,sBAAsB,GAAG;AAAA,MAC/D;AAGA,YAAM,WAAW,MAAM,EAAE,IAAI,GAAG,QAAQ,sCAAsC,EAAE,KAAK,EAAE,EAAE,MAAM;AAC/F,UAAI,CAAC,UAAU;AACb,eAAgB,MAAM,GAAG,kCAAS,qBAAqB,GAAG;AAAA,MAC5D;AAGA,YAAM,iBAAiB,MAAM,EAAE,IAAI,GAAG,QAAQ,mFAAqF,EAAE,KAAK,EAAE,EAAE,MAAM;AACpJ,UAAI,kBAAmB,eAAe,QAAmB,GAAG;AAC1D,eAAgB,MAAM,GAAG,wFAAkB,+BAA+B,GAAG;AAAA,MAC/E;AAEA,YAAM,EAAE,IAAI,GAAG,QAAQ,mCAAmC,EAAE,KAAK,EAAE,EAAE,IAAI;AAEzE,aAAgB,QAAQ,GAAG,sCAAQ;AAAA,IACrC,SAASC,QAAO;AACd,cAAQ,MAAM,yBAAyBA,MAAK;AAC5C,aAAgB,MAAM,GAAG,8CAAW,wBAAwB,GAAG;AAAA,IACjE;AAAA,EACF,CAAC;AACH;AAnMgB;;;ACLhB;AAKO,SAAS,sBAAsBC,MAA6C;AAEjF,EAAAA,KAAI,IAAI,oBAAoB,eAAe,GAAG,OAAO,MAAM;AACzD,QAAI;AACF,YAAM,QAAQ,EAAE,IAAI,OAAO;AAC3B,YAAM,QAAQ,EAAE,IAAI,MAAM;AAC1B,YAAM,SAAS,MAAM,UAAU;AAC/B,YAAM,aAAa,MAAM;AACzB,YAAM,WAAW,MAAM;AAGvB,UAAI,aAAa;AACjB,YAAM,SAAgB,CAAC;AAEvB,UAAI,cAAc,UAAU;AAC1B,qBAAa;AACb,eAAO,KAAK,YAAY,QAAQ;AAAA,MAClC,OAAO;AAEL,cAAM,MAAM,oBAAI,KAAK;AACrB,YAAI;AAEJ,gBAAQ,QAAQ;AAAA,UACd,KAAK;AACH,wBAAY,IAAI,KAAK,IAAI,YAAY,GAAG,IAAI,SAAS,GAAG,IAAI,QAAQ,CAAC;AACrE;AAAA,UACF,KAAK;AACH,wBAAY,IAAI,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK,KAAK,KAAK,GAAI;AAC5D;AAAA,UACF,KAAK;AACH,wBAAY,IAAI,KAAK,IAAI,YAAY,GAAG,IAAI,SAAS,GAAG,CAAC;AACzD;AAAA,UACF,KAAK;AACH,wBAAY,IAAI,KAAK,IAAI,YAAY,GAAG,GAAG,CAAC;AAC5C;AAAA,UACF;AACE,wBAAY,IAAI,KAAK,IAAI,YAAY,GAAG,IAAI,SAAS,GAAG,CAAC;AAAA,QAC7D;AAEA,qBAAa;AACb,eAAO,KAAK,UAAU,YAAY,CAAC;AAAA,MACrC;AAGA,YAAM,WAAW,SAAS,MAAM,QAAQ,IAAI,MAAM,IAAI,cAAc,EAAE,IAAI,YAAY,EAAE;AACxF,YAAM,SAAS,MAAM,EAAE,IAAI,MAAM,IAAI,QAAQ;AAE7C,UAAI,QAAQ;AACV,eAAgB,QAAQ,GAAG,kEAAgB,KAAK,MAAM,MAAM,CAAC;AAAA,MAC/D;AAGA,UAAI,cAAc;AAClB,UAAI,MAAM,SAAS,SAAS;AAC1B,sBAAc;AACd,eAAO,QAAQ,MAAM,QAAQ;AAAA,MAC/B;AAGA,YAAM,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMpB,WAAW;AAAA,UACX,cAAc,QAAQ,OAAO;AAAA,UAC7B,UAAU;AAAA;AAGd,YAAM,aAAa,MAAM,EAAE,IAAI,GAAG,QAAQ,eAAe,EAAE,KAAK,GAAG,MAAM,EAAE,MAAM;AAGjF,YAAM,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQtB,WAAW;AAAA,UACX,cAAc,QAAQ,OAAO;AAAA,UAC7B,UAAU;AAAA;AAAA;AAAA;AAKd,YAAM,eAAe,MAAM,EAAE,IAAI,GAAG,QAAQ,iBAAiB,EAAE,KAAK,GAAG,MAAM,EAAE,IAAI;AAGnF,YAAM,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOf,WAAW;AAAA,UACX,cAAc,QAAQ,OAAO;AAAA;AAAA;AAAA;AAAA;AAMjC,YAAM,cAAc,MAAM,SAAS,UAAU,CAAC,IAAI,CAAC,MAAM,QAAQ;AACjE,YAAM,aAAa,MAAM,EAAE,IAAI,GAAG,QAAQ,UAAU,EAAE,KAAK,GAAG,WAAW,EAAE,IAAI;AAE/E,YAAM,QAAQ;AAAA,QACZ,SAAS;AAAA,UACP,cAAe,YAAY,gBAA2B;AAAA,UACtD,gBAAiB,YAAY,kBAA6B;AAAA,UAC1D,eAAgB,YAAY,iBAA4B;AAAA,UACxD;AAAA,QACF;AAAA,QACA,YAAY,aAAa;AAAA,QACzB,OAAO,WAAW;AAAA,MACpB;AAGA,YAAM,EAAE,IAAI,MAAM,IAAI,UAAU,KAAK,UAAU,KAAK,GAAG,EAAE,eAAe,IAAI,CAAC;AAE7E,aAAgB,QAAQ,GAAG,oDAAY,KAAK;AAAA,IAC9C,SAASC,QAAO;AACd,cAAQ,MAAM,0BAA0BA,MAAK;AAC7C,aAAgB,MAAM,GAAG,yBAAyB,yBAAyB,GAAG;AAAA,IAChF;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,IAAI,2BAA2B,eAAe,GAAG,OAAO,MAAM;AAChE,QAAI;AACF,YAAM,QAAQ,EAAE,IAAI,OAAO;AAC3B,YAAM,QAAQ,EAAE,IAAI,MAAM;AAC1B,YAAM,SAAS,MAAM,UAAU;AAC/B,YAAM,aAAa,MAAM;AACzB,YAAM,WAAW,MAAM;AAGvB,UAAI,aAAa;AACjB,YAAM,SAAgB,CAAC;AAEvB,UAAI,cAAc,UAAU;AAC1B,qBAAa;AACb,eAAO,KAAK,YAAY,QAAQ;AAAA,MAClC,OAAO;AAEL,cAAM,MAAM,oBAAI,KAAK;AACrB,YAAI;AAEJ,gBAAQ,QAAQ;AAAA,UACd,KAAK;AACH,wBAAY,IAAI,KAAK,IAAI,YAAY,GAAG,IAAI,SAAS,GAAG,IAAI,QAAQ,CAAC;AACrE;AAAA,UACF,KAAK;AACH,wBAAY,IAAI,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK,KAAK,KAAK,GAAI;AAC5D;AAAA,UACF,KAAK;AACH,wBAAY,IAAI,KAAK,IAAI,YAAY,GAAG,IAAI,SAAS,GAAG,CAAC;AACzD;AAAA,UACF,KAAK;AACH,wBAAY,IAAI,KAAK,IAAI,YAAY,GAAG,GAAG,CAAC;AAC5C;AAAA,UACF;AACE,wBAAY,IAAI,KAAK,IAAI,YAAY,GAAG,IAAI,SAAS,GAAG,CAAC;AAAA,QAC7D;AAEA,qBAAa;AACb,eAAO,KAAK,UAAU,YAAY,CAAC;AAAA,MACrC;AAGA,UAAI,gBAAgB;AACpB,UAAI,MAAM,SAAS,SAAS;AAC1B,cAAM,YAAY,MAAM,EAAE,IAAI,MAAM,IAAI,cAAc,MAAM,QAAQ,EAAE;AACtE,YAAI,WAAW;AACb,gBAAM,EAAE,YAAY,IAAI,KAAK,MAAM,SAAS;AAC5C,cAAI,eAAe,YAAY,SAAS,GAAG;AACzC,4BAAgB,wBAAwB,YAAY,IAAI,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC;AAC5E,mBAAO,KAAK,GAAG,WAAW;AAAA,UAC5B,OAAO;AAEL,mBAAgB,QAAQ,GAAG,8CAAW;AAAA,cACpC,SAAS,EAAE,qBAAqB,GAAG,0BAA0B,GAAG,sBAAsB,GAAG,cAAc,EAAE;AAAA,cACzG,WAAW,CAAC;AAAA,cACZ,OAAO,CAAC;AAAA,YACV,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAGA,YAAM,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQf,aAAa;AAAA,UACb,UAAU;AAAA;AAGd,YAAM,aAAa,MAAM,EAAE,IAAI,GAAG,QAAQ,UAAU,EAAE,KAAK,GAAG,MAAM,EAAE,MAAM;AAG5E,YAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOhB,aAAa;AAAA,UACb,UAAU;AAAA;AAAA;AAId,YAAM,cAAc,MAAM,EAAE,IAAI,GAAG,QAAQ,WAAW,EAAE,KAAK,GAAG,MAAM,EAAE,IAAI;AAG5E,YAAM,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQf,gBAAgB,cAAc,QAAQ,OAAO,KAAK,IAAI,EAAE;AAAA;AAAA;AAAA;AAK5D,YAAM,cAAc,MAAM,SAAS,UAAU,CAAC,IAAI,OAAO,MAAM,CAAC;AAChE,YAAM,oBAAoB,MAAM,EAAE,IAAI,GAAG,QAAQ,UAAU,EAAE,KAAK,GAAG,WAAW,EAAE,IAAI;AAEtF,YAAM,0BAA2B,YAAY,4BAAuC;AACpF,YAAM,qBAAsB,YAAY,uBAAkC;AAC1E,YAAM,cAAc,qBAAqB,IAAK,0BAA0B,qBAAsB,MAAM;AAEpG,YAAM,QAAQ;AAAA,QACZ,SAAS;AAAA,UACP,qBAAqB;AAAA,UACrB,0BAA0B;AAAA,UAC1B,sBAAuB,YAAY,wBAAmC;AAAA,UACtE,cAAc,KAAK,MAAM,cAAc,GAAG,IAAI;AAAA,UAC9C;AAAA,QACF;AAAA,QACA,WAAW,YAAY;AAAA,QACvB,OAAO,kBAAkB;AAAA,MAC3B;AAEA,aAAgB,QAAQ,GAAG,oDAAY,KAAK;AAAA,IAC9C,SAASC,QAAO;AACd,cAAQ,MAAM,iCAAiCA,MAAK;AACpD,aAAgB,MAAM,GAAG,yBAAyB,gCAAgC,GAAG;AAAA,IACvF;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,IAAI,wBAAwB,eAAe,GAAG,OAAO,MAAM;AAC7D,QAAI;AACF,YAAM,QAAQ,EAAE,IAAI,OAAO;AAG3B,UAAI,gBAAgB;AACpB,UAAI,cAAc;AAClB,YAAM,SAAgB,CAAC;AAEvB,UAAI,MAAM,SAAS,SAAS;AAC1B,sBAAc;AACd,eAAO,KAAK,MAAM,QAAQ;AAE1B,cAAM,YAAY,MAAM,EAAE,IAAI,MAAM,IAAI,cAAc,MAAM,QAAQ,EAAE;AACtE,YAAI,WAAW;AACb,gBAAM,EAAE,YAAY,IAAI,KAAK,MAAM,SAAS;AAC5C,cAAI,eAAe,YAAY,SAAS,GAAG;AACzC,4BAAgB,sBAAsB,YAAY,IAAI,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC;AAC1E,mBAAO,KAAK,GAAG,WAAW;AAAA,UAC5B;AAAA,QACF;AAAA,MACF;AAGA,YAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOjB,WAAW;AAAA,UACX,aAAa;AAAA;AAGjB,YAAM,eAAe,MAAM,EAAE,IAAI,GAAG,QAAQ,YAAY,EAAE,KAAK,GAAG,MAAM,EAAE,MAAM;AAGhF,YAAM,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMf,WAAW;AAAA,UACX,cAAc,QAAQ,OAAO;AAAA;AAAA;AAIjC,YAAM,aAAa,MAAM,EAAE,IAAI,GAAG,QAAQ,UAAU,EAAE,KAAK,GAAG,OAAO,MAAM,GAAG,MAAM,SAAS,UAAU,IAAI,CAAC,CAAC,EAAE,MAAM;AAGrH,YAAM,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,UAKtB,MAAM,SAAS,UAAU,KAAK,mDAAmD;AAAA;AAAA,UAEjF,MAAM,SAAS,UAAU,KAAK,aAAa;AAAA;AAG/C,YAAM,qBAAqB,MAAM,SAAS,UAAU,CAAC,IAAK,gBAAgB,OAAO,MAAM,CAAC,IAAI,CAAC;AAC7F,YAAM,oBAAoB,MAAM,EAAE,IAAI,GAAG,QAAQ,iBAAiB,EAAE,KAAK,GAAG,kBAAkB,EAAE,MAAM;AAGtG,YAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,UAKhB,MAAM,SAAS,UAAU,KAAK,wBAAwB,OAAO,MAAM,CAAC,EAAE,IAAI,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC,GAAG;AAAA;AAGrG,YAAM,eAAe,MAAM,SAAS,UAAU,CAAC,IAAI,OAAO,MAAM,CAAC;AACjE,YAAM,cAAc,MAAM,EAAE,IAAI,GAAG,QAAQ,WAAW,EAAE,KAAK,GAAG,YAAY,EAAE,MAAM;AAEpF,YAAM,gBAAgB;AAAA,QACpB,UAAU;AAAA,UACR,OAAQ,cAAc,kBAA6B;AAAA,UACnD,QAAS,cAAc,mBAA8B;AAAA,UACrD,SAAU,cAAc,oBAA+B;AAAA,UACvD,SAAU,cAAc,oBAA+B;AAAA,QACzD;AAAA,QACA,mBAAmB;AAAA,UACjB,OAAQ,YAAY,gBAA2B;AAAA,UAC/C,SAAU,YAAY,iBAA4B;AAAA,UAClD,eAAgB,YAAY,uBAAkC;AAAA,QAChE;AAAA,QACA,qBAAqB;AAAA,UACnB,OAAQ,mBAAmB,uBAAkC;AAAA,UAC7D,YAAa,mBAAmB,4BAAuC;AAAA,QACzE;AAAA,QACA,qBAAsB,aAAa,kBAA6B;AAAA,MAClE;AAEA,aAAgB,QAAQ,GAAG,0DAAa,aAAa;AAAA,IACvD,SAASC,QAAO;AACd,cAAQ,MAAM,8BAA8BA,MAAK;AACjD,aAAgB,MAAM,GAAG,yBAAyB,6BAA6B,GAAG;AAAA,IACpF;AAAA,EACF,CAAC;AACH;AA5WgB;;;ACLhB;;;ACAA;AAkBA,eAAe,kBAAkB,IAAgB;AAC/C,UAAQ,IAAI,iEAAe;AAE3B,MAAI;AAEF,UAAM,qBAAqB,MAAM,GAAG;AAAA,MAClC;AAAA,IACF,EAAE,KAAK,MAAM,EAAE,MAAM;AAErB,QAAI,CAAC,oBAAoB;AAEvB,YAAM,yBAAyB,MAAa,KAAK,YAAY,EAAE;AAE/D,YAAM,GAAG,QAAQ;AAAA;AAAA;AAAA,OAGhB,EAAE,KAAK,QAAQ,sBAAsB,EAAE,IAAI;AAE5C,cAAQ,IAAI,8HAAyC;AAAA,IACvD,OAAO;AACL,cAAQ,IAAI,yGAAoB;AAAA,IAClC;AAGA,UAAM,sBAAsB,MAAM,GAAG;AAAA,MACnC;AAAA,IACF,EAAE,KAAK,OAAO,EAAE,MAAM;AAEtB,QAAI,CAAC,qBAAqB;AAExB,YAAM,0BAA0B,MAAa,KAAK,YAAY,EAAE;AAChE,YAAM,mBAAmB,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC;AAE9C,YAAM,GAAG,QAAQ;AAAA;AAAA;AAAA,OAGhB,EAAE,KAAK,SAAS,yBAAyB,gBAAgB,EAAE,IAAI;AAEhE,cAAQ,IAAI,8JAAqD;AAAA,IACnE,OAAO;AACL,cAAQ,IAAI,yGAAoB;AAAA,IAClC;AAAA,EAEF,SAASC,QAAO;AACd,YAAQ,MAAM,kEAAgBA,MAAK;AACnC,UAAMA;AAAA,EACR;AACF;AA/Ce;AAoDf,eAAe,qBAAqB,IAAgB;AAClD,UAAQ,IAAI,uEAAgB;AAE5B,MAAI;AAEF,UAAM,aAAa,MAAM,GAAG,QAAQ,0CAA0C,EAAE,KAAK,MAAM,EAAE,MAAM;AACnG,UAAM,cAAc,MAAM,GAAG,QAAQ,0CAA0C,EAAE,KAAK,OAAO,EAAE,MAAM;AAErG,QAAI,CAAC,cAAc,CAAC,aAAa;AAC/B,YAAM,IAAI,MAAM,oHAAqB;AAAA,IACvC;AAGA,UAAM,iBAAiB;AAAA,MACrB;AAAA,QACE,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,YAAY,IAAI,KAAK,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA;AAAA,QACzE,UAAU,WAAW;AAAA,MACvB;AAAA,MACA;AAAA,QACE,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,YAAY,IAAI,KAAK,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACzE,UAAU,YAAY;AAAA,MACxB;AAAA,MACA;AAAA,QACE,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,YAAY,IAAI,KAAK,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACzE,UAAU,WAAW;AAAA,MACvB;AAAA,IACF;AAEA,eAAW,WAAW,gBAAgB;AAEpC,YAAM,WAAW,MAAM,GAAG,QAAQ,+CAA+C,EAC9E,KAAK,QAAQ,WAAW,EAAE,MAAM;AAEnC,UAAI,CAAC,UAAU;AACb,cAAM,GAAG,QAAQ;AAAA;AAAA;AAAA,SAGhB,EAAE;AAAA,UACD,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV,EAAE,IAAI;AAEN,gBAAQ,IAAI,kEAAgB,QAAQ,WAAW,EAAE;AAAA,MACnD,OAAO;AACL,gBAAQ,IAAI,oFAAmB,QAAQ,WAAW,EAAE;AAAA,MACtD;AAAA,IACF;AAAA,EAEF,SAASA,QAAO;AACd,YAAQ,MAAM,kEAAgBA,MAAK;AACnC,UAAMA;AAAA,EACR;AACF;AA5De;AAiEf,eAAe,mBAAmB,IAAgB;AAChD,UAAQ,IAAI,iEAAe;AAE3B,MAAI;AACF,UAAM,cAAc,MAAM,GAAG,QAAQ,0CAA0C,EAAE,KAAK,OAAO,EAAE,MAAM;AAErG,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,8DAAY;AAAA,IAC9B;AAEA,UAAM,eAAe;AAAA,MACnB;AAAA,QACE,UAAU,YAAY;AAAA,QACtB,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,UAAU,YAAY;AAAA,QACtB,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,QAAQ;AAAA,MACV;AAAA,IACF;AAEA,eAAW,SAAS,cAAc;AAChC,YAAM,GAAG,QAAQ;AAAA;AAAA;AAAA,OAGhB,EAAE;AAAA,QACD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACR,EAAE,IAAI;AAAA,IACR;AAEA,YAAQ,IAAI,qEAAc;AAAA,EAE5B,SAASA,QAAO;AACd,YAAQ,MAAM,4DAAeA,MAAK;AAClC,UAAMA;AAAA,EACR;AACF;AAjDe;AAsDf,eAAsB,mBAAmB,KAAyB;AAChE,UAAQ,IAAI,+DAAgB;AAE5B,MAAI;AAEF,UAAM,kBAAkB,IAAI,EAAE;AAG9B,UAAM,qBAAqB,IAAI,EAAE;AAGjC,UAAM,mBAAmB,IAAI,EAAE;AAE/B,YAAQ,IAAI,kEAAc;AAC1B,YAAQ,IAAI,EAAE;AACd,YAAQ,IAAI,gCAAO;AACnB,YAAQ,IAAI,mFAAiC;AAC7C,YAAQ,IAAI,oFAAkC;AAC9C,YAAQ,IAAI,EAAE;AAAA,EAEhB,SAASA,QAAO;AACd,YAAQ,MAAM,+DAAgBA,MAAK;AACnC,UAAMA;AAAA,EACR;AACF;AAxBsB;;;ADxLf,SAAS,gBAAgBC,MAA6C;AAE3E,EAAAA,KAAI,KAAK,YAAY,OAAO,MAAM;AAChC,QAAI;AAEF,YAAM,qBAAqB,MAAM,EAAE,IAAI,GAAG;AAAA,QACxC;AAAA,MACF,EAAE,KAAK,OAAO,EAAE,MAAM;AAEtB,UAAI,oBAAoB;AACtB,eAAgB,MAAM,GAAG,wFAAkB,gCAAgC,GAAG;AAAA,MAChF;AAGA,YAAM,mBAAmB,EAAE,GAAG;AAE9B,aAAgB,QAAQ,GAAG,oDAAY;AAAA,QACrC,SAAS;AAAA,QACT,UAAU;AAAA,UACR,EAAE,UAAU,QAAQ,UAAU,YAAY,MAAM,QAAQ;AAAA,UACxD,EAAE,UAAU,SAAS,UAAU,YAAY,MAAM,SAAS;AAAA,QAC5D;AAAA,MACF,CAAC;AAAA,IAEH,SAASC,QAAO;AACd,cAAQ,MAAM,kCAAkCA,MAAK;AACrD,aAAgB,MAAM,GAAG,oDAAY,uBAAuB,GAAG;AAAA,IACjE;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,KAAK,aAAa,OAAO,MAAM;AACjC,QAAI;AAEF,YAAM,EAAE,IAAI,GAAG,QAAQ,+BAA+B,EAAE,IAAI;AAC5D,YAAM,EAAE,IAAI,GAAG,QAAQ,qBAAqB,EAAE,IAAI;AAClD,YAAM,EAAE,IAAI,GAAG,QAAQ,oBAAoB,EAAE,IAAI;AACjD,YAAM,EAAE,IAAI,GAAG,QAAQ,sBAAsB,EAAE,IAAI;AACnD,YAAM,EAAE,IAAI,GAAG,QAAQ,oBAAoB,EAAE,IAAI;AACjD,YAAM,EAAE,IAAI,GAAG,QAAQ,sBAAsB,EAAE,IAAI;AAGnD,YAAM,mBAAmB,EAAE,GAAG;AAE9B,aAAgB,QAAQ,GAAG,8CAAW;AAAA,QACpC,SAAS;AAAA,MACX,CAAC;AAAA,IAEH,SAASC,QAAO;AACd,cAAQ,MAAM,yBAAyBA,MAAK;AAC5C,aAAgB,MAAM,GAAG,8CAAW,wBAAwB,GAAG;AAAA,IACjE;AAAA,EACF,CAAC;AAGD,EAAAD,KAAI,IAAI,cAAc,OAAO,MAAM;AACjC,QAAI;AACF,YAAM,aAAa,MAAM,EAAE,IAAI,GAAG,QAAQ,sCAAsC,EAAE,MAAM;AACxF,YAAM,eAAe,MAAM,EAAE,IAAI,GAAG,QAAQ,wCAAwC,EAAE,MAAM;AAC5F,YAAM,eAAe,MAAM,EAAE,IAAI,GAAG,QAAQ,wCAAwC,EAAE,MAAM;AAE5F,aAAgB,QAAQ,GAAG,0DAAa;AAAA,QACtC,QAAS,YAAY,SAAoB;AAAA,QACzC,UAAW,cAAc,SAAoB;AAAA,QAC7C,UAAW,cAAc,SAAoB;AAAA,QAC7C,cAAe,YAAY,SAAoB,KAAK;AAAA,MACtD,CAAC;AAAA,IAEH,SAASC,QAAO;AACd,cAAQ,MAAM,gCAAgCA,MAAK;AACnD,aAAgB,MAAM,GAAG,0DAAa,mBAAmB,GAAG;AAAA,IAC9D;AAAA,EACF,CAAC;AACH;AAzEgB;;;AhDOhB,IAAM,MAAM,IAAIC,MAAuC;AAGvD,IAAI,IAAI,KAAK,eAAe,CAAC;AAC7B,IAAI,IAAI,KAAK,mBAAmB,CAAC;AAGjC,IAAI,IAAI,KAAK,CAAC,MAAM;AAClB,SAAO,EAAE,KAAK;AAAA,IACZ,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS,EAAE,IAAI,eAAe;AAAA,IAC9B,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,EACpC,CAAC;AACH,CAAC;AAGD,gBAAgB,GAAG;AACnB,iBAAiB,GAAG;AACpB,mBAAmB,GAAG;AACtB,kBAAkB,GAAG;AACrB,iBAAiB,GAAG;AACpB,mBAAmB,GAAG;AACtB,sBAAsB,GAAG;AACzB,gBAAgB,GAAG;AAEnB,IAAO,cAAQ;;;AkDtCf;AAEA,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAG;AACX,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACjBf;AASA,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAQ;AAChB,UAAMC,SAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAKA,QAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ApDzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;AqDVnB;AAwBA,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AtD3ChB,IAAM,iCAAN,MAAM,gCAA8D;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EArBD,OAYoE;AAAA;AAAA;AAAA,EAC1D;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,kCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAEA,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,wBACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B,GAXyE;AAAA,IAazE,cAA0B,wBAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD,GAT0B;AAAA,IAW1B,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": ["raw", "app", "Node", "Node", "<PERSON><PERSON>", "verify", "decode", "sign", "sign", "verify", "nodeCrypto", "callback", "callback", "salt", "callback", "err", "app", "error", "app", "error", "id", "app", "error", "app", "error", "app", "error", "app", "error", "app", "error", "error", "app", "error", "<PERSON><PERSON>", "error"]}