/**
 * API接口测试脚本
 * 用于测试许可证验证系统的各个API端点
 */

const baseUrl = 'http://localhost:8787';
let accessToken = '';
let createdProductId = null;
let createdAdminId = null;
let createdOrderId = null;
let createdLicenses = [];

// 工具函数：发送HTTP请求
async function makeRequest(method, url, data = null, headers = {}) {
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  console.log(`\n🔄 ${method} ${url}`);
  console.log('📤 Request Headers:', JSON.stringify(options.headers, null, 2));
  if (data) {
    console.log('📤 Request Body:', JSON.stringify(data, null, 2));
  }

  try {
    const response = await fetch(url, options);
    const responseData = await response.text();
    
    let parsedData;
    try {
      parsedData = JSON.parse(responseData);
    } catch {
      parsedData = responseData;
    }

    console.log(`📥 Response Status: ${response.status} ${response.statusText}`);
    console.log('📥 Response Headers:', JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2));
    console.log('📥 Response Body:', typeof parsedData === 'string' ? parsedData : JSON.stringify(parsedData, null, 2));
    
    return {
      status: response.status,
      ok: response.ok,
      data: parsedData
    };
  } catch (error) {
    console.log('❌ Request Error:', error.message);
    return {
      status: 0,
      ok: false,
      error: error.message
    };
  }
}

// 测试函数
async function testAPI() {
  console.log('🚀 开始API接口测试...\n');

  // 1. 健康检查
  console.log('=' .repeat(60));
  console.log('1. 健康检查');
  console.log('=' .repeat(60));
  const healthCheck = await makeRequest('GET', `${baseUrl}/`);

  // 2. 数据库初始化
  console.log('=' .repeat(60));
  console.log('2. 数据库初始化');
  console.log('=' .repeat(60));
  const initDb = await makeRequest('POST', `${baseUrl}/init-db`);

  // 3. 管理员登录
  console.log('=' .repeat(60));
  console.log('3. 管理员登录');
  console.log('=' .repeat(60));
  const loginResponse = await makeRequest('POST', `${baseUrl}/api/auth/login`, {
    username: 'root',
    password: 'password'
  });

  if (loginResponse.ok && loginResponse.data?.data?.token) {
    accessToken = loginResponse.data.data.token;
    console.log('✅ 登录成功，获取到token');
  } else {
    console.log('❌ 登录失败，无法获取token');
    return;
  }

  // 4. 获取所有产品
  console.log('=' .repeat(60));
  console.log('4. 获取所有产品');
  console.log('=' .repeat(60));
  const getProducts = await makeRequest('GET', `${baseUrl}/api/products?page=1&limit=20`, null, {
    'Authorization': `Bearer ${accessToken}`
  });

  // 5. 创建产品
  console.log('=' .repeat(60));
  console.log('5. 创建产品');
  console.log('=' .repeat(60));
  const createProduct = await makeRequest('POST', `${baseUrl}/api/products`, {
    name: 'Test Software v1.0',
    description: 'API测试产品',
    verification_strategy: 'device_count',
    max_devices: 3,
    features: ['feature1', 'feature2', 'premium']
  }, {
    'Authorization': `Bearer ${accessToken}`
  });

  if (createProduct.ok && createProduct.data?.data?.id) {
    createdProductId = createProduct.data.data.id;
    console.log('✅ 产品创建成功，ID:', createdProductId);
  }

  // 6. 获取产品详情
  if (createdProductId) {
    console.log('=' .repeat(60));
    console.log('6. 获取产品详情');
    console.log('=' .repeat(60));
    const getProduct = await makeRequest('GET', `${baseUrl}/api/products/${createdProductId}`, null, {
      'Authorization': `Bearer ${accessToken}`
    });
  }

  // 7. 创建管理员
  console.log('=' .repeat(60));
  console.log('7. 创建管理员');
  console.log('=' .repeat(60));
  const createAdmin = await makeRequest('POST', `${baseUrl}/api/admins`, {
    username: 'testadmin',
    password: 'testpassword',
    role: 'normal',
    product_ids: createdProductId ? [createdProductId] : [1]
  }, {
    'Authorization': `Bearer ${accessToken}`
  });

  if (createAdmin.ok && createAdmin.data?.data?.id) {
    createdAdminId = createAdmin.data.data.id;
    console.log('✅ 管理员创建成功，ID:', createdAdminId);
  }

  // 8. 创建许可证
  console.log('=' .repeat(60));
  console.log('8. 创建许可证');
  console.log('=' .repeat(60));
  const createLicenses = await makeRequest('POST', `${baseUrl}/api/v1/licenses`, {
    product_id: createdProductId || 1,
    count: 3,
    expires_at: '2025-12-31T23:59:59.000Z',
    max_devices: 2
  }, {
    'Authorization': `Bearer ${accessToken}`
  });

  if (createLicenses.ok && createLicenses.data?.data?.licenses) {
    createdLicenses = createLicenses.data.data.licenses;
    console.log('✅ 许可证创建成功，数量:', createdLicenses.length);
  }

  // 9. 获取许可证列表
  console.log('=' .repeat(60));
  console.log('9. 获取许可证列表');
  console.log('=' .repeat(60));
  const getLicenses = await makeRequest('GET', `${baseUrl}/api/v1/licenses?page=1&limit=20`, null, {
    'Authorization': `Bearer ${accessToken}`
  });

  // 10. 创建订单
  console.log('=' .repeat(60));
  console.log('10. 创建订单');
  console.log('=' .repeat(60));
  const createOrder = await makeRequest('POST', `${baseUrl}/api/v1/orders`, {
    product_id: createdProductId || 1,
    license_count: 5,
    unit_price: 99.99
  }, {
    'Authorization': `Bearer ${accessToken}`
  });

  if (createOrder.ok && createOrder.data?.data?.id) {
    createdOrderId = createOrder.data.data.id;
    console.log('✅ 订单创建成功，ID:', createdOrderId);
  }

  // 11. 许可证验证（公开接口）
  if (createdLicenses.length > 0) {
    console.log('=' .repeat(60));
    console.log('11. 许可证验证');
    console.log('=' .repeat(60));
    const verifyLicense = await makeRequest('POST', `${baseUrl}/verify`, {
      license_key: createdLicenses[0],
      device_id: 'test-device-001',
      product_features: ['feature1', 'premium']
    });
  }

  // 12. 获取统计信息
  console.log('=' .repeat(60));
  console.log('12. 获取仪表板统计');
  console.log('=' .repeat(60));
  const getDashboard = await makeRequest('GET', `${baseUrl}/api/v1/stats/dashboard`, null, {
    'Authorization': `Bearer ${accessToken}`
  });

  // 13. Token刷新测试
  console.log('=' .repeat(60));
  console.log('13. Token刷新测试');
  console.log('=' .repeat(60));
  const refreshToken = await makeRequest('POST', `${baseUrl}/api/auth/refresh`, null, {
    'Authorization': `Bearer ${accessToken}`
  });

  console.log('\n🎉 API测试完成！');
  console.log('\n📊 测试摘要:');
  console.log(`- 创建的产品ID: ${createdProductId}`);
  console.log(`- 创建的管理员ID: ${createdAdminId}`);
  console.log(`- 创建的订单ID: ${createdOrderId}`);
  console.log(`- 创建的许可证数量: ${createdLicenses.length}`);
  if (createdLicenses.length > 0) {
    console.log(`- 第一个许可证: ${createdLicenses[0]}`);
  }
}

// 运行测试
testAPI().catch(console.error);