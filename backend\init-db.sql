-- 许可证验证系统数据库初始化脚本
-- 创建时间：2025-07-31

-- 1. 管理员表
CREATE TABLE IF NOT EXISTS admins (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('super', 'normal')),
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    product_ids TEXT, -- JSON 数组，存储分配的产品ID
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 2. 产品表
CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    verification_strategy TEXT NOT NULL DEFAULT 'device_count' CHECK (verification_strategy IN ('device_count', 'expiration', 'feature_based')),
    max_devices INTEGER DEFAULT 1,
    features TEXT, -- JSON 数组，存储产品功能
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 3. 许可证表
CREATE TABLE IF NOT EXISTS licenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,
    license_key TEXT NOT NULL UNIQUE,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'expired', 'revoked')),
    expires_at DATETIME,
    max_devices INTEGER,
    features TEXT, -- JSON 数组，覆盖产品默认功能
    admin_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (admin_id) REFERENCES admins(id)
);

-- 4. 设备绑定表
CREATE TABLE IF NOT EXISTS devices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_id INTEGER NOT NULL,
    device_id TEXT NOT NULL,
    device_info TEXT, -- JSON 格式，存储设备信息
    last_verification DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE,
    UNIQUE(license_id, device_id)
);

-- 5. 验证日志表
CREATE TABLE IF NOT EXISTS verification_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_key TEXT NOT NULL,
    device_id TEXT,
    result TEXT NOT NULL CHECK (result IN ('success', 'failed')),
    reason TEXT,
    ip_address TEXT,
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 6. 订单表
CREATE TABLE IF NOT EXISTS orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    admin_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    license_count INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_licenses_key ON licenses(license_key);
CREATE INDEX IF NOT EXISTS idx_licenses_product ON licenses(product_id);
CREATE INDEX IF NOT EXISTS idx_licenses_admin ON licenses(admin_id);
CREATE INDEX IF NOT EXISTS idx_devices_license ON devices(license_id);
CREATE INDEX IF NOT EXISTS idx_verification_logs_key ON verification_logs(license_key);
CREATE INDEX IF NOT EXISTS idx_verification_logs_created ON verification_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_admin ON orders(admin_id);
CREATE INDEX IF NOT EXISTS idx_orders_product ON orders(product_id);
CREATE INDEX IF NOT EXISTS idx_orders_created ON orders(created_at);

-- 插入示例数据

-- 插入产品数据
INSERT OR IGNORE INTO products (id, name, description, verification_strategy, max_devices, features) VALUES
(1, '基础版软件', '提供基本功能的软件产品', 'device_count', 1, '["basic_features", "email_support"]'),
(2, '专业版软件', '提供高级功能的专业软件', 'device_count', 3, '["basic_features", "advanced_features", "priority_support", "api_access"]'),
(3, '企业版软件', '面向企业的完整解决方案', 'device_count', 10, '["basic_features", "advanced_features", "enterprise_features", "24x7_support", "api_access", "custom_integration"]');

-- 插入管理员账户（需要在应用启动后通过代码插入，因为需要密码哈希）
-- 超级管理员：root / password
-- 普通管理员：admin / password (分配产品1,2)