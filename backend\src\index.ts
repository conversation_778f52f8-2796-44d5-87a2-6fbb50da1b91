import { Hono } from 'hono';
import { CloudflareBindings } from './types/interfaces';
import { corsMiddleware, bindingsMiddleware } from './middleware/common';
import { setupAuthRoutes } from './routes/auth';
import { setupAdminRoutes } from './routes/admins';
import { setupLicenseRoutes } from './routes/licenses';
import { setupClientRoutes } from './routes/client';
import { setupOrderRoutes } from './routes/orders';
import { setupProductRoutes } from './routes/products';
import { setupStatisticsRoutes } from './routes/statistics';
import { setupInitRoutes } from './routes/init';

const app = new Hono<{ Bindings: CloudflareBindings }>();

// 全局中间件
app.use('*', corsMiddleware());
app.use('*', bindingsMiddleware());

// 健康检查
app.get('/', (c) => {
  return c.json({
    success: true,
    message: 'License Verification Service API',
    version: c.env.API_VERSION || 'v1',
    timestamp: new Date().toISOString(),
  });
});

// 设置路由
setupAuthRoutes(app);
setupAdminRoutes(app);
setupLicenseRoutes(app);
setupClientRoutes(app);
setupOrderRoutes(app);
setupProductRoutes(app);
setupStatisticsRoutes(app);
setupInitRoutes(app);

export default app;