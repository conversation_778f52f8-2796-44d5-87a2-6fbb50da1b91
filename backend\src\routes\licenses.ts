import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { authMiddleware } from '../middleware/auth';
import { generateRandomKey } from '../utils/auth';
import * as response from '../utils/response';

export function setupLicenseRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // Get licenses
  app.get('/api/v1/licenses', authMiddleware(), async (c) => {
    try {
      const admin = c.get('admin');
      const query = c.req.query();
      const page = parseInt(query.page || '1');
      const limit = parseInt(query.limit || '20');
      const product_id = query.product_id ? parseInt(query.product_id) : undefined;
      const status = query.status;
      const search = query.search;
      const offset = (page - 1) * limit;
      
      let queryStr = `
        SELECT l.*, p.name as product_name 
        FROM licenses l 
        JOIN products p ON l.product_id = p.id
      `;
      const params: any[] = [];
      const conditions: string[] = [];
      
      // Normal admins can only see licenses for their assigned products
      if (admin.role !== 'super') {
        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);
        if (adminInfo) {
          const { product_ids } = JSON.parse(adminInfo);
          if (product_ids && product_ids.length > 0) {
            conditions.push(`l.product_id IN (${product_ids.map(() => '?').join(',')})`)
            params.push(...product_ids);
          } else {
            // Admin has no assigned products
            return response.success(c, '未找到许可证', {
              licenses: [],
              pagination: { page, limit, total: 0, totalPages: 0 },
            });
          }
        }
      }
      
      if (product_id) {
        conditions.push('l.product_id = ?');
        params.push(product_id);
      }
      
      if (status) {
        conditions.push('l.status = ?');
        params.push(status);
      }
      
      if (search) {
        conditions.push('l.license_key LIKE ?');
        params.push(`%${search}%`);
      }
      
      if (conditions.length > 0) {
        queryStr += ' WHERE ' + conditions.join(' AND ');
      }
      
      queryStr += ' ORDER BY l.created_at DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);
      
      const licenses = await c.env.DB.prepare(queryStr).bind(...params).all();
      
      // Get total count
      let countQuery = 'SELECT COUNT(*) as total FROM licenses l';
      if (conditions.length > 0) {
        countQuery += ' WHERE ' + conditions.join(' AND ');
      }
      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();
      
      return response.success(c, '许可证列表获取成功', {
        licenses: licenses.results,
        pagination: {
          page,
          limit,
          total: (countResult?.total as number) || 0,
          totalPages: Math.ceil(((countResult?.total as number) || 0) / limit),
        },
      });
    } catch (error) {
      console.error('Get licenses error:', error);
      return response.error(c, 'Internal server error', 'GET_LICENSES_ERROR', 500);
    }
  });

  // Create licenses
  app.post('/api/v1/licenses', authMiddleware(), async (c) => {
    try {
      const admin = c.get('admin');
      const body = await c.req.json();
      const { product_id, count, expires_at, max_devices } = body;
      
      if (!product_id || !count) {
        return response.error(c, '产品ID和数量不能为空', 'MISSING_REQUIRED_FIELDS', 400);
      }
      
      if (count <= 0 || count > 1000) {
        return response.error(c, '数量必须在1到1000之间', 'INVALID_COUNT', 400);
      }
      
      // Check if product exists and admin has access
      const product = await c.env.DB.prepare('SELECT * FROM products WHERE id = ? AND status = \'active\'').bind(product_id).first();
      if (!product) {
        return response.error(c, 'Product not found or inactive', 'PRODUCT_NOT_FOUND', 400);
      }
      
      // Check admin permissions
      if (admin.role !== 'super') {
        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);
        if (adminInfo) {
          const { product_ids } = JSON.parse(adminInfo);
          if (!product_ids || !product_ids.includes(product_id.toString())) {
            return response.error(c, 'Access denied to this product', 'PRODUCT_ACCESS_DENIED', 403);
          }
        }
      }
      
      // Generate licenses
      const licenses: string[] = [];
      const insertPromises: Promise<any>[] = [];
      
      for (let i = 0; i < count; i++) {
        const licenseKey = generateRandomKey(24);
        licenses.push(licenseKey);
        
        insertPromises.push(
          c.env.DB.prepare(`
            INSERT INTO licenses (product_id, license_key, status, expires_at, max_devices, admin_id, created_at, updated_at)
            VALUES (?, ?, 'active', ?, ?, ?, datetime('now'), datetime('now'))
          `).bind(product_id, licenseKey, expires_at || null, max_devices || null, admin.admin_id).run()
        );
      }
      
      await Promise.all(insertPromises);
      
      // Create order record
      const unitPrice = 10.0; // Default price, should be configurable
      const totalPrice = unitPrice * count;
      
      await c.env.DB.prepare(`
        INSERT INTO orders (admin_id, product_id, license_count, unit_price, total_price, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, 'completed', datetime('now'), datetime('now'))
      `).bind(admin.admin_id, product_id, count, unitPrice, totalPrice).run();
      
      return response.success(c, `成功创建${count}个许可证`, { licenses }, 201);
    } catch (error) {
      console.error('Create licenses error:', error);
      return response.error(c, 'Internal server error', 'CREATE_LICENSES_ERROR', 500);
    }
  });

  // Get license details
  app.get('/api/v1/licenses/:id', authMiddleware(), async (c) => {
    try {
      const admin = c.get('admin');
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的许可证ID', 'INVALID_LICENSE_ID', 400);
      }
      
      const license = await c.env.DB.prepare(`
        SELECT l.*, p.name as product_name, p.verification_strategy
        FROM licenses l
        JOIN products p ON l.product_id = p.id
        WHERE l.id = ?
      `).bind(id).first();
      
      if (!license) {
        return response.error(c, 'License not found', 'LICENSE_NOT_FOUND', 404);
      }
      
      // Check admin permissions
      if (admin.role !== 'super') {
        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);
        if (adminInfo) {
          const { product_ids } = JSON.parse(adminInfo);
          if (!product_ids || !product_ids.includes((license.product_id as number).toString())) {
            return response.error(c, 'Access denied to this license', 'LICENSE_ACCESS_DENIED', 403);
          }
        }
      }
      
      // Get device bindings
      const devices = await c.env.DB.prepare('SELECT * FROM devices WHERE license_id = ?').bind(id).all();
      
      // Get verification logs
      const logs = await c.env.DB.prepare(`
        SELECT * FROM verification_logs 
        WHERE license_key = ? 
        ORDER BY created_at DESC 
        LIMIT 50
      `).bind(license.license_key).all();
      
      return response.success(c, '许可证信息获取成功', {
        license,
        devices: devices.results,
        verification_logs: logs.results,
      });
    } catch (error) {
      console.error('Get license error:', error);
      return response.error(c, 'Internal server error', 'GET_LICENSE_ERROR', 500);
    }
  });

  // Update license
  app.put('/api/v1/licenses/:id', authMiddleware(), async (c) => {
    try {
      const admin = c.get('admin');
      const id = parseInt(c.req.param('id'));
      const updateData = await c.req.json();
      
      if (!id || id <= 0) {
        return response.error(c, '无效的许可证ID', 'INVALID_LICENSE_ID', 400);
      }
      
      // Check if license exists and get product info
      const license = await c.env.DB.prepare(`
        SELECT l.*, p.id as product_id
        FROM licenses l
        JOIN products p ON l.product_id = p.id
        WHERE l.id = ?
      `).bind(id).first();
      
      if (!license) {
        return response.error(c, 'License not found', 'LICENSE_NOT_FOUND', 404);
      }
      
      // Check admin permissions
      if (admin.role !== 'super') {
        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);
        if (adminInfo) {
          const { product_ids } = JSON.parse(adminInfo);
          if (!product_ids || !product_ids.includes((license.product_id as number).toString())) {
            return response.error(c, 'Access denied to this license', 'LICENSE_ACCESS_DENIED', 403);
          }
        }
      }
      
      // Build update query
      const updateFields: string[] = [];
      const params: any[] = [];
      
      if (updateData.status !== undefined) {
        updateFields.push('status = ?');
        params.push(updateData.status);
      }
      if (updateData.expires_at !== undefined) {
        updateFields.push('expires_at = ?');
        params.push(updateData.expires_at);
      }
      if (updateData.max_devices !== undefined) {
        updateFields.push('max_devices = ?');
        params.push(updateData.max_devices);
      }
      
      updateFields.push('updated_at = datetime(\'now\')');
      params.push(id);
      
      const query = `UPDATE licenses SET ${updateFields.join(', ')} WHERE id = ?`;
      await c.env.DB.prepare(query).bind(...params).run();
      
      // Clear cache
      const cacheKey = `license:verify:${license.license_key}`;
      await c.env.CACHE.delete(cacheKey);
      
      return response.success(c, '许可证更新成功');
    } catch (error) {
      console.error('Update license error:', error);
      return response.error(c, 'Internal server error', 'UPDATE_LICENSE_ERROR', 500);
    }
  });
}