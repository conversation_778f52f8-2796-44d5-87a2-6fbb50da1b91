import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { JWTService } from '../utils/auth';
import * as response from '../utils/response';
import * as bcrypt from 'bcryptjs';

export function setupAuthRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // Admin login endpoint
  app.post('/api/auth/login', async (c) => {
    try {
      const body = await c.req.json();
      const { username, password } = body;

      if (!username || !password) {
        return response.error(c, '用户名和密码不能为空', 'MISSING_CREDENTIALS', 400);
      }

      // Get admin by username
      const admin = await c.env.DB.prepare(`
        SELECT id, username, password_hash, role, status, product_ids
        FROM admins
        WHERE username = ? AND status = 'active'
      `).bind(username).first();

      if (!admin) {
        return response.error(c, '用户名或密码错误', 'INVALID_CREDENTIALS', 401);
      }

      // Verify password using bcrypt
      const isPasswordValid = await bcrypt.compare(password, admin.password_hash as string);

      if (!isPasswordValid) {
        return response.error(c, '用户名或密码错误', 'INVALID_CREDENTIALS', 401);
      }

      const jwtService = new JWTService(c.env.JWT_SECRET);

      // Generate tokens
      const tokenPayload = {
        admin_id: admin.id as number,
        username: admin.username as string,
        role: admin.role as string,
      };

      const token = await jwtService.generateToken(tokenPayload);
      const refreshToken = await jwtService.generateRefreshToken(tokenPayload);

      // Parse product_ids
      let productIds: string[] = [];
      if (admin.product_ids) {
        try {
          productIds = JSON.parse(admin.product_ids as string);
        } catch {
          productIds = (admin.product_ids as string).split(',').map((id: string) => id.trim());
        }
      }

      // Cache admin info
      const cacheKey = `admin:auth:${admin.id}`;
      await c.env.CACHE.put(cacheKey, JSON.stringify({
        id: admin.id,
        username: admin.username,
        role: admin.role,
        product_ids: productIds,
      }), { expirationTtl: 3600 }); // 1 hour

      return response.success(c, '登录成功', {
        token,
        admin_id: admin.id as number,
        username: admin.username as string,
        role: admin.role as string,
        authorized_products: productIds,
        expires_in: 86400,
      });

    } catch (error) {
      console.error('Admin login error:', error);
      return response.error(c, '服务器内部错误', 'LOGIN_ERROR', 500);
    }
  });

  // Token refresh endpoint
  app.post('/api/auth/refresh', async (c) => {
    try {
      const authHeader = c.req.header('Authorization');
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return response.error(c, '缺少有效的刷新token', 'MISSING_TOKEN', 401);
      }

      const refresh_token = authHeader.substring(7);

      const jwtService = new JWTService(c.env.JWT_SECRET);

      // Verify refresh token
      let payload;
      try {
        payload = await jwtService.verifyToken(refresh_token);
      } catch {
        return response.error(c, '无效或过期的刷新token', 'INVALID_TOKEN', 401);
      }

      // Get fresh admin info
      const admin = await c.env.DB.prepare(`
        SELECT id, username, role, status, product_ids
        FROM admins
        WHERE id = ? AND status = 'active'
      `).bind(payload.admin_id).first();

      if (!admin) {
        return response.error(c, '管理员账号不存在或已禁用', 'ADMIN_NOT_FOUND', 401);
      }

      // Generate new tokens
      const tokenPayload = {
        admin_id: admin.id as number,
        username: admin.username as string,
        role: admin.role as string,
      };

      const token = await jwtService.generateToken(tokenPayload);
      const newRefreshToken = await jwtService.generateRefreshToken(tokenPayload);

      // Parse product_ids
      let productIds: string[] = [];
      if (admin.product_ids) {
        try {
          productIds = JSON.parse(admin.product_ids as string);
        } catch {
          productIds = (admin.product_ids as string).split(',').map((id: string) => id.trim());
        }
      }

      // Update cache
      const cacheKey = `admin:auth:${admin.id}`;
      await c.env.CACHE.put(cacheKey, JSON.stringify({
        id: admin.id,
        username: admin.username,
        role: admin.role,
        product_ids: productIds,
      }), { expirationTtl: 3600 });

      return response.success(c, 'Token刷新成功', {
        token,
        expires_in: 86400,
      });

    } catch (error) {
      console.error('Token refresh error:', error);
      return response.error(c, '服务器内部错误', 'REFRESH_ERROR', 500);
    }
  });

  // Logout endpoint
  app.post('/api/auth/logout', async (c) => {
    try {
      const authHeader = c.req.header('Authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return response.error(c, '需要授权头', 'MISSING_TOKEN', 401);
      }

      const token = authHeader.substring(7);
      const jwtService = new JWTService(c.env.JWT_SECRET);

      try {
        const payload = await jwtService.verifyToken(token);
        
        // Clear cache
        const cacheKey = `admin:auth:${payload.admin_id}`;
        await c.env.CACHE.delete(cacheKey);
        
      } catch {
        // Token is invalid, but we still return success for logout
      }

      return response.success(c, '登出成功');

    } catch (error) {
      console.error('Logout error:', error);
      return response.error(c, '服务器内部错误', 'LOGOUT_ERROR', 500);
    }
  });
}