import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { authMiddleware } from '../middleware/auth';
import * as response from '../utils/response';

export function setupOrderRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // Get orders
  app.get('/api/orders', authMiddleware(), async (c) => {
    try {
      const admin = c.get('admin');
      const query = c.req.query();
      const page = parseInt(query.page || '1');
      const limit = parseInt(query.limit || '20');
      const status = query.status;
      const product_id = query.product_id ? parseInt(query.product_id) : undefined;
      const start_date = query.start_date;
      const end_date = query.end_date;
      const offset = (page - 1) * limit;
      
      let queryStr = `
        SELECT o.*, p.name as product_name, a.username as admin_username
        FROM orders o
        JOIN products p ON o.product_id = p.id
        JOIN admins a ON o.admin_id = a.id
      `;
      const params: any[] = [];
      const conditions: string[] = [];
      
      // Normal admins can only see their own orders
      if (admin.role !== 'super') {
        conditions.push('o.admin_id = ?');
        params.push(admin.admin_id);
      }
      
      if (status) {
        conditions.push('o.status = ?');
        params.push(status);
      }
      
      if (product_id) {
        conditions.push('o.product_id = ?');
        params.push(product_id);
      }
      
      if (start_date) {
        conditions.push('o.created_at >= ?');
        params.push(start_date);
      }
      
      if (end_date) {
        conditions.push('o.created_at <= ?');
        params.push(end_date);
      }
      
      if (conditions.length > 0) {
        queryStr += ' WHERE ' + conditions.join(' AND ');
      }
      
      queryStr += ' ORDER BY o.created_at DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);
      
      const orders = await c.env.DB.prepare(queryStr).bind(...params).all();
      
      // Get total count
      let countQuery = 'SELECT COUNT(*) as total FROM orders o';
      if (conditions.length > 0) {
        countQuery += ' WHERE ' + conditions.join(' AND ');
      }
      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();
      
      return response.success(c, '订单列表获取成功', {
        orders: orders.results,
        pagination: {
          page,
          limit,
          total: (countResult?.total as number) || 0,
          totalPages: Math.ceil(((countResult?.total as number) || 0) / limit),
        },
      });
    } catch (error) {
      console.error('Get orders error:', error);
      return response.error(c, 'Internal server error', 'GET_ORDERS_ERROR', 500);
    }
  });

  // Create order
  app.post('/api/orders', authMiddleware(), async (c) => {
    try {
      const admin = c.get('admin');
      const body = await c.req.json();
      const { product_id, license_count, unit_price } = body;
      
      if (!product_id || !license_count || !unit_price) {
        return response.error(c, '产品ID、许可证数量和单价不能为空', 'MISSING_REQUIRED_FIELDS', 400);
      }
      
      // Check if product exists and admin has access
      const product = await c.env.DB.prepare('SELECT * FROM products WHERE id = ? AND status = \'active\'').bind(product_id).first();
      if (!product) {
        return response.error(c, 'Product not found or inactive', 'PRODUCT_NOT_FOUND', 400);
      }
      
      // Check admin permissions for normal admins
      if (admin.role !== 'super') {
        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);
        if (adminInfo) {
          const { product_ids } = JSON.parse(adminInfo);
          if (!product_ids || !product_ids.includes(product_id.toString())) {
            return response.error(c, 'Access denied to this product', 'PRODUCT_ACCESS_DENIED', 403);
          }
        }
      }
      
      const totalPrice = unit_price * license_count;
      
      const result = await c.env.DB.prepare(`
        INSERT INTO orders (admin_id, product_id, license_count, unit_price, total_price, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, 'pending', datetime('now'), datetime('now'))
      `).bind(admin.admin_id, product_id, license_count, unit_price, totalPrice).run();
      
      return response.success(c, '订单创建成功', { 
        id: result.meta.last_row_id,
        total_price: totalPrice 
      }, 201);
    } catch (error) {
      console.error('Create order error:', error);
      return response.error(c, 'Internal server error', 'CREATE_ORDER_ERROR', 500);
    }
  });

  // Get order by ID
  app.get('/api/orders/:id', authMiddleware(), async (c) => {
    try {
      const admin = c.get('admin');
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的订单ID', 'INVALID_ORDER_ID', 400);
      }
      
      let queryStr = `
        SELECT o.*, p.name as product_name, a.username as admin_username
        FROM orders o
        JOIN products p ON o.product_id = p.id
        JOIN admins a ON o.admin_id = a.id
        WHERE o.id = ?
      `;
      
      // Normal admins can only see their own orders
      if (admin.role !== 'super') {
        queryStr += ' AND o.admin_id = ?';
      }
      
      const params = admin.role === 'super' ? [id] : [id, admin.admin_id];
      const order = await c.env.DB.prepare(queryStr).bind(...params).first();
      
      if (!order) {
        return response.error(c, 'Order not found or access denied', 'ORDER_NOT_FOUND', 404);
      }
      
      return response.success(c, '订单信息获取成功', { order });
    } catch (error) {
      console.error('Get order error:', error);
      return response.error(c, 'Internal server error', 'GET_ORDER_ERROR', 500);
    }
  });

  // Update order status
  app.put('/api/orders/:id', authMiddleware(), async (c) => {
    try {
      const admin = c.get('admin');
      const id = parseInt(c.req.param('id'));
      const body = await c.req.json();
      const { status } = body;
      
      if (!id || id <= 0) {
        return response.error(c, '无效的订单ID', 'INVALID_ORDER_ID', 400);
      }
      
      if (!status) {
        return response.error(c, '状态不能为空', 'MISSING_STATUS', 400);
      }
      
      // Check if order exists and admin has access
      let checkQuery = 'SELECT * FROM orders WHERE id = ?';
      if (admin.role !== 'super') {
        checkQuery += ' AND admin_id = ?';
      }
      
      const params = admin.role === 'super' ? [id] : [id, admin.admin_id];
      const order = await c.env.DB.prepare(checkQuery).bind(...params).first();
      
      if (!order) {
        return response.error(c, 'Order not found or access denied', 'ORDER_NOT_FOUND', 404);
      }
      
      await c.env.DB.prepare(`
        UPDATE orders SET status = ?, updated_at = datetime('now') WHERE id = ?
      `).bind(status, id).run();
      
      return response.success(c, '订单状态更新成功');
    } catch (error) {
      console.error('Update order error:', error);
      return response.error(c, 'Internal server error', 'UPDATE_ORDER_ERROR', 500);
    }
  });

  // Delete order
  app.delete('/api/orders/:id', authMiddleware(), async (c) => {
    try {
      const admin = c.get('admin');
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的订单ID', 'INVALID_ORDER_ID', 400);
      }
      
      // Check if order exists and admin has access
      let checkQuery = 'SELECT * FROM orders WHERE id = ?';
      if (admin.role !== 'super') {
        checkQuery += ' AND admin_id = ?';
      }
      
      const params = admin.role === 'super' ? [id] : [id, admin.admin_id];
      const order = await c.env.DB.prepare(checkQuery).bind(...params).first();
      
      if (!order) {
        return response.error(c, 'Order not found or access denied', 'ORDER_NOT_FOUND', 404);
      }
      
      // Only allow deletion of pending orders
      if (order.status !== 'pending') {
        return response.error(c, 'Cannot delete completed or cancelled order', 'ORDER_NOT_DELETABLE', 409);
      }
      
      await c.env.DB.prepare('DELETE FROM orders WHERE id = ?').bind(id).run();
      
      return response.success(c, '订单删除成功');
    } catch (error) {
      console.error('Delete order error:', error);
      return response.error(c, 'Internal server error', 'DELETE_ORDER_ERROR', 500);
    }
  });
}