import { <PERSON>o } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { authMiddleware, superAdminOnly } from '../middleware/auth';
import * as bcrypt from 'bcryptjs';
import * as response from '../utils/response';

export function setupAdminRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // Apply middleware for all admin management routes
  app.use('/api/admins', authMiddleware(), superAdminOnly());
  app.use('/api/admins/*', authMiddleware(), superAdminOnly());

  // Get all admins (super admin only)
  app.get('/api/admins', async (c) => {
    try {
      const query = c.req.query();
      const page = parseInt(query.page || '1');
      const limit = parseInt(query.limit || '20');
      const role = query.role;
      const status = query.status;
      const search = query.search;
      const offset = (page - 1) * limit;
      
      let queryStr = 'SELECT id, username, role, status, product_ids, created_at, updated_at FROM admins';
      const params: any[] = [];
      const conditions: string[] = [];
      
      if (role) {
        conditions.push('role = ?');
        params.push(role);
      }
      
      if (status) {
        conditions.push('status = ?');
        params.push(status);
      }
      
      if (search) {
        conditions.push('username LIKE ?');
        params.push(`%${search}%`);
      }
      
      if (conditions.length > 0) {
        queryStr += ' WHERE ' + conditions.join(' AND ');
      }
      
      queryStr += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);
      
      const admins = await c.env.DB.prepare(queryStr).bind(...params).all();
      
      // Get total count
      let countQuery = 'SELECT COUNT(*) as total FROM admins';
      if (conditions.length > 0) {
        countQuery += ' WHERE ' + conditions.join(' AND ');
      }
      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();
      
      return response.success(c, '管理员列表获取成功', {
        admins: admins.results,
        total: (countResult?.total as number) || 0,
        page,
        limit,
        totalPages: Math.ceil(((countResult?.total as number) || 0) / limit),
      });
    } catch (error) {
      console.error('Get admins error:', error);
      return response.error(c, '服务器内部错误', 'GET_ADMINS_ERROR', 500);
    }
  });

  // Create admin (super admin only)
  app.post('/api/admins', async (c) => {
    try {
      const body = await c.req.json();
      const { username, password, role, product_ids } = body;
      
      if (!username || !password || !role) {
        return response.error(c, '用户名、密码和角色不能为空', 'MISSING_REQUIRED_FIELDS', 400);
      }
      
      // Check if username already exists
      const existing = await c.env.DB.prepare('SELECT id FROM admins WHERE username = ?').bind(username).first();
      if (existing) {
        return response.error(c, '用户名已存在', 'DUPLICATE_USERNAME', 400);
      }
      
      // Validate product_ids if provided
      if (product_ids && product_ids.length > 0) {
        const productCheck = await c.env.DB.prepare(`
          SELECT COUNT(*) as count FROM products WHERE id IN (${product_ids.map(() => '?').join(',')})
        `).bind(...product_ids).first();
        
        if ((productCheck?.count as number) !== product_ids.length) {
          return response.error(c, '一个或多个产品ID无效', 'INVALID_PRODUCT_IDS', 400);
        }
      }
      
      // Hash password
      const passwordHash = await bcrypt.hash(password, 10);
      const productIdsJson = product_ids ? JSON.stringify(product_ids) : null;
      
      const result = await c.env.DB.prepare(`
        INSERT INTO admins (username, password_hash, role, status, product_ids, created_at, updated_at)
        VALUES (?, ?, ?, 'active', ?, datetime('now'), datetime('now'))
      `).bind(username, passwordHash, role, productIdsJson).run();
      
      return response.success(c, '管理员创建成功', { id: result.meta.last_row_id });
    } catch (error) {
      console.error('Create admin error:', error);
      return response.error(c, '服务器内部错误', 'CREATE_ADMIN_ERROR', 500);
    }
  });

  // Get admin by ID (super admin only)
  app.get('/api/admins/:id', async (c) => {
    try {
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的管理员ID', 'INVALID_ADMIN_ID', 400);
      }
      
      const admin = await c.env.DB.prepare(`
        SELECT id, username, role, status, product_ids, created_at, updated_at 
        FROM admins 
        WHERE id = ?
      `).bind(id).first();
      
      if (!admin) {
        return response.error(c, '管理员不存在', 'ADMIN_NOT_FOUND', 404);
      }
      
      // Parse product_ids
      let assignedProducts = [];
      if (admin.product_ids) {
        try {
          const productIds = JSON.parse(admin.product_ids as string);
          const products = await c.env.DB.prepare(`
            SELECT id, name FROM products WHERE id IN (${productIds.map(() => '?').join(',')})
          `).bind(...productIds).all();
          assignedProducts = products.results;
        } catch (e) {
          // Handle legacy comma-separated format
          const productIds = (admin.product_ids as string).split(',').map(id => parseInt(id.trim()));
          const products = await c.env.DB.prepare(`
            SELECT id, name FROM products WHERE id IN (${productIds.map(() => '?').join(',')})
          `).bind(...productIds).all();
          assignedProducts = products.results;
        }
      }
      
      return response.success(c, '管理员信息获取成功', {
        admin: {
          ...admin,
          assigned_products: assignedProducts,
        },
      });
    } catch (error) {
      console.error('Get admin error:', error);
      return response.error(c, '服务器内部错误', 'GET_ADMIN_ERROR', 500);
    }
  });

  // Update admin (super admin only)
  app.put('/api/admins/:id', async (c) => {
    try {
      const id = parseInt(c.req.param('id'));
      const updateData = await c.req.json();
      
      if (!id || id <= 0) {
        return response.error(c, '无效的管理员ID', 'INVALID_ADMIN_ID', 400);
      }
      
      // Check if admin exists
      const existing = await c.env.DB.prepare('SELECT id FROM admins WHERE id = ?').bind(id).first();
      if (!existing) {
        return response.error(c, '管理员不存在', 'ADMIN_NOT_FOUND', 404);
      }
      
      // Validate product_ids if provided
      if (updateData.product_ids && updateData.product_ids.length > 0) {
        const productCheck = await c.env.DB.prepare(`
          SELECT COUNT(*) as count FROM products WHERE id IN (${updateData.product_ids.map(() => '?').join(',')})
        `).bind(...updateData.product_ids).first();
        
        if ((productCheck?.count as number) !== updateData.product_ids.length) {
          return response.error(c, '一个或多个产品ID无效', 'INVALID_PRODUCT_IDS', 400);
        }
      }
      
      // Build update query
      const updateFields: string[] = [];
      const params: any[] = [];
      
      if (updateData.password !== undefined) {
        const passwordHash = await bcrypt.hash(updateData.password, 10);
        updateFields.push('password_hash = ?');
        params.push(passwordHash);
      }
      if (updateData.role !== undefined) {
        updateFields.push('role = ?');
        params.push(updateData.role);
      }
      if (updateData.product_ids !== undefined) {
        updateFields.push('product_ids = ?');
        params.push(updateData.product_ids ? JSON.stringify(updateData.product_ids) : null);
      }
      if (updateData.status !== undefined) {
        updateFields.push('status = ?');
        params.push(updateData.status);
      }
      
      updateFields.push('updated_at = datetime(\'now\')');
      params.push(id);
      
      const query = `UPDATE admins SET ${updateFields.join(', ')} WHERE id = ?`;
      await c.env.DB.prepare(query).bind(...params).run();
      
      // Clear cache for this admin
      const cacheKey = `admin:auth:${id}`;
      await c.env.CACHE.delete(cacheKey);
      
      return response.success(c, '管理员更新成功');
    } catch (error) {
      console.error('Update admin error:', error);
      return response.error(c, '服务器内部错误', 'UPDATE_ADMIN_ERROR', 500);
    }
  });

  // Delete admin (super admin only)
  app.delete('/api/admins/:id', async (c) => {
    try {
      const currentAdmin = c.get('admin');
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的管理员ID', 'INVALID_ADMIN_ID', 400);
      }
      
      // Prevent self-deletion
      if (currentAdmin.admin_id === id) {
        return response.error(c, '不能删除自己的账号', 'CANNOT_DELETE_SELF', 400);
      }
      
      // Check if admin exists
      const existing = await c.env.DB.prepare('SELECT id FROM admins WHERE id = ?').bind(id).first();
      if (!existing) {
        return response.error(c, '管理员不存在', 'ADMIN_NOT_FOUND', 404);
      }
      
      // Check if admin has active licenses
      const activeLicenses = await c.env.DB.prepare('SELECT COUNT(*) as count FROM licenses WHERE admin_id = ? AND status = \'active\'').bind(id).first();
      if (activeLicenses && (activeLicenses.count as number) > 0) {
        return response.error(c, '不能删除拥有活跃许可证的管理员', 'ADMIN_HAS_ACTIVE_LICENSES', 409);
      }
      
      await c.env.DB.prepare('DELETE FROM admins WHERE id = ?').bind(id).run();
      
      // Clear cache
      const cacheKey = `admin:auth:${id}`;
      await c.env.CACHE.delete(cacheKey);
      
      return response.success(c, '管理员删除成功');
    } catch (error) {
      console.error('Delete admin error:', error);
      return response.error(c, '服务器内部错误', 'DELETE_ADMIN_ERROR', 500);
    }
  });
}