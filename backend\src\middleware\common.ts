import { Context, Next } from 'hono';
import { cors } from 'hono/cors';
import { CloudflareBindings } from '../types/interfaces';
import * as response from '../utils/response';

export function corsMiddleware() {
  return cors({
    origin: '*',
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
  });
}

export function bindingsMiddleware() {
  return async (c: Context<{ Bindings: CloudflareBindings }>, next: Next) => {
    if (!c.env.DB) {
      return response.error(c, '数据库不可用', 'DATABASE_UNAVAILABLE', 500);
    }
    if (!c.env.CACHE) {
      return response.error(c, '缓存不可用', 'CACHE_UNAVAILABLE', 500);
    }
    await next();
  };
}