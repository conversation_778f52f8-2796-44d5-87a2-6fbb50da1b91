import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { authMiddleware, superAdminOnly } from '../middleware/auth';
import * as response from '../utils/response';

export function setupProductRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // Get all products
  app.get('/api/products', authMiddleware(), async (c) => {
    try {
      const query = c.req.query();
      const page = parseInt(query.page || '1');
      const limit = parseInt(query.limit || '20');
      const status = query.status;
      const search = query.search;
      const offset = (page - 1) * limit;
      
      let queryStr = 'SELECT * FROM products';
      const params: any[] = [];
      const conditions: string[] = [];
      
      if (status) {
        conditions.push('status = ?');
        params.push(status);
      }
      
      if (search) {
        conditions.push('(name LIKE ? OR description LIKE ?)');
        params.push(`%${search}%`, `%${search}%`);
      }
      
      if (conditions.length > 0) {
        queryStr += ' WHERE ' + conditions.join(' AND ');
      }
      
      queryStr += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);
      
      const products = await c.env.DB.prepare(queryStr).bind(...params).all();
      
      // Get total count
      let countQuery = 'SELECT COUNT(*) as total FROM products';
      if (conditions.length > 0) {
        countQuery += ' WHERE ' + conditions.join(' AND ');
      }
      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();
      
      return response.success(c, '产品列表获取成功', {
        products: products.results,
        pagination: {
          page,
          limit,
          total: (countResult?.total as number) || 0,
          totalPages: Math.ceil(((countResult?.total as number) || 0) / limit),
        },
      });
    } catch (error) {
      console.error('Get products error:', error);
      return response.error(c, '服务器内部错误', 'GET_PRODUCTS_ERROR', 500);
    }
  });

  // Create product
  app.post('/api/products', authMiddleware(), superAdminOnly(), async (c) => {
    try {
      const body = await c.req.json();
      const { name, description, verification_strategy, max_devices, features } = body;
      
      if (!name || !verification_strategy) {
        return response.error(c, '产品名称和验证策略不能为空', 'MISSING_REQUIRED_FIELDS', 400);
      }
      
      // Check if product name already exists
      const existing = await c.env.DB.prepare('SELECT id FROM products WHERE name = ?').bind(name).first();
      if (existing) {
        return response.error(c, '产品名称已存在', 'DUPLICATE_PRODUCT_NAME', 400);
      }
      
      const featuresJson = features ? JSON.stringify(features) : null;
      
      const result = await c.env.DB.prepare(`
        INSERT INTO products (name, description, verification_strategy, max_devices, features, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, 'active', datetime('now'), datetime('now'))
      `).bind(name, description || null, verification_strategy, max_devices || null, featuresJson).run();
      
      return response.success(c, '产品创建成功', { id: result.meta.last_row_id });
    } catch (error) {
      console.error('Create product error:', error);
      return response.error(c, '服务器内部错误', 'CREATE_PRODUCT_ERROR', 500);
    }
  });

  // Get product by ID
  app.get('/api/products/:id', authMiddleware(), async (c) => {
    try {
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的产品ID', 'INVALID_PRODUCT_ID', 400);
      }
      
      const product = await c.env.DB.prepare('SELECT * FROM products WHERE id = ?').bind(id).first();
      
      if (!product) {
        return response.error(c, '产品不存在', 'PRODUCT_NOT_FOUND', 404);
      }
      
      return response.success(c, '产品信息获取成功', { product });
    } catch (error) {
      console.error('Get product error:', error);
      return response.error(c, '服务器内部错误', 'GET_PRODUCT_ERROR', 500);
    }
  });

  // Update product
  app.put('/api/products/:id', authMiddleware(), superAdminOnly(), async (c) => {
    try {
      const id = parseInt(c.req.param('id'));
      const updateData = await c.req.json();
      
      if (!id || id <= 0) {
        return response.error(c, '无效的产品ID', 'INVALID_PRODUCT_ID', 400);
      }
      
      // Check if product exists
      const existing = await c.env.DB.prepare('SELECT id FROM products WHERE id = ?').bind(id).first();
      if (!existing) {
        return response.error(c, '产品不存在', 'PRODUCT_NOT_FOUND', 404);
      }
      
      // Build update query
      const updateFields: string[] = [];
      const params: any[] = [];
      
      if (updateData.name !== undefined) {
        updateFields.push('name = ?');
        params.push(updateData.name);
      }
      if (updateData.description !== undefined) {
        updateFields.push('description = ?');
        params.push(updateData.description);
      }
      if (updateData.verification_strategy !== undefined) {
        updateFields.push('verification_strategy = ?');
        params.push(updateData.verification_strategy);
      }
      if (updateData.max_devices !== undefined) {
        updateFields.push('max_devices = ?');
        params.push(updateData.max_devices);
      }
      if (updateData.features !== undefined) {
        updateFields.push('features = ?');
        params.push(updateData.features ? JSON.stringify(updateData.features) : null);
      }
      if (updateData.status !== undefined) {
        updateFields.push('status = ?');
        params.push(updateData.status);
      }
      
      updateFields.push('updated_at = datetime(\'now\')');
      params.push(id);
      
      const query = `UPDATE products SET ${updateFields.join(', ')} WHERE id = ?`;
      await c.env.DB.prepare(query).bind(...params).run();
      
      return response.success(c, '产品更新成功');
    } catch (error) {
      console.error('Update product error:', error);
      return response.error(c, '服务器内部错误', 'UPDATE_PRODUCT_ERROR', 500);
    }
  });

  // Delete product
  app.delete('/api/products/:id', authMiddleware(), superAdminOnly(), async (c) => {
    try {
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的产品ID', 'INVALID_PRODUCT_ID', 400);
      }
      
      // Check if product exists
      const existing = await c.env.DB.prepare('SELECT id FROM products WHERE id = ?').bind(id).first();
      if (!existing) {
        return response.error(c, '产品不存在', 'PRODUCT_NOT_FOUND', 404);
      }
      
      // Check if product has active licenses
      const activeLicenses = await c.env.DB.prepare('SELECT COUNT(*) as count FROM licenses WHERE product_id = ? AND status = \'active\'').bind(id).first();
      if (activeLicenses && (activeLicenses.count as number) > 0) {
        return response.error(c, '不能删除拥有活跃许可证的产品', 'PRODUCT_HAS_ACTIVE_LICENSES', 409);
      }
      
      await c.env.DB.prepare('DELETE FROM products WHERE id = ?').bind(id).run();
      
      return response.success(c, '产品删除成功');
    } catch (error) {
      console.error('Delete product error:', error);
      return response.error(c, '服务器内部错误', 'DELETE_PRODUCT_ERROR', 500);
    }
  });
}