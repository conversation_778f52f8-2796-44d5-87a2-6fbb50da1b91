import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { initializeDatabase } from '../utils/init-db';
import * as response from '../utils/response';

export function setupInitRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // 数据库初始化端点（仅在开发环境使用）
  app.post('/init-db', async (c) => {
    try {
      // 检查是否已有超级管理员，如果有则拒绝重复初始化
      const existingSuperAdmin = await c.env.DB.prepare(
        'SELECT id FROM admins WHERE role = ? LIMIT 1'
      ).bind('super').first();

      if (existingSuperAdmin) {
        return response.error(c, '数据库已初始化，无需重复操作', 'DATABASE_ALREADY_INITIALIZED', 400);
      }

      // 执行数据库初始化
      await initializeDatabase(c.env);

      return response.success(c, '数据库初始化成功', {
        message: '管理员账户和示例数据已创建',
        accounts: [
          { username: 'root', password: 'password', role: 'super' },
          { username: 'admin', password: 'password', role: 'normal' }
        ]
      });

    } catch (error) {
      console.error('Database initialization error:', error);
      return response.error(c, '数据库初始化失败', 'INIT_DATABASE_ERROR', 500);
    }
  });

  // 重置数据库端点（危险操作，仅开发环境使用）
  app.post('/reset-db', async (c) => {
    try {
      // 删除所有数据
      await c.env.DB.prepare('DELETE FROM verification_logs').run();
      await c.env.DB.prepare('DELETE FROM devices').run();
      await c.env.DB.prepare('DELETE FROM orders').run();
      await c.env.DB.prepare('DELETE FROM licenses').run();
      await c.env.DB.prepare('DELETE FROM admins').run();
      await c.env.DB.prepare('DELETE FROM products').run();

      // 重新初始化
      await initializeDatabase(c.env);

      return response.success(c, '数据库重置成功', {
        message: '所有数据已清除并重新初始化'
      });

    } catch (error) {
      console.error('Database reset error:', error);
      return response.error(c, '数据库重置失败', 'RESET_DATABASE_ERROR', 500);
    }
  });

  // 数据库状态检查端点
  app.get('/db-status', async (c) => {
    try {
      const adminCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM admins').first();
      const productCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM products').first();
      const licenseCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM licenses').first();

      return response.success(c, '数据库状态查询成功', {
        admins: (adminCount?.count as number) || 0,
        products: (productCount?.count as number) || 0,
        licenses: (licenseCount?.count as number) || 0,
        initialized: ((adminCount?.count as number) || 0) > 0
      });

    } catch (error) {
      console.error('Database status check error:', error);
      return response.error(c, '数据库状态查询失败', 'DB_STATUS_ERROR', 500);
    }
  });
}