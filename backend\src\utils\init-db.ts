/**
 * 数据库初始化脚本 - 插入管理员账户
 * 运行命令：pnpm run init-db
 */

import * as bcrypt from 'bcryptjs';

interface CloudflareBindings {
  DB: D1Database;
  CACHE: KVNamespace;
  JWT_SECRET: string;
  API_VERSION: string;
  ALLOWED_ORIGINS: string;
}

/**
 * 初始化管理员账户
 */
async function initAdminAccounts(DB: D1Database) {
  console.log('正在初始化管理员账户...');

  try {
    // 检查超级管理员是否已存在
    const existingSuperAdmin = await DB.prepare(
      'SELECT id FROM admins WHERE username = ?'
    ).bind('root').first();

    if (!existingSuperAdmin) {
      // 创建超级管理员账户
      const superAdminPasswordHash = await bcrypt.hash('password', 10);
      
      await DB.prepare(`
        INSERT INTO admins (username, password_hash, role, status, product_ids, created_at, updated_at)
        VALUES (?, ?, 'super', 'active', NULL, datetime('now'), datetime('now'))
      `).bind('root', superAdminPasswordHash).run();
      
      console.log('✅ 超级管理员账户创建成功 (用户名: root, 密码: password)');
    } else {
      console.log('ℹ️ 超级管理员账户已存在，跳过创建');
    }

    // 检查普通管理员是否已存在
    const existingNormalAdmin = await DB.prepare(
      'SELECT id FROM admins WHERE username = ?'
    ).bind('admin').first();

    if (!existingNormalAdmin) {
      // 创建普通管理员账户
      const normalAdminPasswordHash = await bcrypt.hash('password', 10);
      const assignedProducts = JSON.stringify([1, 2]); // 分配产品1和2
      
      await DB.prepare(`
        INSERT INTO admins (username, password_hash, role, status, product_ids, created_at, updated_at)
        VALUES (?, ?, 'normal', 'active', ?, datetime('now'), datetime('now'))
      `).bind('admin', normalAdminPasswordHash, assignedProducts).run();
      
      console.log('✅ 普通管理员账户创建成功 (用户名: admin, 密码: password, 分配产品: 1,2)');
    } else {
      console.log('ℹ️ 普通管理员账户已存在，跳过创建');
    }

  } catch (error) {
    console.error('❌ 创建管理员账户失败:', error);
    throw error;
  }
}

/**
 * 插入示例许可证数据
 */
async function insertSampleLicenses(DB: D1Database) {
  console.log('正在插入示例许可证数据...');

  try {
    // 获取管理员ID
    const superAdmin = await DB.prepare('SELECT id FROM admins WHERE username = ?').bind('root').first();
    const normalAdmin = await DB.prepare('SELECT id FROM admins WHERE username = ?').bind('admin').first();

    if (!superAdmin || !normalAdmin) {
      throw new Error('管理员账户不存在，请先运行管理员初始化');
    }

    // 生成示例许可证
    const sampleLicenses = [
      {
        product_id: 1,
        license_key: 'BASIC-DEMO-001-ABCD-EFGH',
        expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1年后过期
        admin_id: superAdmin.id
      },
      {
        product_id: 2,
        license_key: 'PRO-DEMO-002-IJKL-MNOP',
        expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
        admin_id: normalAdmin.id
      },
      {
        product_id: 3,
        license_key: 'ENT-DEMO-003-QRST-UVWX',
        expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
        admin_id: superAdmin.id
      }
    ];

    for (const license of sampleLicenses) {
      // 检查许可证是否已存在
      const existing = await DB.prepare('SELECT id FROM licenses WHERE license_key = ?')
        .bind(license.license_key).first();

      if (!existing) {
        await DB.prepare(`
          INSERT INTO licenses (product_id, license_key, status, expires_at, admin_id, created_at, updated_at)
          VALUES (?, ?, 'active', ?, ?, datetime('now'), datetime('now'))
        `).bind(
          license.product_id,
          license.license_key,
          license.expires_at,
          license.admin_id
        ).run();

        console.log(`✅ 示例许可证创建成功: ${license.license_key}`);
      } else {
        console.log(`ℹ️ 许可证已存在，跳过创建: ${license.license_key}`);
      }
    }

  } catch (error) {
    console.error('❌ 插入示例许可证失败:', error);
    throw error;
  }
}

/**
 * 插入示例订单数据
 */
async function insertSampleOrders(DB: D1Database) {
  console.log('正在插入示例订单数据...');

  try {
    const normalAdmin = await DB.prepare('SELECT id FROM admins WHERE username = ?').bind('admin').first();
    
    if (!normalAdmin) {
      throw new Error('普通管理员账户不存在');
    }

    const sampleOrders = [
      {
        admin_id: normalAdmin.id,
        product_id: 1,
        license_count: 10,
        unit_price: 99.00,
        total_price: 990.00,
        status: 'completed'
      },
      {
        admin_id: normalAdmin.id,
        product_id: 2,
        license_count: 5,
        unit_price: 299.00,
        total_price: 1495.00,
        status: 'completed'
      }
    ];

    for (const order of sampleOrders) {
      await DB.prepare(`
        INSERT INTO orders (admin_id, product_id, license_count, unit_price, total_price, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
      `).bind(
        order.admin_id,
        order.product_id,
        order.license_count,
        order.unit_price,
        order.total_price,
        order.status
      ).run();
    }

    console.log('✅ 示例订单数据插入成功');

  } catch (error) {
    console.error('❌ 插入示例订单失败:', error);
    throw error;
  }
}

/**
 * 主初始化函数
 */
export async function initializeDatabase(env: CloudflareBindings) {
  console.log('🚀 开始初始化数据库...');
  
  try {
    // 1. 初始化管理员账户
    await initAdminAccounts(env.DB);
    
    // 2. 插入示例许可证
    await insertSampleLicenses(env.DB);
    
    // 3. 插入示例订单
    await insertSampleOrders(env.DB);
    
    console.log('🎉 数据库初始化完成！');
    console.log('');
    console.log('账户信息：');
    console.log('超级管理员 - 用户名: root, 密码: password');
    console.log('普通管理员 - 用户名: admin, 密码: password');
    console.log('');
    
  } catch (error) {
    console.error('💥 数据库初始化失败:', error);
    throw error;
  }
}

